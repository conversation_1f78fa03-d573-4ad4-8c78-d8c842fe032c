import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border-2 px-3 py-1 text-xs font-bold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-md",
  {
    variants: {
      variant: {
        default:
          "border-primary/30 bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg",
        secondary:
          "border-secondary/50 bg-secondary text-secondary-foreground hover:bg-secondary/90 shadow-md",
        destructive:
          "border-destructive/30 bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-lg",
        outline: "text-foreground border-border bg-background hover:bg-accent",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
