import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Zap,
  TrendingUp,
  Volume2,
  Clock,
  Target,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3,
  Play,
  Pause,
  Activity,
  Settings,
  RefreshCw
} from 'lucide-react';
import { SpikeSignali } from '@/types/turkish-trading';
import { SpikeAnalysisSettings, SpikeAnalysisSettings as SpikeAnalysisSettingsType } from './SpikeAnalysisSettings';
import { io } from 'socket.io-client';

// Real-time MA10 data interface
interface RealTimeMA10Data {
  symbol: string;
  currentPrice: number;
  ma10: number;
  timestamp: number;
  priceChange: number; // Difference from signal price
  ma10Change: number;  // Difference from signal MA10
}

interface SpikeSignalListProps {
  signals: SpikeSignali[];
  onExecuteSignal?: (signal: SpikeSignali) => void;
  onStartAnalysis?: () => void;
  onStopAnalysis?: () => void;
  isAnalyzing?: boolean;
  onSettingsChange?: (settings: SpikeAnalysisSettingsType) => void;
  onSettingsSave?: () => void;
  onSettingsReset?: () => void;
  settings?: SpikeAnalysisSettingsType;
  onSignalClick?: (signal: SpikeSignali) => void;
  showSettings?: boolean; // Ana panelde ayarları gizlemek için
}

export const SpikeSignalList: React.FC<SpikeSignalListProps> = ({
  signals,
  onExecuteSignal,
  onStartAnalysis,
  onStopAnalysis,
  isAnalyzing = false,
  onSettingsChange,
  onSettingsSave,
  onSettingsReset,
  settings,
  onSignalClick,
  showSettings = true
}) => {
  const [selectedSignal, setSelectedSignal] = useState<string | null>(null);
  const [filter, setFilter] = useState<'ALL' | 'HIGH' | 'CONFIRMED_SPIKE'>('ALL');
  const [analysisStartTime, setAnalysisStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState<number>(0);

  // Real-time MA10 data state
  const [realTimeMA10, setRealTimeMA10] = useState<Map<string, RealTimeMA10Data>>(new Map());

  // WebSocket connection for real-time MA10 updates
  useEffect(() => {
    const socket = io(window.location.origin);

    // Listen for kline updates to get real-time MA10 data
    socket.on('kline-update', (data: any) => {
      if (data && data.symbol && data.close && data.ma10) {
        const symbolData: RealTimeMA10Data = {
          symbol: data.symbol,
          currentPrice: parseFloat(data.close),
          ma10: parseFloat(data.ma10),
          timestamp: Date.now(),
          priceChange: 0, // Will be calculated when displaying
          ma10Change: 0   // Will be calculated when displaying
        };

        setRealTimeMA10(prev => {
          const newMap = new Map(prev);
          newMap.set(data.symbol, symbolData);
          return newMap;
        });
      }
    });

    return () => {
      socket.disconnect();
    };
  }, []);

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatPrice = (price: number): string => {
    if (price === undefined || price === null || isNaN(price)) {
      return '0.00';
    }
    return price < 1 ? price.toFixed(4) : price.toFixed(2);
  };

  const formatElapsedTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get real-time MA10 data for a signal
  const getRealTimeMA10 = (signal: SpikeSignali) => {
    const rtData = realTimeMA10.get(signal.sembol);
    if (!rtData) return null;

    const priceChange = rtData.currentPrice - signal.fiyat;
    const ma10Change = rtData.ma10 - signal.ma10;
    const priceChangePercent = ((priceChange / signal.fiyat) * 100);
    const ma10ChangePercent = ((ma10Change / signal.ma10) * 100);

    return {
      ...rtData,
      priceChange,
      ma10Change,
      priceChangePercent,
      ma10ChangePercent
    };
  };

  // Timer effect for elapsed time
  React.useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isAnalyzing && analysisStartTime) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - analysisStartTime) / 1000));
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isAnalyzing, analysisStartTime]);

  // Handle analysis start/stop
  const handleStartAnalysis = () => {
    setAnalysisStartTime(Date.now());
    setElapsedTime(0);
    if (onStartAnalysis) onStartAnalysis();
  };

  const handleStopAnalysis = () => {
    setAnalysisStartTime(null);
    setElapsedTime(0);
    if (onStopAnalysis) onStopAnalysis();
  };

  const handleDebugTest = async () => {
    try {
      const response = await fetch('/api/debug/spike-test');
      const data = await response.json();
      console.log('🔍 Debug Test Result:', data);
      alert(`Debug Test:\nTotal Symbols: ${data.totalSymbols}\nMarket Data Size: ${data.marketDataSize}\nTest Results: ${JSON.stringify(data.testResults, null, 2)}`);
    } catch (error) {
      console.error('Debug test error:', error);
      alert('Debug test failed: ' + (error as Error).message);
    }
  };

  const getConfidenceBadge = (confidence: SpikeSignali['guvenilirlik']) => {
    switch (confidence) {
      case 'ONAYLANMIS_SPIKE':
        return (
          <Badge className="bg-spike-alert text-spike-alert-foreground animate-spike-alert">
            <Zap className="h-3 w-3 mr-1" />
            ONAYLANMIŞ
          </Badge>
        );
      case 'YUKSEK':
        return (
          <Badge variant="default" className="bg-primary">
            <TrendingUp className="h-3 w-3 mr-1" />
            YÜKSEK
          </Badge>
        );
      case 'ORTA':
        return (
          <Badge variant="secondary">
            <AlertCircle className="h-3 w-3 mr-1" />
            ORTA
          </Badge>
        );
      case 'DUSUK':
        return (
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            DÜŞÜK
          </Badge>
        );
    }
  };

  const getStatusIcon = (status: SpikeSignali['durum']) => {
    switch (status) {
      case 'TESPIT_EDILDI':
        return <AlertCircle className="h-4 w-4 text-primary" />;
      case 'TICARET_YAPIYOR':
        return <Target className="h-4 w-4 text-spike-alert animate-pulse" />;
      case 'TAMAMLANDI':
        return <CheckCircle className="h-4 w-4 text-profit" />;
      case 'BASARISIZ':
        return <XCircle className="h-4 w-4 text-loss" />;
    }
  };

  const getStatusColor = (status: SpikeSignali['durum']): string => {
    switch (status) {
      case 'TESPIT_EDILDI':
        return 'text-primary';
      case 'TICARET_YAPIYOR':
        return 'text-spike-alert';
      case 'TAMAMLANDI':
        return 'text-profit';
      case 'BASARISIZ':
        return 'text-loss';
    }
  };

  const filteredSignals = signals.filter(signal => {
    if (filter === 'ALL') return true;
    if (filter === 'HIGH') return signal.guvenilirlik === 'YUKSEK';
    if (filter === 'CONFIRMED_SPIKE') return signal.guvenilirlik === 'ONAYLANMIS_SPIKE';
    return true;
  });

  const confirmedSpikes = signals.filter(s => s.guvenilirlik === 'ONAYLANMIS_SPIKE').length;
  const highConfidence = signals.filter(s => s.guvenilirlik === 'YUKSEK').length;
  const completedTrades = signals.filter(s => s.durum === 'TAMAMLANDI').length;

  return (
    <Card className="w-full bg-gradient-to-br from-primary/5 to-position-bg border-primary/30 shadow-2xl hover:shadow-primary/30 transition-shadow duration-300">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-primary drop-shadow flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Spike Sinyalleri
          </CardTitle>
          <div className="flex items-center gap-2">
            {isAnalyzing && (
              <Badge variant="secondary" className="text-xs">
                <Activity className="h-3 w-3 mr-1 animate-pulse" />
                Analyzing • {formatElapsedTime(elapsedTime)}
              </Badge>
            )}
            {onStartAnalysis && onStopAnalysis && (
              <>
                <Button
                  size="sm"
                  variant={isAnalyzing ? "destructive" : "default"}
                  onClick={isAnalyzing ? handleStopAnalysis : handleStartAnalysis}
                  className="text-xs"
                >
                  {isAnalyzing ? (
                    <>
                      <Pause className="h-3 w-3 mr-1" />
                      Stop Analysis
                    </>
                  ) : (
                    <>
                      <Play className="h-3 w-3 mr-1" />
                      Start Analysis
                    </>
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleDebugTest}
                  className="text-xs"
                >
                  Debug Test
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {showSettings ? (
          <Tabs defaultValue="signals" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="signals" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Sinyaller
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Ayarlar
              </TabsTrigger>
            </TabsList>

          <TabsContent value="signals" className="space-y-4">
            {/* Statistics */}
            <div className="p-4 bg-muted/10 rounded-lg border border-border/20">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-sm text-muted-foreground">Confirmed</div>
                  <div className="font-bold text-spike-alert">{confirmedSpikes}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">High Conf.</div>
                  <div className="font-bold text-primary">{highConfidence}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Traded</div>
                  <div className="font-bold text-profit">{completedTrades}</div>
                </div>
              </div>
            </div>

            {/* Signals List */}
            {filteredSignals.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                <BarChart3 className="h-8 w-8 mx-auto mb-3 opacity-50" />
                <p>No spike signals detected</p>
                <p className="text-sm mt-1">Monitoring market for potential spikes...</p>
              </div>
            ) : (
              <div className="space-y-1 max-h-96 overflow-y-auto">
                {filteredSignals
                  .sort((a, b) => b.zaman - a.zaman)
                  .map((signal) => (
                    <div
                      key={signal.id}
                      className={`p-2 rounded-md border transition-all duration-200 cursor-pointer ${selectedSignal === signal.id
                        ? 'bg-primary/10 border-primary/30 shadow-lg'
                        : 'bg-muted/10 border-primary/10 hover:bg-primary/5 hover:shadow-md'
                        }`}
                      onClick={() => setSelectedSignal(selectedSignal === signal.id ? null : signal.id)}
                    >
                      {/* Signal Header */}
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(signal.durum)}
                            <span className="font-bold">{signal.sembol}</span>
                          </div>
                          {getConfidenceBadge(signal.guvenilirlik)}
                        </div>
                        <div className="text-right">
                          <div className="font-mono text-sm">${formatPrice(signal.fiyat)}</div>
                          <div className="text-xs text-muted-foreground">
                            {formatTime(signal.zaman)}
                          </div>
                        </div>
                      </div>

                      {/* Signal Metrics */}
                      <div className="grid grid-cols-4 gap-1 text-xs">
                        <div>
                          <div className="text-muted-foreground text-xs">MA5</div>
                          <div className="font-mono text-xs">${formatPrice(signal.ma5)}</div>
                        </div>
                        <div>
                          <div className="text-muted-foreground text-xs">Slope</div>
                          <div className={`font-mono text-xs ${(signal.egim || 0) > 0 ? 'text-profit' : 'text-loss'}`}>
                            {(signal.egim || 0) > 0 ? '+' : ''}{(signal.egim || 0).toFixed(4)}
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground flex items-center gap-1">
                            <RefreshCw className="h-3 w-3" />
                            Current MA10
                          </div>
                          {(() => {
                            const rtData = getRealTimeMA10(signal);
                            if (!rtData) {
                              return <div className="font-mono text-muted-foreground">-</div>;
                            }
                            const changeColor = rtData.ma10ChangePercent > 0 ? 'text-profit' :
                                               rtData.ma10ChangePercent < 0 ? 'text-loss' : 'text-muted-foreground';
                            return (
                              <div className="space-y-1">
                                <div className="font-mono">${formatPrice(rtData.ma10)}</div>
                                <div className={`font-mono text-xs ${changeColor}`}>
                                  {rtData.ma10ChangePercent > 0 ? '+' : ''}{rtData.ma10ChangePercent.toFixed(2)}%
                                </div>
                              </div>
                            );
                          })()}
                        </div>
                        <div>
                          <div className="text-muted-foreground">Status</div>
                          <div className={`font-medium ${getStatusColor(signal.durum)}`}>
                            {signal.durum}
                          </div>
                        </div>
                      </div>

                      {/* Action Button */}
                      {signal.durum === 'TESPIT_EDILDI' && signal.guvenilirlik === 'ONAYLANMIS_SPIKE' && onExecuteSignal && (
                        <div className="mt-3 pt-2 border-t border-border/20">
                          <Button
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onExecuteSignal(signal);
                            }}
                            className="w-full bg-gradient-to-r from-spike-alert to-spike-alert/80 hover:from-spike-alert/90 hover:to-spike-alert text-black font-medium"
                          >
                            <Target className="h-3 w-3 mr-2" />
                            Execute Trade
                          </Button>
                        </div>
                      )}

                      {/* Expanded Details */}
                      {selectedSignal === signal.id && (
                        <div className="ml-4 p-3 bg-muted/20 rounded-lg border border-border/20 space-y-3">
                          <h4 className="font-medium text-sm">Signal Analysis</h4>

                          {/* Real-time Price Tracking */}
                          {(() => {
                            const rtData = getRealTimeMA10(signal);
                            if (rtData) {
                              return (
                                <div className="p-2 bg-primary/5 rounded border border-primary/20">
                                  <div className="text-xs text-muted-foreground mb-1 flex items-center gap-1">
                                    <RefreshCw className="h-3 w-3" />
                                    Real-time Updates
                                  </div>
                                  <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                      <div className="text-muted-foreground">Current Price</div>
                                      <div className="font-mono">${formatPrice(rtData.currentPrice)}</div>
                                      <div className={`font-mono text-xs ${rtData.priceChangePercent > 0 ? 'text-profit' : 'text-loss'}`}>
                                        {rtData.priceChangePercent > 0 ? '+' : ''}{rtData.priceChangePercent.toFixed(2)}% from signal
                                      </div>
                                    </div>
                                    <div>
                                      <div className="text-muted-foreground">Current MA10</div>
                                      <div className="font-mono">${formatPrice(rtData.ma10)}</div>
                                      <div className={`font-mono text-xs ${rtData.ma10ChangePercent > 0 ? 'text-profit' : 'text-loss'}`}>
                                        {rtData.ma10ChangePercent > 0 ? '+' : ''}{rtData.ma10ChangePercent.toFixed(2)}% from signal
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                            return null;
                          })()}

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <div className="text-muted-foreground">Signal MA10</div>
                              <div className="font-mono">${formatPrice(signal.ma10)}</div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">Price Gap</div>
                              <div className="font-mono text-profit">
                                +{(() => {
                                  const fiyat = signal.fiyat || 0;
                                  const ma5 = signal.ma5 || 1;
                                  return (((fiyat - ma5) / ma5) * 100).toFixed(2);
                                })()}%
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <div className="text-muted-foreground flex items-center gap-1">
                                <Volume2 className="h-3 w-3" />
                                Volume
                              </div>
                              <div className="font-mono">{(signal.hacim || 0).toLocaleString()}</div>
                            </div>
                            <div>
                              <div className="text-muted-foreground">Vol. Ratio</div>
                              <div className={`font-mono ${(signal.hacim || 0) > (signal.ortalamHacim || 1) * 2 ? 'text-spike-alert' : 'text-muted-foreground'
                                }`}>
                                {((signal.hacim || 0) / (signal.ortalamHacim || 1)).toFixed(1)}x
                              </div>
                            </div>
                          </div>

                          <Separator className="bg-border/30" />

                          <div className="text-xs text-muted-foreground">
                            <div><strong>Signal ID:</strong> {signal.id}</div>
                            <div><strong>Detected at:</strong> {new Date(signal.zaman).toLocaleString()}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            )}

            {/* Live Status */}
            <div className="flex items-center justify-center gap-2 text-muted-foreground text-sm pt-2 border-t border-border/20">
              <div className="h-2 w-2 bg-primary rounded-full animate-pulse" />
              Monitoring {signals.length} total signals
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            {settings && onSettingsChange && onSettingsSave && onSettingsReset ? (
              <SpikeAnalysisSettings
                settings={settings}
                onSettingsChange={onSettingsChange}
                onSave={onSettingsSave}
                onReset={onSettingsReset}
                isConnected={true}
              />
            ) : (
              <div className="p-8 text-center text-muted-foreground">
                <Settings className="h-8 w-8 mx-auto mb-3 opacity-50" />
                <p>Ayarlar yüklenemedi</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
        ) : (
          // Ana panelde sadece sinyaller gösterilir
          <div className="space-y-2">
            {/* Statistics */}
            <div className="p-2 bg-muted/10 rounded-md border border-border/20">
              <div className="grid grid-cols-3 gap-2 text-center">
                <div>
                  <div className="text-xs text-muted-foreground">Confirmed</div>
                  <div className="font-bold text-sm text-spike-alert">{confirmedSpikes}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">High Conf.</div>
                  <div className="font-bold text-sm text-primary">{highConfidence}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Completed</div>
                  <div className="font-bold text-sm text-profit">{completedTrades}</div>
                </div>
              </div>
            </div>

            {/* Signals List */}
            <div className="space-y-1 max-h-96 overflow-y-auto">
              {signals.length === 0 ? (
                <div className="p-6 text-center text-muted-foreground">
                  <Zap className="h-6 w-6 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Henüz spike sinyali tespit edilmedi</p>
                  <p className="text-xs mt-1">Analiz başlatıldığında sinyaller burada görünecek</p>
                </div>
              ) : (
                signals.map((signal) => (
                  <div
                    key={signal.id}
                    className={`p-2 rounded-md border transition-all duration-200 cursor-pointer ${
                      selectedSignal === signal.id
                        ? 'bg-primary/10 border-primary/30 shadow-md'
                        : 'bg-muted/10 border-primary/10 hover:bg-primary/5 hover:shadow-md'
                    }`}
                    onClick={() => {
                      setSelectedSignal(selectedSignal === signal.id ? null : signal.id);
                      if (onSignalClick) onSignalClick(signal);
                    }}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <Zap className="h-3 w-3 text-spike-alert" />
                          <span className="font-bold text-sm">{signal.sembol}</span>
                        </div>
                        {getConfidenceBadge(signal.guvenilirlik)}
                      </div>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(signal.durum)}
                        <span className={`text-xs font-medium ${getStatusColor(signal.durum)}`}>
                          {signal.durum}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div>
                        <div className="text-muted-foreground text-xs">Fiyat</div>
                        <div className="font-mono text-xs">${formatPrice(signal.fiyat || 0)}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground text-xs">MA10</div>
                        <div className="font-mono text-xs">${formatPrice(signal.ma10 || 0)}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground text-xs">Eğim</div>
                        <div className="font-mono text-xs text-primary">{(signal.egim || 0).toFixed(4)}</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2 mt-1 text-xs">
                      <div>
                        <div className="text-muted-foreground text-xs">Hacim</div>
                        <div className="font-mono text-xs">{((signal.hacim || 0) / 1000).toFixed(1)}K</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground text-xs">Zaman</div>
                        <div className="text-xs">{new Date(signal.zaman).toLocaleTimeString()}</div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};