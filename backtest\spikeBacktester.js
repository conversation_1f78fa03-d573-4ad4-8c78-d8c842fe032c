// spikeBacktester.js
import axios from 'axios';
import { calculateMA, calculateSlope } from './technical-indicators.js';

const LIMIT = 1000; // max per Binance API call
const VOLUME_MULTIPLIER = 1.5;

// 1. Kline verilerini <PERSON>ek
async function fetchKlines(symbol, interval, startTime, endTime) {
  const url = `https://fapi.binance.com/fapi/v1/klines?symbol=${symbol}&interval=${interval}&startTime=${startTime}&endTime=${endTime}&limit=${LIMIT}`;
  const response = await axios.get(url);
  return response.data.map(k => ({
    openTime: k[0],
    open: parseFloat(k[1]),
    high: parseFloat(k[2]),
    low: parseFloat(k[3]),
    close: parseFloat(k[4]),
    volume: parseFloat(k[5]),
    closeTime: k[6]
  }));
}

// 2. Spike tespit kriteri
function isSpike(candles, i, threshold) {
  if (i < 10) return false;

  const closes = candles.slice(i - 10, i).map(c => c.close);
  const volumes = candles.slice(i - 10, i).map(c => c.volume);

  const ma10 = calculateMA(closes, 10);
  const slope = calculateSlope(closes, 10);
  const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;

  const current = candles[i];
  const priceGap = ((current.close - ma10) / ma10) * 100;
  const volumeRatio = current.volume / avgVolume;

  return priceGap > threshold.priceGap && slope > threshold.slope && volumeRatio > VOLUME_MULTIPLIER;
}

// 3. TP/SL kontrol fonksiyonu
function evaluateSpikeEntry(candles, entryIndex, tpPercent = 1.5, slPercent = 1.0, maxLookahead = 20) {
  const entryPrice = candles[entryIndex].close;
  const tpPrice = entryPrice * (1 + tpPercent / 100);
  const slPrice = entryPrice * (1 - slPercent / 100);

  for (let i = 1; i <= maxLookahead; i++) {
    const c = candles[entryIndex + i];
    if (!c) break;
    if (c.high >= tpPrice) return 'TP';
    if (c.low <= slPrice) return 'SL';
  }
  return 'NEUTRAL';
}

// 4. Ana fonksiyon
export async function runBacktest(config) {
  const { symbols, interval, threshold, cooldown, tpPercent = 1.0, slPercent = 0.8, maxLookahead = 30 } = config;

  let totalTested = 0;
  let successCount = 0;
  let failCount = 0;

  const symbolCooldowns = new Map();

  // Her sembol için test yap
  for (const symbol of symbols) {
    console.log(`\n🔍 Testing ${symbol}...`);

    // Son 30 gün için veri çek (daha fazla veri için)
    const end = Date.now();
    const start = end - (30 * 24 * 60 * 60 * 1000); // 30 gün önce

    try {
      const candles = await fetchKlines(symbol, interval, start, end);
      let tp = 0, sl = 0, neutral = 0;

      for (let i = 10; i < candles.length - maxLookahead; i++) {
        const currentTime = candles[i].openTime;

        // Cooldown kontrolü
        if (symbolCooldowns.has(symbol)) {
          const lastSpikeTime = symbolCooldowns.get(symbol);
          if (currentTime - lastSpikeTime < cooldown) {
            continue;
          }
        }

        if (isSpike(candles, i, threshold)) {
          totalTested++;
          symbolCooldowns.set(symbol, currentTime);

          const result = evaluateSpikeEntry(candles, i, tpPercent, slPercent, maxLookahead);
          if (result === 'TP') {
            tp++;
            successCount++;
          } else if (result === 'SL') {
            sl++;
            failCount++;
          } else {
            neutral++;
          }
        }
      }

      const symbolTotal = tp + sl + neutral;
      if (symbolTotal > 0) {
        console.log(`  Spike Sayısı: ${symbolTotal}`);
        console.log(`  ✅ TP: ${tp} (%${((tp / symbolTotal) * 100).toFixed(1)})`);
        console.log(`  ❌ SL: ${sl} (%${((sl / symbolTotal) * 100).toFixed(1)})`);
        console.log(`  ⚠️ Neutral: ${neutral} (%${((neutral / symbolTotal) * 100).toFixed(1)})`);
      } else {
        console.log(`  Hiç spike tespit edilmedi.`);
      }

    } catch (error) {
      console.error(`❌ ${symbol} için veri çekilemedi:`, error.message);
    }
  }

  const successRate = totalTested > 0 ? (successCount / totalTested) * 100 : 0;

  return {
    totalTested,
    successCount,
    failCount,
    successRate
  };
}
