// main.js
import { runBacktest } from './spikeBacktester.js';

const main = async () => {
  try {
    console.log('🚀 Backtest başlatılıyor...');

    const result = await runBacktest({
      symbols: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'],
      interval: '1m',
      threshold: {
        priceGap: 0.4,   // % fark
        slope: 0.1,      // eğim
      },
      cooldown: 5 * 60 * 1000, // 5 dakika aynı sembol için bekleme
      tpPercent: 1.0,    // Take Profit %1
      slPercent: 0.8,    // Stop Loss %0.8
      maxLookahead: 30   // 30 dakika sonrasına kadar bak
    });

    console.log('\n📊 Backtest Sonuçları');
    console.log('----------------------');
    console.log(`🧪 Test edilen spike sayısı: ${result.totalTested}`);
    console.log(`✅ Başarılı işlemler: ${result.successCount}`);
    console.log(`❌ Zararlı işlemler: ${result.failCount}`);
    console.log(`🎯 Başarı oranı: %${result.successRate.toFixed(2)}`);
  } catch (error) {
    console.error('❌ Backtest hatası:', error.message);
    console.error(error.stack);
  }
};

main();
