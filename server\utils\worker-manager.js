/**
 * WORKER MANAGER
 * Manages spike detection workers for high-performance processing
 * Load balancing and health monitoring
 */

import { Worker } from 'worker_threads';
import { fileURLToPath } from 'url';
import path from 'path';
import os from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WorkerManager {
  constructor() {
    this.workers = [];
    this.currentWorkerIndex = 0;
    this.workerCount = Math.min(os.cpus().length, 4); // Max 4 workers
    this.isInitialized = false;
    this.pendingTasks = 0;
    this.completedTasks = 0;
    this.failedTasks = 0;
    
    // Performance metrics
    this.metrics = {
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      taskCount: 0
    };
  }

  /**
   * Initialize worker pool
   */
  async initialize(config = {}) {
    if (this.isInitialized) {
      console.log('⚠️ Worker manager already initialized');
      return;
    }

    console.log(`🔧 Initializing ${this.workerCount} spike detection workers...`);

    try {
      for (let i = 0; i < this.workerCount; i++) {
        await this.createWorker(i, config);
      }

      this.isInitialized = true;
      console.log(`✅ Worker manager initialized with ${this.workers.length} workers`);
      
      // Start health monitoring
      this.startHealthMonitoring();
      
    } catch (error) {
      console.error('❌ Worker manager initialization failed:', error.message);
      throw error;
    }
  }

  /**
   * Create a single worker
   */
  async createWorker(workerId, config) {
    return new Promise((resolve, reject) => {
      try {
        const workerPath = path.join(__dirname, '../workers/spike-detection-worker.cjs');
        
        const worker = new Worker(workerPath, {
          workerData: { 
            workerId: `worker-${workerId}`,
            config 
          }
        });

        const workerInfo = {
          id: workerId,
          worker,
          isReady: false,
          isHealthy: true,
          taskCount: 0,
          lastActivity: Date.now(),
          processingTimes: []
        };

        // Worker ready handler
        worker.on('message', (message) => {
          this.handleWorkerMessage(workerInfo, message);
        });

        // Worker error handler
        worker.on('error', (error) => {
          console.error(`❌ Worker ${workerId} error:`, error.message);
          workerInfo.isHealthy = false;
          this.failedTasks++;
          
          // Restart worker if needed
          this.restartWorker(workerId, config);
        });

        // Worker exit handler
        worker.on('exit', (code) => {
          if (code !== 0) {
            console.error(`❌ Worker ${workerId} exited with code ${code}`);
            workerInfo.isHealthy = false;
            this.restartWorker(workerId, config);
          }
        });

        this.workers[workerId] = workerInfo;

        // Wait for worker ready signal
        const readyTimeout = setTimeout(() => {
          reject(new Error(`Worker ${workerId} ready timeout`));
        }, 5000);

        const originalHandler = worker.on('message', (message) => {
          if (message.type === 'WORKER_READY') {
            clearTimeout(readyTimeout);
            workerInfo.isReady = true;
            console.log(`✅ Worker ${workerId} ready`);
            resolve(workerInfo);
          }
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Handle worker messages
   */
  handleWorkerMessage(workerInfo, message) {
    const { type, spike, processingTime, error } = message;

    switch (type) {
      case 'WORKER_READY':
        workerInfo.isReady = true;
        break;

      case 'SPIKE_DETECTED':
        this.completedTasks++;
        this.pendingTasks--;
        workerInfo.taskCount++;
        workerInfo.lastActivity = Date.now();
        
        // Update performance metrics
        if (processingTime) {
          workerInfo.processingTimes.push(processingTime);
          if (workerInfo.processingTimes.length > 100) {
            workerInfo.processingTimes.shift(); // Keep last 100
          }
          
          this.updateMetrics(processingTime);
        }

        // Emit spike to main application
        this.emit('spike-detected', spike);
        break;

      case 'ERROR':
        this.failedTasks++;
        this.pendingTasks--;
        workerInfo.isHealthy = false;
        console.error(`❌ Worker ${workerInfo.id} task error:`, error);
        break;

      case 'HEALTH_RESPONSE':
        workerInfo.isHealthy = message.status === 'healthy';
        workerInfo.lastActivity = Date.now();
        break;
    }
  }

  /**
   * Get next available worker (round-robin)
   */
  getNextWorker() {
    if (!this.isInitialized || this.workers.length === 0) {
      return null;
    }

    let attempts = 0;
    while (attempts < this.workers.length) {
      const worker = this.workers[this.currentWorkerIndex];
      this.currentWorkerIndex = (this.currentWorkerIndex + 1) % this.workers.length;
      
      if (worker && worker.isReady && worker.isHealthy) {
        return worker;
      }
      
      attempts++;
    }

    console.warn('⚠️ No healthy workers available');
    return null;
  }

  /**
   * Process spike detection task
   */
  async processSpike(symbol, klineData, marketData, cooldownCheck) {
    const worker = this.getNextWorker();
    if (!worker) {
      console.warn('⚠️ No workers available for spike detection');
      return false;
    }

    try {
      this.pendingTasks++;
      
      worker.worker.postMessage({
        type: 'DETECT_SPIKE',
        symbol,
        klineData,
        marketData,
        cooldownCheck,
        timestamp: Date.now()
      });

      return true;
    } catch (error) {
      console.error('❌ Failed to send task to worker:', error.message);
      this.pendingTasks--;
      return false;
    }
  }

  /**
   * Update worker configuration
   */
  updateWorkerConfig(config) {
    this.workers.forEach(workerInfo => {
      if (workerInfo.isReady) {
        workerInfo.worker.postMessage({
          type: 'UPDATE_CONFIG',
          config
        });
      }
    });
  }

  /**
   * Restart a failed worker
   */
  async restartWorker(workerId, config) {
    console.log(`🔄 Restarting worker ${workerId}...`);
    
    try {
      // Terminate old worker
      if (this.workers[workerId]) {
        await this.workers[workerId].worker.terminate();
      }

      // Create new worker
      await this.createWorker(workerId, config);
      console.log(`✅ Worker ${workerId} restarted successfully`);
      
    } catch (error) {
      console.error(`❌ Failed to restart worker ${workerId}:`, error.message);
    }
  }

  /**
   * Start health monitoring
   */
  startHealthMonitoring() {
    setInterval(() => {
      this.workers.forEach(workerInfo => {
        if (workerInfo.isReady) {
          workerInfo.worker.postMessage({ type: 'HEALTH_CHECK' });
        }
      });
    }, 30000); // Check every 30 seconds
  }

  /**
   * Update performance metrics
   */
  updateMetrics(processingTime) {
    this.metrics.taskCount++;
    this.metrics.totalProcessingTime += processingTime;
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.taskCount;
  }

  /**
   * Get performance statistics
   */
  getStats() {
    const healthyWorkers = this.workers.filter(w => w.isHealthy).length;
    const readyWorkers = this.workers.filter(w => w.isReady).length;
    
    return {
      totalWorkers: this.workers.length,
      healthyWorkers,
      readyWorkers,
      pendingTasks: this.pendingTasks,
      completedTasks: this.completedTasks,
      failedTasks: this.failedTasks,
      averageProcessingTime: this.metrics.averageProcessingTime,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('🔄 Shutting down worker manager...');
    
    const shutdownPromises = this.workers.map(async (workerInfo) => {
      if (workerInfo.worker) {
        try {
          await workerInfo.worker.terminate();
        } catch (error) {
          console.error(`❌ Error terminating worker ${workerInfo.id}:`, error.message);
        }
      }
    });

    await Promise.allSettled(shutdownPromises);
    this.workers = [];
    this.isInitialized = false;
    
    console.log('✅ Worker manager shutdown complete');
  }

  /**
   * Event emitter functionality
   */
  emit(event, data) {
    // This will be connected to main application event system
    if (this.eventHandler) {
      this.eventHandler(event, data);
    }
  }

  setEventHandler(handler) {
    this.eventHandler = handler;
  }
}

// Singleton instance
export const workerManager = new WorkerManager();
export default workerManager;
