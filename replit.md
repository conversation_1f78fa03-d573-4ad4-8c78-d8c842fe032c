# ParaBOT - Crypto Trading Bot

## Overview

ParaBOT is a comprehensive cryptocurrency trading bot system built with React, Node.js, and Express. The application specializes in automated trading with spike detection algorithms and provides a full-featured dashboard for managing trading configurations, monitoring positions, and analyzing market data.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Framework**: Tailwind CSS with shadcn/ui components
- **State Management**: React Query (@tanstack/react-query) for server state
- **Routing**: React Router for client-side navigation
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ESM modules
- **Database**: PostgreSQL with Drizzle ORM
- **Database Provider**: Neon Database (@neondatabase/serverless)
- **Session Management**: In-memory storage with fallback to database sessions
- **API Pattern**: RESTful endpoints with /api prefix

### Key Components

#### Trading Engine
- **Spike Detection Algorithm**: Implements MA5/MA10 crossover detection with volume analysis
- **Position Management**: Automated SHORT position handling with risk management
- **Market Data Processing**: Real-time WebSocket connections to Binance API
- **Risk Management**: Configurable stop-loss, take-profit, and position sizing

#### Authentication System
- **Session-based Authentication**: Local storage with user session management
- **Mock User System**: JSON-based user data for development
- **Role-based Access**: Admin and user roles with different permissions

#### UI Components
- **Dashboard Layout**: Responsive multi-tab interface
- **Trading Panel**: Configuration interface for bot parameters
- **Market Monitor**: Real-time price and volume display
- **Position Tracker**: Live P&L monitoring and position management
- **Signal Display**: Visual representation of trading signals

## Data Flow

### Trading Flow
1. **Market Data Ingestion**: WebSocket connections fetch real-time kline data
2. **Signal Processing**: Trading engine analyzes price action using MA indicators
3. **Position Execution**: Automated SHORT positions based on spike detection
4. **Risk Management**: Continuous monitoring with automated stop-loss/take-profit

### User Interface Flow
1. **Authentication**: User login with credentials validation
2. **Dashboard Access**: Multi-tab interface with real-time updates
3. **Configuration**: Trading parameters adjustment through UI controls
4. **Monitoring**: Live position tracking and market data display

## External Dependencies

### Trading Infrastructure
- **Binance API**: WebSocket streams for market data and order execution
- **Neon Database**: PostgreSQL hosting for production data storage
- **Real-time Updates**: WebSocket connections for live data streaming

### UI Framework Dependencies
- **Radix UI**: Headless component primitives for accessibility
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Icon library for consistent UI elements
- **React Query**: Server state management and caching

## Deployment Strategy

### Development Environment
- **Local Development**: Vite dev server with Express backend
- **Hot Module Replacement**: Real-time code updates during development
- **Environment Variables**: DATABASE_URL for database connection

### Production Build
- **Frontend**: Vite build with optimized bundle output to dist/public
- **Backend**: esbuild compilation to dist/index.js
- **Database**: Drizzle migrations with PostgreSQL schema management
- **Deployment**: Single-server deployment with static file serving

### Configuration Management
- **Environment Variables**: Database connection and API keys
- **Build Scripts**: Unified build process for frontend and backend
- **Database Migrations**: Version-controlled schema changes with Drizzle

### Special Features
- **Turkish Language Support**: UI components and types include Turkish translations
- **Replit Integration**: Special development environment support with runtime error overlay
- **Mock Data Systems**: Development-friendly data simulation for testing

The application follows a modern full-stack architecture with emphasis on real-time trading capabilities, user-friendly interfaces, and robust risk management systems.