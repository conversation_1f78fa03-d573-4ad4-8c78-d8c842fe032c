/**
 * Spike Detection Algorithm Test
 * Optimize edilmiş spike detection algoritmasını test eder
 */

import { SpikeDetector } from './server/algorithms/spike-detection.js';
import { calculateRSI, calculateMACD } from './server/algorithms/technical-indicators.js';

// Test için mock data oluştur
const createMockMarketData = () => {
  const marketData = new Map();
  
  // BTCUSDT için test verisi
  const closes = [];
  const volumes = [];
  
  // 50 adet mock veri oluştur
  let basePrice = 45000;
  let baseVolume = 1000;
  
  for (let i = 0; i < 50; i++) {
    // Normal fiyat hareketi
    const priceChange = (Math.random() - 0.5) * 0.01; // ±0.5% değişim
    basePrice = basePrice * (1 + priceChange);
    
    // Normal hacim
    const volumeChange = (Math.random() - 0.5) * 0.2; // ±10% değişim
    baseVolume = baseVolume * (1 + volumeChange);
    
    closes.push(basePrice);
    volumes.push(baseVolume);
  }
  
  // Son 10 dakikada spike koşullarını sağla
  for (let i = 40; i < 50; i++) {
    // Güçlü fiyat artışı (RSI > 60 ve MACD > 0 için)
    closes[i] = closes[i-1] * 1.02; // %2 artış

    // Hacim artışı (1.5x+ için)
    volumes[i] = baseVolume * 2.5; // 2.5x ortalama hacim
  }

  // RSI ve MACD kontrolü
  const rsi = calculateRSI(closes);
  const macd = calculateMACD(closes);

  console.log(`📊 Test Data - RSI: ${rsi?.toFixed(2)}, MACD: ${macd?.macd?.toFixed(8)}`);

  // Eğer RSI < 60 ise daha fazla artış ekle
  if (rsi < 60) {
    for (let i = 47; i < 50; i++) {
      closes[i] = closes[i-1] * 1.025; // %2.5 ek artış
    }
  }
  
  marketData.set('BTCUSDT', {
    closes,
    volumes,
    highs: closes.map(c => c * 1.001), // Yüksek fiyatlar
    lows: closes.map(c => c * 0.999)   // Düşük fiyatlar
  });
  
  return marketData;
};

// Test için mock ticker data
const createMockTicker = (symbol, price, volume) => ({
  s: symbol,
  c: price.toString(),
  h: (price * 1.001).toString(),
  l: (price * 0.999).toString(),
  v: volume.toString(),
  q: (price * volume).toString()
});

// Test fonksiyonu
async function testSpikeDetection() {
  console.log('🧪 Spike Detection Algorithm Test Başlıyor...\n');
  
  try {
    // Spike detector oluştur (WebSocket kline stream ile)
    const detector = new SpikeDetector({
      algorithm: 'advanced',
      cooldownPeriod: 30000, // 30 saniye
      symbols: ['BTCUSDT', 'ETHUSDT'] // Test için 2 sembol
    });
    
    // Event listener ekle
    detector.on('spike', (spike) => {
      console.log('🚨 SPIKE DETECTED!');
      console.log('📊 Spike Details:');
      console.log(`   Symbol: ${spike.sembol}`);
      console.log(`   Price: $${spike.fiyat.toFixed(2)}`);
      console.log(`   MA5: $${spike.ma5.toFixed(2)}`);
      console.log(`   MA10: $${spike.ma10.toFixed(2)}`);
      console.log(`   MA Difference: ${spike.maDifference.toFixed(4)}%`);
      console.log(`   RSI: ${spike.rsi.toFixed(2)}`);
      console.log(`   MACD: ${spike.macd.toFixed(8)}`);
      console.log(`   Volume Ratio: ${spike.volumeRatio.toFixed(2)}x`);
      console.log(`   Price Velocity (1m): ${spike.velocity1m.toFixed(2)}%`);
      console.log(`   Confidence: ${spike.confidence.toFixed(2)}%`);
      console.log(`   Reliability: ${spike.guvenilirlik}`);
      console.log(`   Algorithm: ${spike.algorithm}`);
      console.log('');
    });
    
    detector.on('connected', () => {
      console.log('✅ Detector connected');
    });
    
    detector.on('error', (error) => {
      console.error('❌ Detector error:', error.message);
    });
    
    // WebSocket bağlantısını başlat (historical data + real-time)
    console.log('📡 WebSocket bağlantısı başlatılıyor...');
    console.log('📈 Historical data yükleniyor...');

    await detector.connect();

    // Bağlantı kurulana kadar bekle
    await new Promise(resolve => {
      detector.on('connected', () => {
        console.log('✅ WebSocket bağlantısı kuruldu (historical + real-time)');
        resolve();
      });
    });

    // Market data durumunu kontrol et
    console.log('📊 Market data durumu:');
    const stats = detector.getStats();
    console.log(`   Tracked symbols: ${stats.trackedSymbols}`);
    console.log(`   Cache size: ${stats.cacheSize}`);

    // Gerçek zamanlı veri akışını bekle
    console.log('⏳ Gerçek zamanlı kline verilerini bekliyoruz...');
    console.log('💡 Bu test historical + real-time Binance verilerini kullanır');

    // 30 saniye bekle ve gelen verileri gözlemle
    setTimeout(() => {
      console.log('📊 Test süresi doldu, bağlantı kapatılıyor...');
      detector.disconnect();
    }, 30000);
    
    // İstatistikleri göster
    console.log('📊 Detector Statistics:');
    console.log(detector.getStats());
    
  } catch (error) {
    console.error('❌ Test Error:', error.message);
    console.error(error.stack);
  }
}

// Test çalıştır
testSpikeDetection().then(() => {
  console.log('✅ Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test failed:', error.message);
  process.exit(1);
});
