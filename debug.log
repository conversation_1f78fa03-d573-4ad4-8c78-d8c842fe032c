[dotenv@17.2.0] injecting env (0) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`)
🔍 Tüm USDT çiftleri alınıyor...
⚠️ MONITORED_SYMBOLS boş, logging stream başlatılamıyor
🚀 ParaBOT Server 3001 portunda çalışıyor
📡 API Base URL: http://localhost:3001/api
🔐 Login endpoint: http://localhost:3001/api/auth/login
⚙️  Binance endpoints: http://localhost:3001/api/binance/*
🌐 WebSocket Server: ws://localhost:3001
✅ 452 aktif USDT çifti bulundu
📋 İlk 10 çift: 1000000BOBUSDT, 1000000MOGUSDT, 1000BONKUSDT, 1000CATUSDT, 1000CHEEMSUSDT, 1000FLOKIUSDT, 1000LUNCUSDT, 1000PEPEUSDT, 1000RATSUSDT, 1000SATSUSDT
🔧 452 coin için market data başlatılıyor...
✅ Market data 452 coin için hazırlandı
🔍 getUserCredentials için Mehmet - User bulundu: true
🔍 Mehmet için binanceConfig: true
🔍 Mehmet için API Key: Var
🔍 Mehmet için Secret Key: Var
