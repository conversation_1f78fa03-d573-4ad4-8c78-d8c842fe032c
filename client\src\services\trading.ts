// ParaBOT Trading Engine
import { SpikeSignal, Position, TradingConfig, KlineData } from '@/types/trading';

export class TradingEngine {
  private config: TradingConfig;
  private positions: Map<string, Position> = new Map();
  private spikeSignals: SpikeSignal[] = [];
  private priceHistory: Map<string, KlineData[]> = new Map();
  private ma5Values: Map<string, number[]> = new Map();
  private ma10Values: Map<string, number[]> = new Map();
  private volumeHistory: Map<string, number[]> = new Map();
  private cooldownPeriods: Map<string, number> = new Map();

  constructor(config: TradingConfig) {
    this.config = config;
  }

  // Spike Detection Algorithm
  public processPriceData(klineData: KlineData): SpikeSignal | null {
    const { symbol, close, volume, openTime } = klineData;
    
    // Store price history (last 50 candles for MA calculation)
    if (!this.priceHistory.has(symbol)) {
      this.priceHistory.set(symbol, []);
      this.ma5Values.set(symbol, []);
      this.ma10Values.set(symbol, []);
      this.volumeHistory.set(symbol, []);
    }

    const prices = this.priceHistory.get(symbol)!;
    const volumes = this.volumeHistory.get(symbol)!;
    
    prices.push(klineData);
    volumes.push(volume);
    
    // Keep only last 50 candles
    if (prices.length > 50) {
      prices.shift();
      volumes.shift();
    }

    // Need at least 10 candles for MA10
    if (prices.length < 10) return null;

    // Calculate Moving Averages
    const ma5 = this.calculateMA(prices.slice(-5).map(p => p.close));
    const ma10 = this.calculateMA(prices.slice(-10).map(p => p.close));
    
    this.ma5Values.get(symbol)!.push(ma5);
    this.ma10Values.get(symbol)!.push(ma10);

    // Keep only last 20 MA values for slope calculation
    const ma5History = this.ma5Values.get(symbol)!;
    if (ma5History.length > 20) ma5History.shift();

    // Calculate MA5 slope (needs at least 3 points)
    if (ma5History.length < 3) return null;
    
    const slope = this.calculateSlope(ma5History.slice(-3));
    const avgVolume = volumes.length >= 10 ? 
      this.calculateMA(volumes.slice(-10)) : 
      this.calculateMA(volumes);

    // Check cooldown period
    const lastSignalTime = this.cooldownPeriods.get(symbol) || 0;
    if (openTime - lastSignalTime < 5 * 60 * 1000) { // 5 minutes cooldown
      return null;
    }

    // Spike Detection Conditions
    const priceAboveMA5 = close > ma5;
    const positiveSlope = slope > this.config.spikeThreshold;
    const volumeSpike = volume > avgVolume * this.config.volumeMultiplier;
    const priceGap = (close - ma5) / ma5;

    let confidence: SpikeSignal['confidence'] = 'LOW';
    let signalStrength = 0;

    // Scoring system
    if (priceAboveMA5) signalStrength += 1;
    if (positiveSlope) signalStrength += 2;
    if (volumeSpike) signalStrength += 2;
    if (priceGap > 0.002) signalStrength += 1; // 0.2% above MA5

    // Assign confidence level
    if (signalStrength >= 5) confidence = 'CONFIRMED_SPIKE';
    else if (signalStrength >= 4) confidence = 'HIGH';
    else if (signalStrength >= 3) confidence = 'MEDIUM';

    // Only process HIGH and CONFIRMED_SPIKE signals
    if (confidence === 'HIGH' || confidence === 'CONFIRMED_SPIKE') {
      const signal: SpikeSignal = {
        id: `${symbol}_${openTime}`,
        symbol,
        timestamp: openTime,
        price: close,
        ma5,
        ma10,
        slope,
        volume,
        volumeAvg: avgVolume,
        confidence,
        status: 'DETECTED'
      };

      this.spikeSignals.push(signal);
      this.cooldownPeriods.set(symbol, openTime);
      
      console.log(`🚀 Spike detected: ${symbol} at ${close} (Confidence: ${confidence})`);
      
      return signal;
    }

    return null;
  }

  // Calculate Simple Moving Average
  private calculateMA(values: number[]): number {
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  // Calculate slope of MA5 (trend direction)
  private calculateSlope(values: number[]): number {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2; // Sum of indices 0,1,2...
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + i * val, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6; // Sum of squares

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope;
  }

  // Trading Logic - REAL API INTEGRATION
  public async executeSpikeTrade(signal: SpikeSignal): Promise<Position | null> {
    if (!this.config.autoTrade) {
      console.log('⏸️ Auto trade disabled, skipping signal');
      return null;
    }

    if (this.positions.size >= this.config.maxPositions) {
      console.log('⚠️ Max positions reached, skipping trade');
      return null;
    }

    try {
      // Calculate position size and prices
      const entryPrice = signal.price;
      const tpPrice = entryPrice * (1 - this.config.tpPercentage / 100);
      const slPrice = entryPrice * (1 + this.config.slPercentage / 100);

      // REAL BINANCE API CALL - Send to backend for execution
      const tradeParams = {
        symbol: signal.symbol,
        side: 'SHORT', // Spike trading typically shorts the spike
        quantity: this.calculatePositionSize(signal),
        leverage: this.config.leverage,
        stopLossPercent: this.config.slPercentage,
        takeProfitPercent: this.config.tpPercentage,
        confidence: signal.confidence,
        spikePrice: signal.price,
        spikeType: 'YUKSEK',
        rsi: 75 // Will be provided by signal
      };

      // Send trade request to backend
      const response = await fetch('/api/trading/execute-spike-trade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tradeParams)
      });

      if (!response.ok) {
        throw new Error(`Trading API error: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Trade execution failed');
      }

      // Create position from real API response
      const position: Position = {
        id: result.orderId || `pos_${signal.id}`,
        symbol: signal.symbol,
        side: 'SHORT',
        size: result.quantity || tradeParams.quantity,
        entryPrice: result.executedPrice || entryPrice,
        markPrice: result.executedPrice || entryPrice,
        pnl: 0,
        pnlPercentage: 0,
        leverage: this.config.leverage,
        tpPrice,
        slPrice,
        status: 'OPEN',
        openTime: Date.now()
      };

      this.positions.set(position.id, position);
      signal.status = 'TRADING';

      console.log(`📈 REAL Position opened: ${signal.symbol} SHORT at ${position.entryPrice}`);
      console.log(`🎯 TP: ${tpPrice.toFixed(4)} | 🛑 SL: ${slPrice.toFixed(4)}`);
      console.log(`📊 Order ID: ${result.orderId}`);

      return position;
    } catch (error) {
      console.error('Failed to execute REAL spike trade:', error);
      signal.status = 'FAILED';
      return null;
    }
  }

  // Calculate position size based on risk management
  private calculatePositionSize(signal: SpikeSignal): number {
    // Simple position sizing - can be enhanced
    const accountBalance = 1000; // Should get from API
    const riskPercent = 1; // 1% risk per trade
    const riskAmount = accountBalance * (riskPercent / 100);
    const stopLossPercent = this.config.slPercentage;

    // Position size = Risk Amount / (Entry Price * Stop Loss %)
    const positionSize = riskAmount / (signal.price * (stopLossPercent / 100));

    return Math.round(positionSize * 1000) / 1000; // Round to 3 decimals
  }

  // Position Management (Yazılımsal OCO)
  public updatePosition(positionId: string, markPrice: number): void {
    const position = this.positions.get(positionId);
    if (!position) return;

    position.markPrice = markPrice;
    
    // Calculate PnL
    if (position.side === 'LONG') {
      position.pnl = (markPrice - position.entryPrice) * position.size;
    } else {
      position.pnl = (position.entryPrice - markPrice) * position.size;
    }
    
    position.pnlPercentage = (position.pnl / (position.entryPrice * position.size)) * 100 * position.leverage;

    // Check TP/SL conditions (Yazılımsal OCO)
    this.checkOCOConditions(position);
  }

  private checkOCOConditions(position: Position): void {
    if (position.status !== 'OPEN') return;

    const { markPrice, tpPrice, slPrice, side } = position;

    if (side === 'SHORT') {
      // For SHORT positions: TP when price goes down, SL when price goes up
      if (tpPrice && markPrice <= tpPrice) {
        this.closePosition(position.id, 'TP_REACHED');
      } else if (slPrice && markPrice >= slPrice) {
        this.closePosition(position.id, 'SL_REACHED');
      }
    } else if (side === 'LONG') {
      // For LONG positions: TP when price goes up, SL when price goes down
      if (tpPrice && markPrice >= tpPrice) {
        this.closePosition(position.id, 'TP_REACHED');
      } else if (slPrice && markPrice <= slPrice) {
        this.closePosition(position.id, 'SL_REACHED');
      }
    }
  }

  public closePosition(positionId: string, reason: string): void {
    const position = this.positions.get(positionId);
    if (!position) return;

    position.status = 'CLOSED';
    const holdTime = Date.now() - position.openTime;

    console.log(`🔒 Position closed: ${position.symbol} ${position.side} | Reason: ${reason}`);
    console.log(`💰 PnL: ${position.pnl.toFixed(4)} (${position.pnlPercentage.toFixed(2)}%)`);
    console.log(`⏱️ Hold time: ${Math.round(holdTime / 1000)}s`);

    // Update related spike signal
    const relatedSignal = this.spikeSignals.find(s => s.id === position.id.replace('pos_', ''));
    if (relatedSignal) {
      relatedSignal.status = 'COMPLETED';
    }
  }

  // Getters
  public getPositions(): Position[] {
    return Array.from(this.positions.values()).filter(p => p.status === 'OPEN');
  }

  public getSpikeSignals(): SpikeSignal[] {
    return this.spikeSignals.slice(-20); // Last 20 signals
  }

  public updateConfig(newConfig: Partial<TradingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getConfig(): TradingConfig {
    return { ...this.config };
  }

  // Risk Management
  public checkRiskLimits(): boolean {
    const totalPnL = Array.from(this.positions.values())
      .reduce((sum, pos) => sum + pos.pnl, 0);
    
    if (totalPnL <= -this.config.maxDailyLoss) {
      console.error('🚨 Daily loss limit reached! Stopping auto trade.');
      this.config.autoTrade = false;
      return false;
    }

    return true;
  }
}

export const tradingEngine = new TradingEngine({
  leverage: 20,
  tpPercentage: 0.8,
  slPercentage: 0.8,
  maxPositions: 3,
  spikeThreshold: 0.001,
  volumeMultiplier: 2,
  maxDailyLoss: 100,
  autoTrade: false
});

// Financial Detector API Service
export interface FinancialDetectorConfig {
  priceThreshold: number;      // % as decimal (0.01 = 1%)
  slopeThreshold: number;      // Slope value
  volumeMultiplier: number;    // Volume multiplier
  minDataPoints: number;       // Minimum data points
  cooldownPeriod: number;      // Cooldown in ms
}

export interface FinancialDetectorStats {
  symbolsTracked: number;
  dataPoints: number;
  spikesDetected: number;
  lastSpikeTime: number | null;
  config: FinancialDetectorConfig;
}

export const financialDetectorService = {
  // Get current stats
  async getStats(): Promise<FinancialDetectorStats> {
    const response = await fetch('/api/financial-detector/stats');
    if (!response.ok) throw new Error('Failed to get stats');
    return response.json();
  },

  // Update configuration
  async updateConfig(config: Partial<FinancialDetectorConfig>): Promise<{ success: boolean; config: FinancialDetectorConfig }> {
    const response = await fetch('/api/financial-detector/config', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(config)
    });
    if (!response.ok) throw new Error('Failed to update config');
    return response.json();
  },

  // Apply preset
  async applyPreset(presetName: 'conservative' | 'standard' | 'aggressive' | 'scalping'): Promise<{ success: boolean; config: FinancialDetectorConfig }> {
    const response = await fetch(`/api/financial-detector/preset/${presetName}`, {
      method: 'POST'
    });
    if (!response.ok) throw new Error('Failed to apply preset');
    return response.json();
  },

  // Get available presets
  async getPresets(): Promise<{ presets: Record<string, FinancialDetectorConfig> }> {
    const response = await fetch('/api/financial-detector/presets');
    if (!response.ok) throw new Error('Failed to get presets');
    return response.json();
  }
};
