// VERSION: v2.1 - Refresh endpoints fixed
import express from 'express';
import cors from 'cors';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import session from 'express-session';
import dotenv from 'dotenv';
import axios from 'axios';
import crypto from 'crypto';
import WebSocket from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { Server } from 'socket.io';
import { createServer } from 'http';
// Import spike detection algorithms
import { detectSpikeWithLogging } from './algorithms/spike-detection.js';
import { calculateMA } from './algorithms/technical-indicators.js';
import { createFinancialSpikeDetector, SPIKE_PRESETS } from './algorithms/financial-spike-detector.js';
import { SpikeDetector } from './algorithms/spike-detection.js';
// Ticker detector geçici olarak devre dışı (node-binance-api callback sorunu)
// const { TickerSpikeDetector } = require('./algorithms/ticker-spike-detector.js');

// HIGH-PERFORMANCE IMPORTS
import { redisManager } from './utils/redis-manager.js';
import { workerManager } from './utils/worker-manager.js';

// Environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Global variables
const activeConnections = new Map();
const userSettings = new Map();
const activePositions = new Map();
const marketData = new Map();
const spikeDetections = new Map();
const symbolCooldowns = new Map(); // Per-symbol cooldown tracking
let binanceWsConnections = new Map();

// Spike detection data
const recentSpikes = [];

// Initialize Financial Spike Detector
const financialDetector = createFinancialSpikeDetector(SPIKE_PRESETS.STANDARD);

// Initialize Optimized Spike Detector
const spikeDetector = new SpikeDetector({
  algorithm: 'advanced', // Optimize edilmiş algoritma
  cooldownPeriod: 30 * 1000, // 30 saniye cooldown
  symbols: [], // Boş bırakılırsa tüm USDT çiftleri izlenir
  minDataPoints: 40, // MACD için 35+ gerekli
  maxDataPoints: 50
});

// Ticker Spike Detector - Geçici olarak devre dışı (node-binance-api callback sorunu)
let tickerDetector = null;
console.log('⚠️ Ticker detector disabled due to node-binance-api callback issues');
console.log('📊 Using financial detector only for spike detection');

// Listen for financial spikes
financialDetector.on('spike', (spikeData) => {
  console.log('💰 Financial Spike Event:', spikeData);

  // Convert to Turkish format for frontend compatibility
  const turkishSpike = {
    id: `financial_${spikeData.symbol}_${spikeData.timestamp}`,
    sembol: spikeData.symbol,
    zaman: spikeData.timestamp,
    fiyat: spikeData.price,
    ma5: 0, // Not used in financial detector
    ma10: spikeData.ma10,
    egim: spikeData.slope,
    hacim: parseFloat((spikeData.volumeRatio * spikeData.avgVolume).toFixed(2)),
    ortalamHacim: spikeData.avgVolume,
    spikePercent: spikeData.spikePercent,
    volumeRatio: spikeData.volumeRatio,
    trend: spikeData.trend,
    confidence: spikeData.confidence,
    guvenilirlik: spikeData.confidence > 80 ? 'YUKSEK' :
      spikeData.confidence > 60 ? 'ORTA' : 'DUSUK',
    durum: 'FINANCIAL_SPIKE',
    detectorType: 'FINANCIAL'
  };

  // Add to recent spikes
  recentSpikes.unshift(turkishSpike);
  if (recentSpikes.length > 100) {
    recentSpikes.pop();
  }

  // Emit to all connected clients
  io.emit('spike-detected', turkishSpike);

  // Emit to spike analysis rooms
  io.to('spike-analysis').emit('spike-update', {
    spike: turkishSpike,
    totalSpikes: recentSpikes.length
  });

  // 🚀 FINANCIAL DETECTOR İÇİN OTOMATİK TRADİNG
  if (dashboardConfig.otomatikTicaret && dashboardConfig.islemBaslatildi) {
    console.log('🤖 Financial detector - Otomatik trading aktif');

    // Spike kalitesi kontrolü
    const confidence = turkishSpike.confidence || 50;
    const meetsQualityThreshold = confidence >= 70 &&
      turkishSpike.spikePercent >= dashboardConfig.spikeEsigi &&
      turkishSpike.volumeRatio >= dashboardConfig.hacimCarpani;

    if (meetsQualityThreshold) {
      console.log(`✅ Financial spike kalite kriterlerini karşılıyor - Otomatik trading başlatılıyor`);

      // Tüm aktif kullanıcılar için otomatik trading
      for (const [username, settings] of userSettings) {
        if (settings && settings.autoTrade && settings.isActive && dashboardConfig.islemBaslatildi) {
          console.log(`🎯 ${username} için financial spike otomatik trading başlatılıyor...`);
          executeAutoTrade(turkishSpike, username);
        }
      }
    } else {
      console.log(`❌ Financial spike kalite kriterlerini karşılamıyor:`, {
        confidence: confidence,
        spikePercent: turkishSpike.spikePercent,
        volumeRatio: turkishSpike.volumeRatio,
        requiredConfidence: 70,
        requiredSpikePercent: dashboardConfig.spikeEsigi,
        requiredVolumeRatio: dashboardConfig.hacimCarpani
      });
    }
  } else {
    console.log('⏸️ Financial detector - Otomatik trading kapalı veya işlem başlatılmamış');
  }
});

// Listen for kline updates from financial detector
financialDetector.on('kline-update', (klineData) => {
  // Forward kline update to all connected clients for real-time MA10 tracking
  io.emit('kline-update', klineData);
});

// Ticker detector event listeners - Geçici olarak devre dışı
// tickerDetector.on('spike', ...) - node-binance-api callback sorunu nedeniyle devre dışı
// tickerDetector.on('ma10-update', ...) - node-binance-api callback sorunu nedeniyle devre dışı
console.log('📊 Ticker detector events disabled - using financial detector only');

// Start detectors
financialDetector.start();

// Transfer historical data after initialization
const transferHistoricalData = () => {
  console.log('📊 Transferring historical data to Financial Detector...');
  let transferredSymbols = 0;

  for (const [symbol, data] of marketData) {
    if (data.closes && data.closes.length >= 10) {
      // Transfer each historical kline to financial detector
      for (let i = 0; i < data.closes.length; i++) {
        const historicalKline = {
          c: data.closes[i].toString(),
          h: data.highs[i].toString(),
          l: data.lows[i].toString(),
          v: data.volumes[i].toString(),
          t: Date.now() - (data.closes.length - i) * 60000, // Fake timestamps
          T: Date.now() - (data.closes.length - i - 1) * 60000
        };
        financialDetector.addMarketData(symbol, historicalKline);
      }
      transferredSymbols++;
    }
  }

  console.log(`✅ Transferred historical data for ${transferredSymbols} symbols to Financial Detector`);

  // Apply ultra aggressive settings after transfer
  financialDetector.updateConfig({
    priceThreshold: 0.1,
    slopeThreshold: 0.01,
    volumeMultiplier: 1.1,
    minDataPoints: 10,
    cooldownPeriod: 60000
  });

  console.log('🔥 Ultra aggressive settings applied to Financial Detector');
};

// Transfer data after 5 seconds (when historical data is loaded)
setTimeout(transferHistoricalData, 5000);

// Function to update spike detector settings
const updateSpikeDetectorSettings = (username, settings) => {
  console.log(`⚙️ Spike detector ayarları güncellendi: ${username}`, settings);

  // Update financial detector if needed
  if (settings.detectorType === 'financial') {
    financialDetector.updateConfig(settings);
  }
};

// Middleware
app.use(express.json());
app.use(cors());
app.use(session({
  secret: process.env.SESSION_SECRET || 'parabot-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // HTTP için false, HTTPS için true
    maxAge: 24 * 60 * 60 * 1000 // 24 saat
  }
}));

// Users JSON dosyasını oku
const getUsersData = () => {
  try {
    const usersPath = path.join(__dirname, 'users.json');
    const usersData = fs.readFileSync(usersPath, 'utf-8');
    return JSON.parse(usersData);
  } catch (error) {
    console.error('Users.json dosyası okunamadı:', error);
    return { users: [] };
  }
};

// API Credentials - Kullanıcıya özel
const getUserCredentials = (username) => {
  try {
    const usersData = getUsersData();
    const user = usersData.users.find(u => u.username === username);

    console.log(`🔍 getUserCredentials için ${username} - User bulundu:`, !!user);
    if (user) {
      console.log(`🔍 ${username} için binanceConfig:`, !!user.binanceConfig);
      if (user.binanceConfig) {
        console.log(`🔍 ${username} için API Key:`, user.binanceConfig.apiKey ? 'Var' : 'Yok');
        console.log(`🔍 ${username} için Secret Key:`, user.binanceConfig.secretKey ? 'Var' : 'Yok');
        console.log(`🔍 ${username} için API Key değeri:`, user.binanceConfig.apiKey);
        console.log(`🔍 ${username} için Secret Key değeri:`, user.binanceConfig.secretKey);
      }
    }

    if (user && user.binanceConfig && user.binanceConfig.apiKey && user.binanceConfig.secretKey) {
      return {
        apiKey: user.binanceConfig.apiKey,
        secretKey: user.binanceConfig.secretKey,
        testnet: false, // Mainnet kullan
        username
      };
    }

    // Fallback to global config
    const credentialsPath = path.join(__dirname, 'binance-key.json');
    if (fs.existsSync(credentialsPath)) {
      const credentialsData = fs.readFileSync(credentialsPath, 'utf-8');
      const globalConfig = JSON.parse(credentialsData);
      return { ...globalConfig, username };
    }

    return { username };
  } catch (error) {
    console.error('Credentials okuma hatası:', error);
    return { username };
  }
};

// Backward compatibility
const getCredentialsData = () => {
  try {
    const credentialsPath = path.join(__dirname, 'binance-key.json');
    const credentialsData = fs.readFileSync(credentialsPath, 'utf-8');
    return JSON.parse(credentialsData);
  } catch (error) {
    console.error('Binance-key.json dosyası okunamadı:', error);
    return {};
  }
};

// Authentication middleware
const requireAuth = (req, res, next) => {
  console.log(`🔐 requireAuth middleware - URL: ${req.url}`);
  console.log(`🔐 Session exists:`, !!req.session);
  console.log(`🔐 Session user:`, !!req.session?.user);

  if (!req.session.user) {
    console.log(`❌ Session user yok - 401 döndürülüyor`);
    return res.status(401).json({
      success: false,
      error: 'Oturum açmanız gerekiyor'
    });
  }
  console.log(`✅ Authentication başarılı - User: ${req.session.user.username}`);
  next();
};

// Login endpoint
app.post('/api/auth/login', (req, res) => {
  try {
    const { kullaniciAdi, sifre } = req.body;

    if (!kullaniciAdi || !sifre) {
      return res.status(400).json({ error: 'Kullanıcı adı ve şifre gereklidir' });
    }

    const usersData = getUsersData();
    const users = usersData.users;

    const user = users.find(u =>
      u.username === kullaniciAdi &&
      u.password === sifre
    );

    if (user) {
      const { password, ...userWithoutPassword } = user;
      const token = `token_${user.username}_${Date.now()}`;

      // Session'a kullanıcı bilgilerini kaydet
      req.session.user = {
        ...userWithoutPassword,
        sonGiris: Date.now()
      };

      // Kullanıcının API credentials'ını kontrol et
      const userCredentials = getUserCredentials(user.username);
      if (!userCredentials.apiKey || !userCredentials.secretKey) {
        console.warn(`⚠️ ${user.username} için API credentials eksik`);
      }

      // Kullanıcı ayarlarını yükle
      if (!userSettings.has(user.username)) {
        userSettings.set(user.username, {
          maPeriod: 10,
          spikeThreshold: 2.0,
          tradeAmount: 100,
          leverage: 10,
          takeProfitPercent: 1.5,
          stopLossPercent: 2.0,
          maxPositions: 3,
          isActive: false
        });
      }

      res.json({
        success: true,
        user: {
          ...userWithoutPassword,
          sonGiris: Date.now()
        },
        token
      });
    } else {
      res.status(401).json({
        success: false,
        error: 'Kullanıcı adı veya şifre hatalı'
      });
    }
  } catch (error) {
    console.error('Login hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    });
  }
});

// Binance API credentials endpoint with REAL validation
app.get('/api/binance/credentials', async (req, res) => {
  try {
    const credentials = getCredentialsData();

    // GERÇEK API key validation
    let isValid = false;
    let validationError = null;

    if (credentials.apiKey && credentials.secretKey) {
      try {
        // Test API key with account info request
        const testResponse = await binanceRequest('/fapi/v2/account', {}, 'GET', credentials);
        isValid = !!testResponse;
        console.log('✅ API key validation successful');
      } catch (validationErr) {
        validationError = validationErr.message;
        console.log('❌ API key validation failed:', validationError);
      }
    }

    // Güvenlik için sadece varlığını bildir, key'leri gösterme
    res.json({
      success: true,
      hasApiKey: !!credentials.apiKey,
      hasSecretKey: !!credentials.secretKey,
      testnet: credentials.testnet || false,
      isValid: isValid,
      validationError: validationError
    });
  } catch (error) {
    console.error('Credentials okuma hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Credentials okunamadı'
    });
  }
});

// Binance API credentials güncelleme endpoint
app.post('/api/binance/credentials', (req, res) => {
  try {
    const { apiKey, secretKey, testnet } = req.body;

    if (!apiKey || !secretKey) {
      return res.status(400).json({
        success: false,
        error: 'API Key ve Secret Key gereklidir'
      });
    }

    const credentialsData = {
      apiKey,
      secretKey,
      testnet: testnet || false,
      updatedAt: Date.now()
    };

    const credentialsPath = path.join(__dirname, 'binance-key.json');
    fs.writeFileSync(credentialsPath, JSON.stringify(credentialsData, null, 2));

    res.json({
      success: true,
      message: 'Binance API credentials güncellendi',
      testnet: testnet || false
    });
  } catch (error) {
    console.error('Credentials güncelleme hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Credentials güncellenemedi'
    });
  }
});

// Sunucu durumu endpoint
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    message: 'Server çalışıyor',
    timestamp: Date.now(),
    port: PORT
  });
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'Test endpoint çalışıyor',
    server: 'ParaBOT Server',
    version: '1.0.0'
  });
});

// Financial Spike Detector API endpoints
app.get('/api/financial-detector/stats', (req, res) => {
  res.json({
    success: true,
    stats: financialDetector.getStats()
  });
});

app.post('/api/financial-detector/config', (req, res) => {
  try {
    const newConfig = req.body;
    financialDetector.updateConfig(newConfig);
    res.json({
      success: true,
      message: 'Configuration updated',
      config: financialDetector.config
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

app.get('/api/financial-detector/presets', (req, res) => {
  res.json({
    success: true,
    presets: SPIKE_PRESETS
  });
});

// Dashboard trading config endpoint - Ana Panel Ayarları Güncellendi
let dashboardConfig = {
  kaldirac: 20, // 20x sabit
  tpYuzdesi: 0.5, // Default 0.5%
  slYuzdesi: 0.5, // Default 0.5%
  maksPozisyon: 3, // Aynı anda maksimum 3 açık işlem
  maksGunlukIslem: 20, // Default 20, max 300'e kadar ayarlanabilir
  islemMiktari: 0.3, // Default 0.5 USD
  spikeEsigi: 1.1, // Default 1.1% spike yüzdesi
  hacimCarpani: 1.5, // Hacim çarpanı
  maPeriyodu: 10, // MA ayarı default 10
  islemBaslatildi: false
};

app.get('/api/dashboard/config', (req, res) => {
  res.json({
    success: true,
    config: dashboardConfig
  });
});

app.post('/api/dashboard/config', (req, res) => {
  try {
    const newConfig = req.body;
    dashboardConfig = { ...dashboardConfig, ...newConfig };

    console.log('⚙️ Dashboard config updated:', newConfig);

    // 🚀 KRİTİK EKSİKLİK DÜZELTİLDİ: UserSettings Senkronizasyonu
    console.log('🔄 UserSettings senkronize ediliyor...');
    for (const [username, settings] of userSettings) {
      if (settings) {
        // Dashboard config'den userSettings'e aktar
        settings.autoTrade = dashboardConfig.islemBaslatildi || false; // Otomatik ticaret dashboard'dan kontrol ediliyor
        settings.maxPositions = dashboardConfig.maksPozisyon || 3;
        settings.tradeAmount = dashboardConfig.islemMiktari || 5;
        settings.takeProfitPercent = dashboardConfig.tpYuzdesi || 0.5;
        settings.stopLossPercent = dashboardConfig.slYuzdesi || 0.5;
        settings.spikeThreshold = dashboardConfig.spikeEsigi || 1.1;
        settings.volumeMultiplier = dashboardConfig.hacimCarpani || 1.5;
        settings.maPeriyodu = dashboardConfig.maPeriyodu || 10;
        settings.isActive = dashboardConfig.islemBaslatildi || false;

        console.log(`✅ ${username} ayarları güncellendi:`, {
          autoTrade: settings.autoTrade,
          maxPositions: settings.maxPositions,
          tradeAmount: settings.tradeAmount,
          isActive: settings.isActive
        });
      }
    }

    // 🚀 KRİTİK: Financial Detector'ı ZORLA güncelle
    if (financialDetector && financialDetector.updateFromDashboard) {
      console.log('🔄 Financial Detector güncelleniyor...');
      financialDetector.updateFromDashboard(dashboardConfig);
      console.log('✅ Financial Detector güncellendi!');
    } else {
      console.log('❌ Financial Detector bulunamadı!');
    }

    // Ticker detector devre dışı
    if (tickerDetector && tickerDetector.updateFromDashboard) {
      tickerDetector.updateFromDashboard(dashboardConfig);
    }

    // Emit config change to all connected clients
    io.emit('dashboard-config-updated', dashboardConfig);

    res.json({
      success: true,
      message: 'Dashboard configuration updated',
      config: dashboardConfig
    });
  } catch (error) {
    console.error('❌ Dashboard config update error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update dashboard configuration',
      error: error.message
    });
  }
});

app.post('/api/dashboard/trading/start', (req, res) => {
  try {
    dashboardConfig.islemBaslatildi = true;
    console.log('🚀 Dashboard trading started');

    // 🚀 KRİTİK EKSİKLİK DÜZELTİLDİ: UserSettings Aktivasyonu
    console.log('🔄 Tüm kullanıcılar için trading aktifleştiriliyor...');
    for (const [username, settings] of userSettings) {
      if (settings) {
        settings.isActive = true;
        settings.autoTrade = true; // Trading başlatıldığında otomatik ticaret aktif
        console.log(`✅ ${username} trading aktifleştirildi (autoTrade: ${settings.autoTrade})`);
      }
    }

    // Start ticker detector with popular symbols
    const popularSymbols = [
      'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT',
      'DOTUSDT', 'LINKUSDT', 'AVAXUSDT', 'MATICUSDT', 'ATOMUSDT',
      'LTCUSDT', 'BCHUSDT', 'XLMUSDT', 'VETUSDT', 'FILUSDT'
    ];

    // 🚀 KRİTİK: Financial Detector'ı dashboard config ile senkronize et
    if (financialDetector && financialDetector.updateFromDashboard) {
      console.log('🔄 Trading başlatılırken Financial Detector senkronize ediliyor...');
      financialDetector.updateFromDashboard(dashboardConfig);
      console.log('✅ Financial Detector dashboard ile senkronize edildi!');
    }

    console.log('📊 Financial detector aktif - Ticker detector devre dışı');

    io.emit('dashboard-trading-started', dashboardConfig);

    res.json({
      success: true,
      message: 'Trading started',
      config: dashboardConfig
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to start trading',
      error: error.message
    });
  }
});

app.post('/api/dashboard/trading/stop', (req, res) => {
  try {
    dashboardConfig.islemBaslatildi = false;
    console.log('⏸️ Dashboard trading stopped');

    // 🚀 KRİTİK EKSİKLİK DÜZELTİLDİ: UserSettings Deaktivasyonu
    console.log('🔄 Tüm kullanıcılar için trading deaktifleştiriliyor...');
    for (const [username, settings] of userSettings) {
      if (settings) {
        settings.isActive = false;
        settings.autoTrade = false;
        console.log(`⏸️ ${username} trading deaktifleştirildi`);
      }
    }

    // Ticker detector geçici olarak devre dışı
    console.log('📊 Financial detector aktif - Ticker detector devre dışı');

    io.emit('dashboard-trading-stopped', dashboardConfig);

    res.json({
      success: true,
      message: 'Trading stopped',
      config: dashboardConfig
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to stop trading',
      error: error.message
    });
  }
});

app.post('/api/financial-detector/preset/:presetName', (req, res) => {
  try {
    const { presetName } = req.params;
    const preset = SPIKE_PRESETS[presetName.toUpperCase()];

    if (!preset) {
      return res.status(404).json({
        success: false,
        error: 'Preset not found',
        availablePresets: Object.keys(SPIKE_PRESETS)
      });
    }

    financialDetector.updateConfig(preset);
    res.json({
      success: true,
      message: `Applied ${presetName} preset`,
      config: financialDetector.config
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Test spike endpoint
app.post('/api/financial-detector/test-spike/:symbol', (req, res) => {
  try {
    const { symbol } = req.params;

    // Create fake kline data that should trigger a spike
    const testKline = {
      c: '50000.00',  // Close price
      h: '50100.00',  // High price
      l: '49900.00',  // Low price
      v: '1000.00',   // Volume
      t: Date.now(),  // Start time
      T: Date.now() + 60000  // End time
    };

    // Add test data multiple times to build history
    for (let i = 0; i < 15; i++) {
      const price = 49000 + (i * 10); // Gradual price increase
      const volume = 100 + (i * 50);  // Gradual volume increase

      financialDetector.addMarketData(symbol, {
        c: price.toString(),
        h: (price + 5).toString(),
        l: (price - 5).toString(),
        v: volume.toString(),
        t: Date.now() - (15 - i) * 60000,
        T: Date.now() - (14 - i) * 60000
      });
    }

    // Add final spike data
    financialDetector.addMarketData(symbol, testKline);

    res.json({
      success: true,
      message: `Test spike data sent for ${symbol}`,
      testKline
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Debug endpoint for spike detection
app.get('/api/debug/spike-test', (req, res) => {
  const testSymbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT'];
  const results = [];

  testSymbols.forEach(symbol => {
    const symbolData = marketData.get(symbol);
    if (symbolData) {
      const { closes, highs, lows, volumes } = symbolData;
      const result = {
        symbol,
        dataLength: closes.length,
        lastPrice: closes[closes.length - 1],
        volume: volumes[volumes.length - 1],
        ma5: calculateMA(closes, 5),
        ma10: calculateMA(closes, 10),
        ma200: calculateMA(closes, 200),
        hasEnoughData: closes.length >= 30
      };
      results.push(result);
    }
  });

  res.json({
    success: true,
    totalSymbols: MONITORED_SYMBOLS.length,
    marketDataSize: marketData.size,
    testResults: results,
    message: 'Debug bilgileri'
  });
});

// Clear spike cache endpoint
app.post('/api/debug/clear-spikes', (req, res) => {
  spikeDetections.clear();
  symbolCooldowns.clear();

  res.json({
    success: true,
    message: 'Spike cache cleared',
    timestamp: Date.now()
  });
});

// Test specific symbol endpoint
app.get('/api/debug/test-symbol/:symbol', (req, res) => {
  const symbol = req.params.symbol.toUpperCase();
  const symbolData = marketData.get(symbol);

  if (!symbolData) {
    return res.json({
      success: false,
      error: `Symbol ${symbol} not found`,
      availableSymbols: Array.from(marketData.keys()).slice(0, 10)
    });
  }

  const { closes, highs, lows, volumes } = symbolData;
  const ma5 = calculateMA(closes, 5);
  const ma10 = calculateMA(closes, 10);
  const currentPrice = closes[closes.length - 1];
  const currentVolume = volumes[volumes.length - 1];
  const avgVolume = volumes.length >= 20 ?
    volumes.slice(-20).reduce((a, b) => a + b, 0) / 20 : currentVolume;

  // Test spike detection manually
  const mockKline = {
    c: currentPrice.toString(),
    h: (currentPrice * 1.01).toString(),
    l: (currentPrice * 0.99).toString(),
    v: currentVolume.toString()
  };

  const testSpike = detectSpike(symbol, mockKline);

  // Create a test spike with correct format
  const testSpikeData = {
    id: `${symbol}_test_${Date.now()}`,
    sembol: symbol,
    zaman: Date.now(),
    fiyat: currentPrice,
    ma5: ma5 || 0,
    ma10: ma10 || 0,
    egim: 0.5, // Test slope
    hacim: currentVolume,
    ortalamHacim: avgVolume,
    guvenilirlik: 'YUKSEK',
    durum: 'TESPIT_EDILDI'
  };

  res.json({
    success: true,
    symbol,
    data: {
      dataLength: closes.length,
      currentPrice,
      currentVolume,
      ma5,
      ma10,
      avgVolume,
      volumeRatio: avgVolume > 0 ? currentVolume / avgVolume : 0,
      hasEnoughData: closes.length >= 30
    },
    spikeTest: testSpike,
    testSpikeData,
    message: `${symbol} analysis complete`
  });
});

// Force emit test spike
app.post('/api/debug/emit-test-spike/:symbol', (req, res) => {
  const symbol = req.params.symbol.toUpperCase();
  const symbolData = marketData.get(symbol);

  if (!symbolData) {
    return res.json({
      success: false,
      error: `Symbol ${symbol} not found`
    });
  }

  const { closes, volumes } = symbolData;
  const ma5 = calculateMA(closes, 5);
  const ma10 = calculateMA(closes, 10);
  const currentPrice = closes[closes.length - 1];
  const currentVolume = volumes[volumes.length - 1];
  const avgVolume = volumes.length >= 20 ?
    volumes.slice(-20).reduce((a, b) => a + b, 0) / 20 : currentVolume;

  const testSpike = {
    id: `${symbol}_test_${Date.now()}`,
    sembol: symbol,
    zaman: Date.now(),
    fiyat: currentPrice,
    ma5: ma5 || 0,
    ma10: ma10 || 0,
    egim: 1.25, // Test slope
    hacim: currentVolume,
    ortalamHacim: avgVolume,
    guvenilirlik: 'ONAYLANMIS_SPIKE',
    durum: 'TESPIT_EDILDI'
  };

  console.log(`🧪 EMITTING TEST SPIKE:`, testSpike);
  io.emit('spike', testSpike);

  res.json({
    success: true,
    message: `Test spike emitted for ${symbol}`,
    spike: testSpike
  });
});

// Hata yakalama middleware
app.use((err, req, res, next) => {
  console.error('Sunucu hatası:', err);
  res.status(500).json({
    success: false,
    error: 'Sunucu hatası oluştu'
  });
});

// Logout endpoint
app.post('/api/auth/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({
        success: false,
        error: 'Oturum sonlandırılamadı'
      });
    }
    res.json({
      success: true,
      message: 'Oturum sonlandırıldı'
    });
  });
});

// Binance API helper functions
const BINANCE_API_BASE = 'https://fapi.binance.com';
const BINANCE_TESTNET_BASE = 'https://testnet.binancefuture.com';

// Symbol exchange info cache
const symbolInfoCache = new Map();

// Get symbol exchange info
const getSymbolInfo = async (symbol, credentials) => {
  if (symbolInfoCache.has(symbol)) {
    return symbolInfoCache.get(symbol);
  }

  try {
    const exchangeInfo = await binanceRequest('/fapi/v1/exchangeInfo', {}, 'GET', credentials);
    const symbolInfo = exchangeInfo.symbols.find(s => s.symbol === symbol);

    if (symbolInfo) {
      const info = {
        symbol: symbolInfo.symbol,
        status: symbolInfo.status,
        baseAsset: symbolInfo.baseAsset,
        quoteAsset: symbolInfo.quoteAsset,
        pricePrecision: symbolInfo.pricePrecision,
        quantityPrecision: symbolInfo.quantityPrecision,
        tickSize: parseFloat(symbolInfo.filters.find(f => f.filterType === 'PRICE_FILTER')?.tickSize || '0.01'),
        stepSize: parseFloat(symbolInfo.filters.find(f => f.filterType === 'LOT_SIZE')?.stepSize || '0.01'),
        minQty: parseFloat(symbolInfo.filters.find(f => f.filterType === 'LOT_SIZE')?.minQty || '0.01'),
        maxQty: parseFloat(symbolInfo.filters.find(f => f.filterType === 'LOT_SIZE')?.maxQty || '1000000'),
        minNotional: parseFloat(symbolInfo.filters.find(f => f.filterType === 'MIN_NOTIONAL')?.notional || '10')
      };

      symbolInfoCache.set(symbol, info);
      return info;
    }

    throw new Error(`Symbol ${symbol} bulunamadı`);
  } catch (error) {
    console.error('Symbol info alma hatası:', error);
    throw error;
  }
};

// Round price to tick size
const roundToTickSize = (price, tickSize) => {
  const factor = 1 / tickSize;
  return Math.round(price * factor) / factor;
};

// Round quantity to step size
const roundToStepSize = (quantity, stepSize) => {
  const factor = 1 / stepSize;
  return Math.round(quantity * factor) / factor;
};

// HMAC imzalama fonksiyonu
const signRequest = (query, secret) => {
  return crypto.createHmac('sha256', secret).update(query).digest('hex');
};

// Binance API request fonksiyonu
const binanceRequest = async (endpoint, params = {}, method = 'GET', credentials = null) => {
  try {
    if (!credentials) {
      credentials = getCredentialsData();
    }

    if (!credentials.apiKey || !credentials.secretKey) {
      throw new Error('API credentials eksik');
    }

    const baseUrl = credentials.testnet ? BINANCE_TESTNET_BASE : BINANCE_API_BASE;
    const timestamp = Date.now();

    const query = new URLSearchParams({
      ...params,
      timestamp
    }).toString();

    const signature = signRequest(query, credentials.secretKey);
    const signedQuery = `${query}&signature=${signature}`;

    const url = `${baseUrl}${endpoint}?${signedQuery}`;

    const response = await axios({
      method,
      url,
      headers: {
        'X-MBX-APIKEY': credentials.apiKey,
        'Content-Type': 'application/json'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Binance API hatası:', error.response?.data || error.message);
    throw error;
  }
};

// Refresh endpoints moved before 404 handler

// Close position API
app.post('/api/position/close', requireAuth, async (req, res) => {
  try {
    const { symbol, positionId } = req.body;
    const username = req.session.user.username;
    const credentials = getUserCredentials(username);

    if (!symbol && !positionId) {
      return res.status(400).json({
        success: false,
        error: 'Symbol veya positionId gereklidir'
      });
    }

    // Get current position info
    const positions = await binanceRequest('/fapi/v2/positionRisk', {}, 'GET', credentials);
    const position = positions.find(pos =>
      pos.symbol === symbol && parseFloat(pos.positionAmt) !== 0
    );

    if (!position) {
      return res.status(400).json({
        success: false,
        error: 'Açık pozisyon bulunamadı'
      });
    }

    const positionAmt = parseFloat(position.positionAmt);
    const side = positionAmt > 0 ? 'SELL' : 'BUY'; // LONG pozisyon için SELL, SHORT için BUY
    const quantity = Math.abs(positionAmt);

    // Close position with market order
    const closeOrder = await binanceRequest('/fapi/v1/order', {
      symbol: symbol,
      side: side,
      type: 'MARKET',
      quantity: quantity.toString(),
      reduceOnly: true
    }, 'POST', credentials);

    console.log(`✅ ${username}: Position closed for ${symbol} - ${quantity} ${side}`);

    // Cancel any pending TP/SL orders
    try {
      await cancelOCOOrder(symbol, { ...credentials, username });
    } catch (error) {
      console.log(`⚠️ No TP/SL orders to cancel for ${symbol}`);
    }

    res.json({
      success: true,
      message: 'Pozisyon kapatıldı',
      order: closeOrder
    });

  } catch (error) {
    console.error('Pozisyon kapatma hatası:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Pozisyon kapatılamadı'
    });
  }
});

app.get('/api/refresh/test', (req, res) => {
  console.log('🔧 Refresh test endpoint hit');
  res.json({
    success: true,
    message: 'Refresh test endpoint working',
    timestamp: Date.now()
  });
});

// Refresh positions API
console.log('🔧 Registering /api/refresh/positions endpoint');
app.get('/api/refresh/positions', requireAuth, async (req, res) => {
  try {
    console.log('🔄 /api/refresh/positions endpoint çağrıldı');
    
    const username = req.session.user.username;
    console.log(`👤 Username: ${username}`);
    const credentials = getUserCredentials(username);

    if (!credentials) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı credentials bulunamadı'
      });
    }

    console.log(`🔄 ${username}: Refreshing positions from Binance...`);

    const positions = await binanceRequest('/fapi/v2/positionRisk', {}, 'GET', credentials);

    // Filter only positions with size > 0
    const activePositions = positions.filter(pos =>
      parseFloat(pos.positionAmt) !== 0
    );

    console.log(`📊 ${username}: Found ${activePositions.length} active positions`);

    const formattedPositions = activePositions.map(pos => {
      const positionAmt = parseFloat(pos.positionAmt);
      const side = positionAmt > 0 ? 'LONG' : 'SHORT';
      const entryPrice = parseFloat(pos.entryPrice);
      const markPrice = parseFloat(pos.markPrice);
      const unrealizedPnl = parseFloat(pos.unRealizedProfit);
      const pnlPercent = parseFloat(pos.percentage || '0');

      return {
        id: `${pos.symbol}_${Date.now()}`,
        sembol: pos.symbol,
        yon: side,
        miktar: Math.abs(positionAmt),
        girisFiyati: entryPrice,
        guncelFiyat: markPrice,
        pnl: unrealizedPnl,
        pnlYuzdesi: pnlPercent,
        kaldirac: parseFloat(pos.leverage || '20'),
        durum: 'ACIK',
        acilisZamani: Date.now() - (24 * 60 * 60 * 1000),
        stopLoss: null,
        takeProfit: null
      };
    });

    res.json({
      success: true,
      positions: formattedPositions,
      timestamp: Date.now()
    });

  } catch (error) {
    console.error(`❌ Position refresh error:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Pozisyonlar yenilenemedi. Lütfen tekrar deneyin.'
    });
  }
});

// Refresh orders API
console.log('🔧 Registering /api/refresh/orders endpoint');
app.get('/api/refresh/orders', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;
    const credentials = getUserCredentials(username);

    console.log(`🔄 ${username}: Refreshing orders from Binance...`);

    const orders = await binanceRequest('/fapi/v1/openOrders', {}, 'GET', credentials);

    console.log(`📋 ${username}: Found ${orders.length} open orders`);

    const formattedOrders = orders.map(order => ({
      id: order.orderId.toString(),
      sembol: order.symbol,
      tip: order.type === 'LIMIT' ? 'LIMIT' : order.type === 'STOP_MARKET' ? 'STOP_LOSS' : 'MARKET',
      yon: order.side,
      miktar: parseFloat(order.origQty),
      fiyat: parseFloat(order.price || '0'),
      durum: order.status,
      olusturulmaZamani: order.time,
      guncellemeZamani: order.updateTime
    }));

    res.json({
      success: true,
      orders: formattedOrders,
      timestamp: Date.now()
    });

  } catch (error) {
    const username = req.session?.user?.username || 'Unknown';
    console.error(`❌ ${username}: Orders refresh error:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Emirler yenilenemedi. Lütfen tekrar deneyin.'
    });
  }
});

// 404 handler - must be last
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint bulunamadı'
  });
});

// HIGH-PERFORMANCE INITIALIZATION
async function initializeHighPerformanceComponents() {
  console.log('🚀 Initializing high-performance components...');

  try {
    // 1. Initialize Redis Manager
    console.log('🔗 Connecting to Redis...');
    const redisConnected = await redisManager.connect();
    if (redisConnected) {
      console.log('✅ Redis connected successfully');
    } else {
      console.warn('⚠️ Redis connection failed, using in-memory fallback');
    }

    // 2. Initialize Worker Manager
    console.log('🔧 Initializing worker pool...');
    await workerManager.initialize({
      priceThreshold: 0.4,
      slopeThreshold: 0.1,
      volumeMultiplier: 1.5,
      ma10Period: 10,
      volumePeriod: 20,
      minDataPoints: 10
    });

    // 3. Connect worker events to main application
    workerManager.setEventHandler((event, data) => {
      if (event === 'spike-detected') {
        handleWorkerSpikeDetection(data);
      }
    });

    console.log('✅ High-performance components initialized');
    return true;

  } catch (error) {
    console.error('❌ High-performance initialization failed:', error.message);
    console.warn('⚠️ Falling back to standard mode');
    return false;
  }
}

// HIGH-PERFORMANCE KLINE PROCESSING WITH WORKERS
async function processKlineWithWorkers(symbol, kline) {
  try {
    // Check if workers are available
    if (!workerManager.isInitialized) {
      // Fallback to standard spike detection
      const spike = detectSpike(symbol, kline);
      if (spike) {
        handleStandardSpikeDetection(spike);
      }
      return;
    }

    // Check Redis cooldown first (fast check)
    const inCooldown = await redisManager.isInSpikeCooldown(symbol, 30000);
    if (inCooldown) {
      return; // Skip processing if in cooldown
    }

    // Get market data for the symbol
    const symbolData = marketData.get(symbol);
    if (!symbolData || !symbolData.closes || symbolData.closes.length < 10) {
      return; // Not enough data
    }

    // Send to worker for processing
    const success = await workerManager.processSpike(
      symbol,
      kline,
      symbolData,
      { inCooldown }
    );

    if (!success) {
      // Fallback to standard detection if worker failed
      const spike = detectSpike(symbol, kline);
      if (spike) {
        handleStandardSpikeDetection(spike);
      }
    }

  } catch (error) {
    console.error(`❌ Worker kline processing error for ${symbol}:`, error.message);

    // Fallback to standard detection
    try {
      const spike = detectSpike(symbol, kline);
      if (spike) {
        handleStandardSpikeDetection(spike);
      }
    } catch (fallbackError) {
      console.error(`❌ Fallback spike detection failed for ${symbol}:`, fallbackError.message);
    }
  }
}

// SYNC REDIS POSITION COUNT WITH BINANCE
async function syncPositionCount(username, credentials) {
  try {
    const positionsResponse = await binanceRequest('/fapi/v2/positionRisk', {}, 'GET', credentials);

    if (positionsResponse && Array.isArray(positionsResponse)) {
      const realPositionCount = positionsResponse.filter(pos =>
        parseFloat(pos.positionAmt) !== 0
      ).length;

      // Update Redis with real count
      await redisManager.redis?.set(`positions:count:${username}`, realPositionCount);
      console.log(`🔄 ${username}: Position count synced - Real: ${realPositionCount}`);

      return realPositionCount;
    }

    return 0;
  } catch (error) {
    console.error(`❌ ${username}: Position sync failed:`, error.message);
    return await redisManager.getPositionCount(username); // Fallback to Redis
  }
}

// PERIODIC POSITION SYNC FOR ALL USERS
function startPeriodicPositionSync() {
  console.log('🔄 Starting periodic position sync...');

  // Sync every 30 seconds
  setInterval(async () => {
    try {
      for (const [username, settings] of userSettings) {
        if (settings.isActive) {
          const credentials = getUserCredentials(username);
          if (credentials) {
            await syncPositionCount(username, credentials);
          }
        }
      }
    } catch (error) {
      console.error('❌ Periodic position sync error:', error.message);
    }
  }, 30000); // 30 seconds

  console.log('✅ Periodic position sync started (30s interval)');
}

// Handle spike detection from workers
async function handleWorkerSpikeDetection(spike) {
  try {
    console.log(`🎯 Worker detected spike: ${spike.symbol} @ ${spike.price} (${spike.confidence}% confidence)`);

    // Set spike cooldown in Redis
    await redisManager.setSpikeCooldown(spike.symbol, 30000);

    // Record processing metrics
    await redisManager.recordExecutionTime('spike_detection', Date.now() - spike.timestamp);

    // Emit to connected clients (optimized format)
    const spikeSignal = {
      type: 'spike',
      symbol: spike.symbol,
      spikePercent: spike.priceAboveMA,
      price: spike.price,
      ma10: spike.ma10,
      maSlope: spike.ma10Slope,
      volume24h: spike.volume,
      timestamp: spike.timestamp,
      signal: spike.tradeDirection,
      confidence: spike.confidence
    };

    io.emit('spike', spikeSignal);
    io.emit('spike-detected', {
      sembol: spike.symbol,
      fiyat: spike.price,
      ma10: spike.ma10,
      ma10Slope: spike.ma10Slope,
      priceAboveMA: spike.priceAboveMA,
      volume: spike.volume,
      volumeRatio: spike.volumeRatio,
      confidence: spike.confidence,
      timestamp: spike.timestamp,
      spikeType: spike.spikeType,
      tradeDirection: spike.tradeDirection
    });

    // Store spike detection
    spikeDetections.set(`${spike.symbol}_${Date.now()}`, spikeSignal);

    // Execute auto trade if enabled
    await executeAutoTradeFromSpike({
      sembol: spike.symbol,
      fiyat: spike.price,
      confidence: spike.confidence,
      spikeType: spike.spikeType
    });

  } catch (error) {
    console.error('❌ Worker spike handling error:', error.message);
  }
}

// Fallback standard spike detection handler
function handleStandardSpikeDetection(spike) {
  try {
    console.log(`🚨 STANDARD SPIKE DETECTED: ${spike.symbol} at ${spike.price}`);

    // Emit optimized spike signal
    const spikeSignal = {
      type: 'spike',
      symbol: spike.symbol,
      spikePercent: spike.spikePercent,
      price: spike.price,
      ma10: spike.ma10,
      maSlope: spike.maSlope,
      volume24h: spike.volume,
      atr: spike.atr,
      timestamp: spike.timestamp,
      signal: spike.signal,
      confidence: spike.confidence
    };

    io.emit('spike', spikeSignal);

    // Store spike detection
    spikeDetections.set(`${spike.symbol}_${Date.now()}`, spikeSignal);

  } catch (error) {
    console.error('❌ Standard spike handling error:', error.message);
  }
}

// Sunucuyu başlat
server.listen(PORT, async () => {
  console.log(`🚀 ParaBOT Server ${PORT} portunda çalışıyor`);
  console.log(`📡 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🔐 Login endpoint: http://localhost:${PORT}/api/auth/login`);
  console.log(`⚙️  Binance endpoints: http://localhost:${PORT}/api/binance/*`);
  console.log(`🔄 Refresh endpoints: http://localhost:${PORT}/api/refresh/*`);
  console.log(`🌐 WebSocket Server: ws://localhost:${PORT}`);

  // Initialize high-performance components
  const hpInitialized = await initializeHighPerformanceComponents();

  // Start periodic position sync for all users
  startPeriodicPositionSync();

  // List registered routes for debugging
  console.log('\n📋 Registered API Routes:');
  app._router.stack.forEach((middleware) => {
    if (middleware.route) {
      console.log(`   ${Object.keys(middleware.route.methods).join(', ').toUpperCase()} ${middleware.route.path}`);
    }
  });

  // Initialize spike detection after HP components
  if (hpInitialized) {
    console.log('🎯 High-performance spike detection active');
  }
});

// Binance WebSocket Stream Management
const BINANCE_WS_BASE = 'wss://fstream.binance.com/ws/';
const BINANCE_STREAM_BASE = 'wss://fstream.binance.com/stream';

// Dynamic USDT pairs monitoring - will be populated from Binance
let MONITORED_SYMBOLS = [];

// Get all active USDT pairs from Binance
const getAllUSDTSymbols = async () => {
  try {
    console.log('🔍 Tüm USDT çiftleri alınıyor...');

    // Get exchange info without credentials (public endpoint)
    const response = await axios.get('https://fapi.binance.com/fapi/v1/exchangeInfo');
    const exchangeInfo = response.data;

    // Filter active USDT pairs
    const usdtSymbols = exchangeInfo.symbols
      .filter(symbol =>
        symbol.quoteAsset === 'USDT' &&
        symbol.status === 'TRADING' &&
        symbol.contractType === 'PERPETUAL'
      )
      .map(symbol => symbol.symbol)
      .sort();

    console.log(`✅ ${usdtSymbols.length} aktif USDT çifti bulundu`);
    console.log('📋 İlk 10 çift:', usdtSymbols.slice(0, 10).join(', '));

    return usdtSymbols;
  } catch (error) {
    console.error('❌ USDT çiftleri alınamadı:', error.message);

    // Fallback to popular symbols if API fails
    const fallbackSymbols = [
      'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT', 'XRPUSDT',
      'LTCUSDT', 'LINKUSDT', 'BCHUSDT', 'XLMUSDT', 'UNIUSDT', 'VETUSDT',
      'EOSUSDT', 'TRXUSDT', 'ETCUSDT', 'FILUSDT', 'XMRUSDT', 'NEOUSDT',
      'COMPUSDT', 'AAVEUSDT', 'MKRUSDT', 'SNXUSDT', 'YFIUSDT', 'SUSHIUSDT',
      'AVAXUSDT', 'ALGOUSDT', 'ATOMUSDT', 'KSMUSDT', 'EGLDUSDT', 'SOLUSDT'
    ];

    console.log('🔄 Fallback olarak popüler coinler kullanılıyor:', fallbackSymbols.length);
    return fallbackSymbols;
  }
};

// OLD TECHNICAL INDICATORS REMOVED - Now using algorithms/technical-indicators.js



// Real spike confidence calculation
const calculateRealSpikeConfidence = (priceVelocity, volumeMultiplier, rsi, macd) => {
  let confidence = 0;

  // Price velocity scoring (0-40 points)
  if (priceVelocity >= 3) confidence += 40;
  else if (priceVelocity >= 2) confidence += 30;
  else if (priceVelocity >= 1) confidence += 20;

  // Volume scoring (0-30 points)
  if (volumeMultiplier >= 5) confidence += 30;
  else if (volumeMultiplier >= 4) confidence += 25;
  else if (volumeMultiplier >= 3) confidence += 20;

  // RSI scoring (0-15 points)
  if (rsi && rsi >= 80) confidence += 15;
  else if (rsi && rsi >= 70) confidence += 10;

  // MACD scoring (0-15 points)
  if (macd && macd.histogram > 0.01) confidence += 15;
  else if (macd && macd.histogram > 0) confidence += 10;

  return Math.min(100, confidence);
};

// OLD SPIKE DETECTION REMOVED - Now using algorithms/spike-detection.js

// Calculate signal confidence
const calculateConfidence = (priceAboveMA, slope, volumeSpike, priceIncrease) => {
  let confidence = 0;

  if (priceAboveMA > 3) confidence += 25;
  else if (priceAboveMA > 2) confidence += 15;
  else if (priceAboveMA > 1) confidence += 10;

  if (slope > 1) confidence += 25;
  else if (slope > 0.5) confidence += 15;
  else if (slope > 0.3) confidence += 10;

  if (volumeSpike) confidence += 30;
  else confidence += 10;

  if (priceIncrease > 3) confidence += 20;
  else if (priceIncrease > 2) confidence += 15;
  else if (priceIncrease > 1) confidence += 10;

  return Math.min(100, confidence);
};

// Load historical kline data for a symbol - DEPRECATED: Using WebSocket instead
const loadHistoricalData = async (symbol) => {
  console.log(`⚠️ loadHistoricalData deprecated for ${symbol} - using WebSocket kline stream instead`);

  // Initialize empty data structure for WebSocket to fill
  const symbolData = marketData.get(symbol);
  if (symbolData) {
    symbolData.lastUpdate = Date.now();
    console.log(`📊 ${symbol}: Initialized for WebSocket data collection`);
    return true;
  }

  return false;
};

// Initialize market data for symbols - WebSocket based
const initializeMarketData = async () => {
  // Get all USDT symbols first
  MONITORED_SYMBOLS = await getAllUSDTSymbols();

  console.log(`🔧 ${MONITORED_SYMBOLS.length} coin için market data başlatılıyor...`);

  // Initialize empty data structures for WebSocket to fill
  MONITORED_SYMBOLS.forEach(symbol => {
    marketData.set(symbol, {
      closes: [],
      highs: [],
      lows: [],
      volumes: [],
      lastUpdate: 0
    });
  });

  console.log(`📡 WebSocket kline stream ile veri toplanacak (HTTP API kullanılmıyor)`);
  console.log(`🚀 Market data yapıları hazırlandı: ${MONITORED_SYMBOLS.length} coin`);
  console.log(`⏳ WebSocket bağlantısı kurulduğunda veri akışı başlayacak...`);
};

// Connect to Binance WebSocket streams with chunking for large symbol lists
const connectBinanceStreams = () => {
  if (MONITORED_SYMBOLS.length === 0) {
    console.log('⚠️ MONITORED_SYMBOLS boş, bağlantı başlatılamıyor');
    return;
  }

  // Binance WebSocket has a limit on streams per connection (~200)
  // We need to chunk symbols into multiple connections
  const CHUNK_SIZE = 200;
  const chunks = [];

  for (let i = 0; i < MONITORED_SYMBOLS.length; i += CHUNK_SIZE) {
    chunks.push(MONITORED_SYMBOLS.slice(i, i + CHUNK_SIZE));
  }

  console.log(`🔗 ${chunks.length} WebSocket bağlantısı oluşturuluyor...`);

  chunks.forEach((chunk, index) => {
    const streams = chunk.map(symbol =>
      `${symbol.toLowerCase()}@kline_1m`
    ).join('/');

    const wsUrl = `${BINANCE_STREAM_BASE}?streams=${streams}`;

    const ws = new WebSocket(wsUrl);

    ws.on('open', () => {
      console.log(`🔗 WebSocket ${index + 1}/${chunks.length} bağlandı - ${chunk.length} sembol`);
      if (index === 0) {
        console.log(`📊 Toplam ${MONITORED_SYMBOLS.length} USDT çifti takip ediliyor`);
        console.log(`💡 Örnekler: ${MONITORED_SYMBOLS.slice(0, 5).join(', ')}...`);
      }
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        if (message.stream && message.data) {
          const streamName = message.stream;
          const klineData = message.data;

          if (streamName.includes('@kline_1m') && klineData.k) {
            const symbol = klineData.k.s;
            const kline = klineData.k;

            // Only process completed klines
            if (kline.x) {
              updateMarketData(symbol, kline);

              // HIGH-PERFORMANCE SPIKE DETECTION WITH WORKERS
              processKlineWithWorkers(symbol, kline);
            }

            // Emit real-time kline data
            io.emit('kline', {
              symbol,
              price: parseFloat(kline.c),
              volume: parseFloat(kline.v),
              timestamp: kline.t
            });
          }
        }
      } catch (error) {
        console.error('WebSocket message parsing error:', error);
      }
    });

    ws.on('close', () => {
      console.log(`🔌 WebSocket ${index + 1} disconnected, reconnecting...`);
      setTimeout(() => connectBinanceStreams(), 5000);
    });

    ws.on('error', (error) => {
      console.error(`❌ WebSocket ${index + 1} error:`, error);
    });

    binanceWsConnections.set(`stream_${index}`, ws);
  });
};

// Update market data with new kline
const updateMarketData = (symbol, kline) => {
  const symbolData = marketData.get(symbol);
  if (!symbolData) return;

  const close = parseFloat(kline.c);
  const high = parseFloat(kline.h);
  const low = parseFloat(kline.l);
  const volume = parseFloat(kline.v);

  // Add new data
  symbolData.closes.push(close);
  symbolData.highs.push(high);
  symbolData.lows.push(low);
  symbolData.volumes.push(volume);

  // Keep only last 500 candles (for MA200 + buffer)
  const maxLength = 500;
  if (symbolData.closes.length > maxLength) {
    symbolData.closes = symbolData.closes.slice(-maxLength);
    symbolData.highs = symbolData.highs.slice(-maxLength);
    symbolData.lows = symbolData.lows.slice(-maxLength);
    symbolData.volumes = symbolData.volumes.slice(-maxLength);
  }

  symbolData.lastUpdate = Date.now();

  // Feed data to financial spike detector
  financialDetector.addMarketData(symbol, kline);
};

// MANUAL POSITION COUNT RESET ENDPOINT
app.post('/api/positions/reset-count/:username', async (req, res) => {
  try {
    const { username } = req.params;
    const credentials = getUserCredentials(username);

    if (!credentials) {
      return res.status(404).json({
        success: false,
        error: 'User credentials not found'
      });
    }

    // Sync with real Binance positions
    const realCount = await syncPositionCount(username, credentials);

    res.json({
      success: true,
      message: `Position count reset for ${username}`,
      realPositionCount: realCount,
      timestamp: Date.now()
    });

    console.log(`🔄 Manual position reset for ${username}: ${realCount} positions`);

  } catch (error) {
    console.error('❌ Position reset error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// GET CURRENT POSITION COUNT
app.get('/api/positions/count/:username', async (req, res) => {
  try {
    const { username } = req.params;
    const credentials = getUserCredentials(username);

    if (!credentials) {
      return res.status(404).json({
        success: false,
        error: 'User credentials not found'
      });
    }

    const redisCount = await redisManager.getPositionCount(username);
    const realCount = await syncPositionCount(username, credentials);

    res.json({
      success: true,
      redisCount,
      realCount,
      synced: redisCount === realCount,
      timestamp: Date.now()
    });

  } catch (error) {
    console.error('❌ Position count check error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoints for market data
app.get('/api/market/symbols', (req, res) => {
  res.json({
    success: true,
    symbols: MONITORED_SYMBOLS,
    count: MONITORED_SYMBOLS.length
  });
});

app.get('/api/market/data/:symbol', (req, res) => {
  const { symbol } = req.params;
  const symbolData = marketData.get(symbol.toUpperCase());

  if (!symbolData) {
    return res.status(404).json({
      success: false,
      error: 'Symbol not found'
    });
  }

  const closes = symbolData.closes;
  const currentPrice = closes[closes.length - 1] || 0;
  const ma10 = calculateMA(closes, 10);
  const ma200 = calculateMA(closes, 200);
  const maSlope = calculateSlope(closes, 10);

  res.json({
    success: true,
    symbol: symbol.toUpperCase(),
    currentPrice,
    ma10,
    ma200,
    maSlope,
    dataPoints: closes.length,
    lastUpdate: symbolData.lastUpdate
  });
});

app.get('/api/market/spikes', (req, res) => {
  const recent = req.query.recent ? parseInt(req.query.recent) : 10;

  // Return spikes from new spike detector
  const sortedSpikes = recentSpikes.slice(0, recent);

  res.json({
    success: true,
    spikes: sortedSpikes,
    total: recentSpikes.length
  });
});

// OCO (One-Cancels-Other) Order System for SHORT positions with proper sizing
const placeOCOOrder = async (symbol, quantity, entryPrice, credentials) => {
  try {
    const { takeProfitPercent, stopLossPercent } = userSettings.get(credentials.username) || {};

    if (!takeProfitPercent || !stopLossPercent) {
      throw new Error('TP/SL ayarları bulunamadı');
    }

    // Get symbol info for proper sizing
    const symbolInfo = await getSymbolInfo(symbol, credentials);

    const orderId = uuidv4();
    const timestamp = Date.now();

    // Calculate TP and SL prices for SHORT with proper rounding
    const takeProfitPrice = roundToTickSize(entryPrice * (1 - takeProfitPercent / 100), symbolInfo.tickSize);
    const stopLossPrice = roundToTickSize(entryPrice * (1 + stopLossPercent / 100), symbolInfo.tickSize);

    // Round quantity to step size
    const roundedQuantity = roundToStepSize(quantity, symbolInfo.stepSize);

    // Validate minimum quantity and notional
    if (roundedQuantity < symbolInfo.minQty) {
      throw new Error(`Minimum quantity: ${symbolInfo.minQty}`);
    }

    const notional = roundedQuantity * entryPrice;
    if (notional < symbolInfo.minNotional) {
      throw new Error(`Minimum notional: ${symbolInfo.minNotional}`);
    }

    // 1. Place main SHORT order
    const mainOrder = await binanceRequest('/fapi/v1/order', {
      symbol,
      side: 'SELL',
      type: 'MARKET',
      positionSide: 'SHORT',
      quantity: roundedQuantity.toString(),
      reduceOnly: false
    }, 'POST', credentials);

    console.log(`📦 SHORT pozisyon açıldı: ${symbol} - ${roundedQuantity} - ${entryPrice}`);

    // 2. Place Take Profit order
    const tpOrder = await binanceRequest('/fapi/v1/order', {
      symbol,
      side: 'BUY',
      type: 'TAKE_PROFIT_MARKET',
      positionSide: 'SHORT',
      quantity: roundedQuantity.toString(),
      stopPrice: takeProfitPrice.toString(),
      closePosition: true,
      reduceOnly: true,
      workingType: 'CONTRACT_PRICE' // ✅ CONTRACT_PRICE kullan
    }, 'POST', credentials);

    // 3. Place Stop Loss order
    const slOrder = await binanceRequest('/fapi/v1/order', {
      symbol,
      side: 'BUY',
      type: 'STOP_MARKET',
      positionSide: 'SHORT',
      quantity: roundedQuantity.toString(),
      stopPrice: stopLossPrice.toString(),
      closePosition: true,
      reduceOnly: true,
      workingType: 'CONTRACT_PRICE' // ✅ CONTRACT_PRICE kullan
    }, 'POST', credentials);

    // Store position info
    const position = {
      id: orderId,
      symbol,
      side: 'SHORT',
      quantity: roundedQuantity,
      entryPrice,
      takeProfitPrice,
      stopLossPrice,
      mainOrderId: mainOrder.orderId,
      tpOrderId: tpOrder.orderId,
      slOrderId: slOrder.orderId,
      status: 'ACTIVE',
      timestamp,
      username: credentials.username,
      commission: 0, // Will be updated from trade events
      realizedPnl: 0
    };

    activePositions.set(symbol, position);

    // Emit position update to user
    io.to(credentials.username).emit('position-opened', position);

    console.log(`✅ OCO emirler yerleştirildi: TP=${takeProfitPrice} SL=${stopLossPrice}`);

    return position;

  } catch (error) {
    console.error('OCO emir hatası:', error);
    throw error;
  }
};

// Cancel OCO orders
const cancelOCOOrder = async (symbol, credentials) => {
  try {
    const position = activePositions.get(symbol);
    if (!position) {
      throw new Error('Aktif pozisyon bulunamadı');
    }

    // Cancel TP and SL orders
    const cancelPromises = [];

    if (position.tpOrderId) {
      cancelPromises.push(
        binanceRequest('/fapi/v1/order', {
          symbol,
          orderId: position.tpOrderId
        }, 'DELETE', credentials)
      );
    }

    if (position.slOrderId) {
      cancelPromises.push(
        binanceRequest('/fapi/v1/order', {
          symbol,
          orderId: position.slOrderId
        }, 'DELETE', credentials)
      );
    }

    await Promise.all(cancelPromises);

    // Remove from active positions
    activePositions.delete(symbol);

    // Emit position update
    io.to(credentials.username).emit('position-closed', {
      symbol,
      reason: 'MANUAL_CANCEL',
      timestamp: Date.now()
    });

    console.log(`❌ OCO emirler iptal edildi: ${symbol}`);

    return true;

  } catch (error) {
    console.error('OCO iptal hatası:', error);
    throw error;
  }
};

// Order placement API
app.post('/api/order/place', requireAuth, async (req, res) => {
  try {
    const { symbol, quantity, type = 'SPIKE' } = req.body;
    const username = req.session.user.username;
    const credentials = getUserCredentials(username);

    if (!symbol || !quantity) {
      return res.status(400).json({
        success: false,
        error: 'Symbol ve quantity gereklidir'
      });
    }

    // Check if position already exists
    if (activePositions.has(symbol)) {
      return res.status(400).json({
        success: false,
        error: 'Bu sembol için zaten aktif pozisyon var'
      });
    }

    // Get current market price
    const symbolData = marketData.get(symbol);
    if (!symbolData || symbolData.closes.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Market verisi bulunamadı'
      });
    }

    const currentPrice = symbolData.closes[symbolData.closes.length - 1];

    // Place OCO order
    const position = await placeOCOOrder(symbol, quantity, currentPrice, credentials);

    res.json({
      success: true,
      message: 'Emir yerleştirildi',
      position
    });

  } catch (error) {
    console.error('Emir verme hatası:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Emir verilemedi'
    });
  }
});

// Cancel specific order API - REAL BINANCE CANCELLATION
app.post('/api/order/cancel', requireAuth, async (req, res) => {
  try {
    const { orderId, symbol } = req.body;
    const username = req.session.user.username;
    const credentials = getUserCredentials(username);

    if (!orderId || !symbol) {
      return res.status(400).json({
        success: false,
        error: 'OrderId ve Symbol gereklidir'
      });
    }

    console.log(`🗑️ ${username}: Cancelling order ${orderId} for ${symbol}`);

    // REAL Binance order cancellation
    const cancelResult = await binanceRequest('/fapi/v1/order', {
      symbol: symbol,
      orderId: orderId
    }, 'DELETE', credentials);

    console.log(`✅ ${username}: Order ${orderId} cancelled successfully`);

    res.json({
      success: true,
      message: 'Emir başarıyla iptal edildi',
      cancelledOrder: cancelResult
    });

  } catch (error) {
    console.error(`❌ Order cancellation error for ${req.body.orderId}:`, error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: error.response?.data?.msg || error.message || 'Emir iptal edilemedi'
    });
  }
});

// Cancel all orders API - REAL BINANCE CANCELLATION
app.post('/api/orders/cancel-all', requireAuth, async (req, res) => {
  try {
    const { symbol } = req.body; // Optional: cancel all orders for specific symbol
    const username = req.session.user.username;
    const credentials = getUserCredentials(username);

    console.log(`🗑️ ${username}: Cancelling all orders${symbol ? ` for ${symbol}` : ''}`);

    let cancelResult;

    if (symbol) {
      // Cancel all orders for specific symbol
      cancelResult = await binanceRequest('/fapi/v1/openOrders', {
        symbol: symbol
      }, 'DELETE', credentials);
    } else {
      // Cancel all open orders
      cancelResult = await binanceRequest('/fapi/v1/allOpenOrders', {}, 'DELETE', credentials);
    }

    console.log(`✅ ${username}: All orders cancelled successfully`);

    res.json({
      success: true,
      message: symbol ? `${symbol} için tüm emirler iptal edildi` : 'Tüm emirler iptal edildi',
      cancelledOrders: cancelResult
    });

  } catch (error) {
    console.error(`❌ Cancel all orders error:`, error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: error.response?.data?.msg || error.message || 'Emirler iptal edilemedi'
    });
  }
});

// Refresh orders API
console.log('🔧 Registering /api/refresh/orders endpoint');
app.get('/api/refresh/orders', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;
    const credentials = getUserCredentials(username);

    console.log(`🔄 ${username}: Refreshing orders from Binance...`);

    const orders = await binanceRequest('/fapi/v1/openOrders', {}, 'GET', credentials);

    console.log(`📋 ${username}: Found ${orders.length} open orders`);

    const formattedOrders = orders.map(order => ({
      id: order.orderId.toString(),
      sembol: order.symbol,
      tip: order.type === 'LIMIT' ? 'LIMIT' : order.type === 'STOP_MARKET' ? 'STOP_LOSS' : 'MARKET',
      yon: order.side,
      miktar: parseFloat(order.origQty),
      fiyat: parseFloat(order.price || '0'),
      durum: order.status,
      olusturulmaZamani: order.time,
      guncellemeZamani: order.updateTime
    }));

    res.json({
      success: true,
      orders: formattedOrders,
      timestamp: Date.now()
    });

  } catch (error) {
    const username = req.session?.user?.username || 'Unknown';
    console.error(`❌ ${username}: Orders refresh error:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Emirler yenilenemedi. Lütfen tekrar deneyin.'
    });
  }
});

// Get active positions
app.get('/api/positions', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;
    const userPositions = Array.from(activePositions.values())
      .filter(pos => pos.username === username);

    res.json({
      success: true,
      positions: userPositions,
      count: userPositions.length
    });

  } catch (error) {
    console.error('Pozisyon listeleme hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Pozisyonlar getirilemedi'
    });
  }
});

// 🚀 YENİ: Gelişmiş Otomatik Trading Fonksiyonu
const executeAutoTrade = async (spike, username) => {
  try {
    console.log(`🤖 ${username} için otomatik trading başlatılıyor...`);

    const settings = userSettings.get(username);
    if (!settings || !settings.isActive) {
      console.log(`❌ ${username} için ayarlar bulunamadı veya aktif değil`);
      return;
    }

    const credentials = getUserCredentials(username);
    if (!credentials) {
      console.log(`❌ ${username} için API credentials bulunamadı`);
      return;
    }

    // Pozisyon limiti kontrolü
    const userPositions = Array.from(activePositions.values())
      .filter(pos => pos.username === username && pos.status === 'ACTIVE');

    const maxPositions = dashboardConfig.maksPozisyon || settings.maxPositions || 3;
    if (userPositions.length >= maxPositions) {
      console.log(`⚠️ ${username} için pozisyon limiti aşıldı (${userPositions.length}/${maxPositions})`);
      return;
    }

    // Aynı sembol için aktif pozisyon kontrolü
    const existingPosition = userPositions.find(pos => pos.symbol === spike.sembol);
    if (existingPosition) {
      console.log(`⚠️ ${spike.sembol} için zaten aktif pozisyon var`);
      return;
    }

    // İşlem miktarı hesaplama (Dashboard config'den al)
    const tradeAmount = dashboardConfig.islemMiktari || settings.tradeAmount || 100;

    console.log(`💰 İşlem detayları:`, {
      symbol: spike.sembol,
      price: spike.fiyat,
      amount: tradeAmount,
      confidence: spike.confidence,
      username: username
    });

    // Execute spike trade with ultra-fast TP/SL
    const tradeParams = {
      symbol: spike.sembol,
      side: 'SHORT',
      quantity: tradeAmount,
      leverage: 20,
      stopLossPercent: 0.5,
      takeProfitPercent: 0.5,
      spikePrice: spike.fiyat,
      spikeType: 'YUKSEK'
    };

    const position = await executeSpikeTrade(username, tradeParams);

    if (position) {
      console.log(`✅ ${username} için otomatik işlem başarılı: ${spike.sembol}`);

      // Trading log ekle
      addTradingLog(username, 'SUCCESS',
        `Otomatik işlem açıldı: ${spike.sembol} @ $${spike.fiyat}`,
        { spike, position });

      // Kullanıcıya bildirim gönder
      io.to(username).emit('auto-trade-executed', {
        spike,
        position,
        timestamp: Date.now(),
        message: `${spike.sembol} için otomatik SHORT pozisyon açıldı`
      });
    }

  } catch (error) {
    console.error(`❌ ${username} için otomatik trading hatası:`, error);

    addTradingLog(username, 'ERROR',
      `Otomatik işlem hatası: ${spike.sembol} - ${error.message}`,
      { spike, error: error.message });

    // Hata bildirimini kullanıcıya gönder
    io.to(username).emit('auto-trade-error', {
      spike,
      error: error.message,
      timestamp: Date.now()
    });
  }
};

// Eski autoTrade fonksiyonu (geriye uyumluluk için)
const autoTrade = async (spike, username) => {
  return executeAutoTrade(spike, username);
};

// UserDataStream Management
const userDataStreams = new Map();

// Get listen key for user data stream
const getListenKey = async (credentials) => {
  try {
    const response = await binanceRequest('/fapi/v1/listenKey', {}, 'POST', credentials);
    return response.listenKey;
  } catch (error) {
    console.error('Listen key alma hatası:', error);
    throw error;
  }
};

// Keep alive listen key
const keepAliveListenKey = async (credentials) => {
  try {
    await binanceRequest('/fapi/v1/listenKey', {}, 'PUT', credentials);
    console.log(`🔄 Listen key yenilendi: ${credentials.username}`);
  } catch (error) {
    console.error('Listen key yenileme hatası:', error);
    // Try to restart stream if keep alive fails
    setTimeout(() => {
      startUserDataStream(credentials.username);
    }, 5000);
  }
};

// Start user data stream with improved error handling
const startUserDataStream = async (username) => {
  try {
    // Check if stream already exists
    const existingStream = userDataStreams.get(username);
    if (existingStream) {
      console.log(`⚠️ Stream zaten mevcut: ${username}`);
      return;
    }

    const credentials = getUserCredentials(username);

    if (!credentials.apiKey || !credentials.secretKey) {
      throw new Error('API credentials eksik');
    }

    const listenKey = await getListenKey(credentials);
    const baseUrl = credentials.testnet ?
      'wss://stream.binancefuture.com/ws/' :
      'wss://fstream.binance.com/ws/';

    const ws = new WebSocket(`${baseUrl}${listenKey}`);

    ws.on('open', () => {
      console.log(`📡 User data stream açıldı: ${username}`);
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        handleUserDataUpdate(username, message);
      } catch (error) {
        console.error('User data stream mesaj hatası:', error);
      }
    });

    ws.on('close', (code, reason) => {
      console.log(`🔌 User data stream kapandı: ${username} - Code: ${code}, Reason: ${reason}`);

      // Clean up existing stream
      const existingStream = userDataStreams.get(username);
      if (existingStream) {
        clearInterval(existingStream.keepAliveInterval);
        userDataStreams.delete(username);
      }

      // Reconnect after 5 seconds (only if not manual disconnect)
      if (code !== 1000) {
        setTimeout(() => {
          console.log(`🔄 Reconnecting user data stream: ${username}`);
          startUserDataStream(username);
        }, 5000);
      }
    });

    ws.on('error', (error) => {
      console.error(`❌ User data stream hatası: ${username}`, error);

      // Clean up and restart on error
      const existingStream = userDataStreams.get(username);
      if (existingStream) {
        clearInterval(existingStream.keepAliveInterval);
        userDataStreams.delete(username);
      }

      // Restart after error
      setTimeout(() => {
        console.log(`🔄 Restarting user data stream after error: ${username}`);
        startUserDataStream(username);
      }, 10000);
    });

    // Keep alive interval (every 30 minutes)
    const keepAliveInterval = setInterval(async () => {
      try {
        await keepAliveListenKey(credentials);
      } catch (error) {
        console.error('Keep alive hatası:', error);
      }
    }, 30 * 60 * 1000);

    userDataStreams.set(username, {
      ws,
      listenKey,
      keepAliveInterval,
      credentials,
      startTime: Date.now()
    });

  } catch (error) {
    console.error(`❌ User data stream başlatma hatası: ${username}`, error);

    // Retry after 15 seconds
    setTimeout(() => {
      console.log(`🔄 Retrying user data stream: ${username}`);
      startUserDataStream(username);
    }, 15000);
  }
};

// Stop user data stream
const stopUserDataStream = (username) => {
  const stream = userDataStreams.get(username);
  if (stream) {
    stream.ws.close();
    clearInterval(stream.keepAliveInterval);
    userDataStreams.delete(username);
    console.log(`❌ User data stream durduruldu: ${username}`);
  }
};

// Handle user data stream updates
const handleUserDataUpdate = (username, message) => {
  const { e: eventType } = message;

  switch (eventType) {
    case 'ORDER_TRADE_UPDATE':
      handleOrderUpdate(username, message);
      break;
    case 'ACCOUNT_UPDATE':
      handleAccountUpdate(username, message);
      break;
    case 'MARGIN_CALL':
      handleMarginCall(username, message);
      break;
    default:
      console.log(`Bilinmeyen event type: ${eventType}`);
  }
};

// Handle order updates with double execution prevention
const handleOrderUpdate = (username, message) => {
  const order = message.o;
  const { s: symbol, X: orderStatus, i: orderId, S: side, o: orderType, z: executedQty, n: commission } = order;

  console.log(`📋 Order update: ${symbol} - ${orderStatus} - ${orderType}`);

  // Check if this is a TP or SL order
  const position = activePositions.get(symbol);
  if (position && position.username === username) {

    if (orderStatus === 'FILLED') {
      // Prevent double execution
      if (position.status === 'CLOSING' || position.status === 'CLOSED') {
        console.log(`⚠️ Position already closing/closed: ${symbol}`);
        return;
      }

      // Mark position as closing to prevent double execution
      position.status = 'CLOSING';

      // Update commission if available
      if (commission) {
        position.commission = (position.commission || 0) + parseFloat(commission);
      }

      // TP or SL was triggered
      if (orderId === position.tpOrderId) {
        console.log(`✅ Take Profit tetiklendi: ${symbol}`);

        // Cancel SL order immediately
        cancelBinanceOrder(symbol, position.slOrderId, getUserCredentials(username));

        // Update position
        position.status = 'CLOSED';
        position.closeReason = 'TAKE_PROFIT';
        position.closeTime = Date.now();
        position.actualClosePrice = parseFloat(order.ap || position.takeProfitPrice);

        // Calculate final PnL
        const pnlResult = calculateProfit(position, position.actualClosePrice);
        position.realizedPnl = pnlResult.netPnl;

        // Remove from active positions
        activePositions.delete(symbol);

        // Emit to user
        io.to(username).emit('position-closed', {
          symbol,
          reason: 'TAKE_PROFIT',
          pnl: pnlResult,
          position,
          timestamp: Date.now()
        });

      } else if (orderId === position.slOrderId) {
        console.log(`❌ Stop Loss tetiklendi: ${symbol}`);

        // Cancel TP order immediately
        cancelBinanceOrder(symbol, position.tpOrderId, getUserCredentials(username));

        // Update position
        position.status = 'CLOSED';
        position.closeReason = 'STOP_LOSS';
        position.closeTime = Date.now();
        position.actualClosePrice = parseFloat(order.ap || position.stopLossPrice);

        // Calculate final PnL
        const pnlResult = calculateProfit(position, position.actualClosePrice);
        position.realizedPnl = pnlResult.netPnl;

        // Remove from active positions
        activePositions.delete(symbol);

        // Emit to user
        io.to(username).emit('position-closed', {
          symbol,
          reason: 'STOP_LOSS',
          pnl: pnlResult,
          position,
          timestamp: Date.now()
        });
      }
    }
  }

  // Emit detailed order update
  io.to(username).emit('order-update', {
    symbol,
    orderId,
    type: orderType,
    side,
    status: orderStatus,
    quantity: parseFloat(order.q || 0),
    price: parseFloat(order.p || 0),
    executedQty: parseFloat(executedQty || 0),
    commission: parseFloat(commission || 0),
    timestamp: Date.now(),
    updateTime: parseInt(order.T || Date.now())
  });

  // Also emit to refresh open orders list
  refreshUserOrders(username);
};

// Symbol precision information endpoint
app.get('/api/market/exchange-info', async (req, res) => {
  try {
    console.log('📊 Fetching exchange info from Binance...');

    // Get exchange information including symbol precision
    const response = await axios.get('https://fapi.binance.com/fapi/v1/exchangeInfo');

    if (response.data && response.data.symbols) {
      // Extract precision information for each symbol
      const symbolPrecision = {};
      response.data.symbols.forEach(symbol => {
        if (symbol.symbol.endsWith('USDT')) {
          symbolPrecision[symbol.symbol] = {
            pricePrecision: symbol.pricePrecision,
            quantityPrecision: symbol.quantityPrecision,
            baseAssetPrecision: symbol.baseAssetPrecision,
            quotePrecision: symbol.quotePrecision
          };
        }
      });

      console.log(`✅ Fetched precision info for ${Object.keys(symbolPrecision).length} symbols`);

      res.json({
        success: true,
        symbolPrecision: symbolPrecision,
        timestamp: Date.now()
      });
    } else {
      throw new Error('Invalid response format from Binance');
    }

  } catch (error) {
    console.error('❌ Exchange info fetch error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Exchange info alınamadı. Binance API bağlantısını kontrol ediniz.',
      timestamp: Date.now()
    });
  }
});

// REAL Market Data API Endpoint
app.get('/api/market/ticker-24hr', async (req, res) => {
  try {
    console.log('📊 Fetching real market data from Binance...');

    // Get 24hr ticker statistics from Binance
    const response = await axios.get('https://fapi.binance.com/fapi/v1/ticker/24hr');

    if (response.data && Array.isArray(response.data)) {
      console.log(`✅ Fetched ${response.data.length} ticker data from Binance`);

      res.json({
        success: true,
        tickers: response.data,
        timestamp: Date.now()
      });
    } else {
      throw new Error('Invalid response format from Binance');
    }

  } catch (error) {
    console.error('❌ Market data fetch error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Market data alınamadı. Binance API bağlantısını kontrol ediniz.',
      timestamp: Date.now()
    });
  }
});

// Trading execution endpoint for frontend
app.post('/api/trading/execute-spike-trade', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;
    const tradeParams = req.body;

    console.log(`🚀 Frontend trade request from ${username}:`, tradeParams);

    // Execute the trade using existing backend function
    const result = await executeSpikeTrade(username, tradeParams);

    res.json({
      success: true,
      orderId: result.orderId,
      executedPrice: result.avgPrice || result.price,
      quantity: result.origQty,
      timestamp: Date.now()
    });

  } catch (error) {
    console.error(`❌ Frontend trade execution error:`, error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: Date.now()
    });
  }
});

// Refresh user orders function
const refreshUserOrders = async (username) => {
  try {
    const credentials = getUserCredentials(username);
    if (!credentials) return;

    const baseUrl = credentials.testnet ? BINANCE_TESTNET_BASE : BINANCE_API_BASE;
    const timestamp = Date.now();
    const recvWindow = 60000;

    // Create query string
    const queryString = `timestamp=${timestamp}&recvWindow=${recvWindow}`;

    // Create signature
    const signature = crypto
      .createHmac('sha256', credentials.secretKey)
      .update(queryString)
      .digest('hex');

    // Get open orders
    const response = await axios.get(`${baseUrl}/fapi/v1/openOrders`, {
      headers: {
        'X-MBX-APIKEY': credentials.apiKey
      },
      params: {
        timestamp,
        recvWindow,
        signature
      }
    });

    const openOrders = response.data;

    // Format orders for frontend
    const formattedOrders = openOrders.map(order => ({
      i: order.orderId,
      s: order.symbol,
      o: order.type,
      S: order.side,
      q: order.origQty,
      p: order.price,
      X: order.status,
      T: order.time,
      z: order.executedQty
    }));

    // Emit orders update
    io.to(username).emit('orders-update', {
      orders: formattedOrders,
      timestamp: Date.now()
    });

  } catch (error) {
    console.error(`❌ Error refreshing orders for ${username}:`, error.message);
  }
};

// Handle account updates
const handleAccountUpdate = (username, message) => {
  const account = message.a;
  const { B: balances, P: positions } = account;

  // Emit balance update
  if (balances) {
    const usdtBalance = balances.find(b => b.a === 'USDT');
    if (usdtBalance) {
      io.to(username).emit('balance-update', {
        asset: 'USDT',
        balance: parseFloat(usdtBalance.wb),
        availableBalance: parseFloat(usdtBalance.cw),
        timestamp: Date.now()
      });
    }
  }

  // Emit position updates with detailed formatting
  if (positions) {
    const activePos = positions.filter(p => parseFloat(p.pa) !== 0);
    const formattedPositions = activePos.map(pos => ({
      s: pos.s,           // symbol
      pa: pos.pa,         // position amount
      ep: pos.ep,         // entry price
      mp: pos.mp,         // mark price
      up: pos.up,         // unrealized PnL
      mt: pos.mt,         // margin type
      iw: pos.iw,         // isolated wallet
      ps: pos.ps,         // position side
      l: pos.l || '20'    // leverage
    }));

    io.to(username).emit('positions-update', {
      positions: formattedPositions,
      timestamp: Date.now()
    });

    // Also emit individual position updates for real-time tracking
    formattedPositions.forEach(pos => {
      io.to(username).emit('position-update', {
        symbol: pos.s,
        positionAmt: parseFloat(pos.pa),
        entryPrice: parseFloat(pos.ep),
        markPrice: parseFloat(pos.mp),
        unRealizedProfit: parseFloat(pos.up),
        leverage: parseInt(pos.l),
        positionSide: pos.ps,
        timestamp: Date.now()
      });
    });
  }
};

// Handle margin calls
const handleMarginCall = (username, message) => {
  console.log(`⚠️ Margin call: ${username}`);

  io.to(username).emit('margin-call', {
    message: 'Margin call alarmı!',
    timestamp: Date.now(),
    data: message
  });
};

// Cancel Binance order helper
const cancelBinanceOrder = async (symbol, orderId, credentials) => {
  try {
    await binanceRequest('/fapi/v1/order', {
      symbol,
      orderId
    }, 'DELETE', credentials);
  } catch (error) {
    console.error('Emir iptal hatası:', error);
  }
};

// Calculate profit with commission
const calculateProfit = (position, actualClosePrice = null) => {
  const entryPrice = position.entryPrice;
  const closePrice = actualClosePrice || (position.closeReason === 'TAKE_PROFIT' ?
    position.takeProfitPrice : position.stopLossPrice);

  // For SHORT positions: profit = (entry - close) * quantity
  const grossProfit = (entryPrice - closePrice) * position.quantity;

  // Calculate commission (typical futures commission: 0.04% maker, 0.04% taker)
  const commission = position.commission ||
    (position.quantity * entryPrice * 0.0004) + (position.quantity * closePrice * 0.0004);

  // Net PnL = Gross PnL - Commission
  const netPnl = grossProfit - commission;

  return {
    grossProfit,
    commission,
    netPnl
  };
};

// Account balance API
app.get('/api/account/balance', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;
    console.log(`💰 Balance API çağrıldı - Username: ${username}`);
    const credentials = getUserCredentials(username);

    // Check if API credentials are valid
    if (!credentials.apiKey || !credentials.secretKey) {
      console.log(`❌ API credentials eksik - Username: ${username}`);
      return res.json({
        success: false,
        error: 'API credentials eksik'
      });
    }

    try {
      const accountInfo = await binanceRequest('/fapi/v2/account', {}, 'GET', credentials);

      const usdtBalance = accountInfo.assets.find(asset => asset.asset === 'USDT');

      if (!usdtBalance) {
        return res.json({
          success: false,
          error: 'USDT bakiyesi bulunamadı'
        });
      }

      res.json({
        success: true,
        balance: {
          asset: 'USDT',
          walletBalance: parseFloat(usdtBalance.walletBalance),
          availableBalance: parseFloat(usdtBalance.availableBalance),
          crossUnPnl: parseFloat(usdtBalance.crossUnPnl)
        }
      });
    } catch (apiError) {
      console.error('Binance API hatası:', apiError.response?.data || apiError.message);

      // API key invalid olduğunda hata döndür - DEMO YOK!
      if (apiError.response?.status === 401) {
        console.log('❌ API key geçersiz - Lütfen geçerli API key giriniz');
        res.status(401).json({
          success: false,
          error: 'API key geçersiz. Lütfen Binance API ayarlarınızı kontrol ediniz.',
          needsApiKey: true
        });
      } else {
        throw apiError;
      }
    }

  } catch (error) {
    console.error('Bakiye getirme hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Bakiye getirilemedi'
    });
  }
});

// Account positions API
app.get('/api/account/positions', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;
    const credentials = getUserCredentials(username);

    const positions = await binanceRequest('/fapi/v2/positionRisk', {}, 'GET', credentials);

    // Filter only positions with size > 0
    const activePositions = positions.filter(pos =>
      parseFloat(pos.positionAmt) !== 0
    );

    res.json({
      success: true,
      positions: activePositions.map(pos => ({
        symbol: pos.symbol,
        positionAmt: parseFloat(pos.positionAmt),
        entryPrice: parseFloat(pos.entryPrice),
        markPrice: parseFloat(pos.markPrice),
        unRealizedProfit: parseFloat(pos.unRealizedProfit),
        positionSide: pos.positionSide
      }))
    });

  } catch (error) {
    console.error('Pozisyon getirme hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Pozisyonlar getirilemedi'
    });
  }
});

// Start user data stream API
app.post('/api/stream/start', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;

    if (userDataStreams.has(username)) {
      return res.json({
        success: true,
        message: 'Stream zaten aktif'
      });
    }

    await startUserDataStream(username);

    res.json({
      success: true,
      message: 'User data stream başlatıldı'
    });

  } catch (error) {
    console.error('Stream başlatma hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Stream başlatılamadı'
    });
  }
});

// Stop user data stream API
app.post('/api/stream/stop', requireAuth, async (req, res) => {
  try {
    const username = req.session.user.username;

    stopUserDataStream(username);

    res.json({
      success: true,
      message: 'User data stream durduruldu'
    });

  } catch (error) {
    console.error('Stream durdurma hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Stream durdurulamadı'
    });
  }
});

// Trading logs for each user
const tradingLogs = new Map();

// Add log entry
const addTradingLog = (username, type, message, data = null) => {
  if (!tradingLogs.has(username)) {
    tradingLogs.set(username, []);
  }

  const logs = tradingLogs.get(username);
  const logEntry = {
    id: uuidv4(),
    timestamp: Date.now(),
    type, // 'INFO', 'SUCCESS', 'WARNING', 'ERROR', 'SPIKE', 'ORDER', 'POSITION'
    message,
    data
  };

  logs.push(logEntry);

  // Keep only last 1000 logs
  if (logs.length > 1000) {
    logs.splice(0, logs.length - 1000);
  }

  // Emit to user
  io.to(username).emit('trading-log', logEntry);
};

// Get trading logs API
app.get('/api/logs', requireAuth, (req, res) => {
  try {
    const username = req.session.user.username;
    const limit = parseInt(req.query.limit) || 100;
    const type = req.query.type;

    const logs = tradingLogs.get(username) || [];

    let filteredLogs = logs;
    if (type) {
      filteredLogs = logs.filter(log => log.type === type);
    }

    const recentLogs = filteredLogs.slice(-limit).reverse();

    res.json({
      success: true,
      logs: recentLogs,
      total: filteredLogs.length
    });
  } catch (error) {
    console.error('Log getirme hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Loglar getirilemedi'
    });
  }
});

// Clear logs API
app.post('/api/logs/clear', requireAuth, (req, res) => {
  try {
    const username = req.session.user.username;
    tradingLogs.set(username, []);

    res.json({
      success: true,
      message: 'Loglar temizlendi'
    });
  } catch (error) {
    console.error('Log temizleme hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Loglar temizlenemedi'
    });
  }
});

// OLD detectSpikeWithLogging REMOVED - Now using algorithms/spike-detection.js

// Enhanced auto trade with logging
const autoTradeWithLogging = async (spike, username) => {
  try {
    addTradingLog(username, 'INFO',
      `Otomatik işlem başlatılıyor: ${spike.symbol}`,
      spike);

    const settings = userSettings.get(username);
    if (!settings || !settings.isActive) {
      addTradingLog(username, 'WARNING',
        'Otomatik işlem aktif değil');
      return;
    }

    const credentials = getUserCredentials(username);

    // Check position limits
    const userPositions = Array.from(activePositions.values())
      .filter(pos => pos.username === username);

    if (userPositions.length >= settings.maxPositions) {
      addTradingLog(username, 'WARNING',
        `Pozisyon limiti aşıldı: ${userPositions.length}/${settings.maxPositions}`);
      return;
    }

    // Execute spike trade with ultra-fast TP/SL
    const tradeParams = {
      symbol: spike.symbol,
      side: 'SHORT',
      quantity: settings.tradeAmount,
      leverage: settings.leverage || 20,
      stopLossPercent: settings.stopLossPercent || 0.5,
      takeProfitPercent: settings.takeProfitPercent || 0.5,
      spikePrice: spike.price,
      spikeType: 'YUKSEK'
    };

    const position = await executeSpikeTrade(username, tradeParams);

    addTradingLog(username, 'SUCCESS',
      `Otomatik işlem gerçekleştirildi: ${spike.symbol}`,
      { spike, position });

    // Emit notification
    io.to(username).emit('auto-trade', {
      spike,
      position,
      timestamp: Date.now()
    });

  } catch (error) {
    console.error('Otomatik işlem hatası:', error);
    addTradingLog(username, 'ERROR',
      `Otomatik işlem hatası: ${spike.symbol} - ${error.message}`,
      { spike, error: error.message });
  }
};

// Order history for each user
const orderHistory = new Map();

// Add order to history
const addOrderHistory = (username, order) => {
  if (!orderHistory.has(username)) {
    orderHistory.set(username, []);
  }

  const orders = orderHistory.get(username);
  orders.push({
    ...order,
    timestamp: Date.now()
  });

  // Keep only last 500 orders
  if (orders.length > 500) {
    orders.splice(0, orders.length - 500);
  }
};

// Get order history API
app.get('/api/orders/history', requireAuth, (req, res) => {
  try {
    const username = req.session.user.username;
    const limit = parseInt(req.query.limit) || 50;

    const orders = orderHistory.get(username) || [];
    const recentOrders = orders.slice(-limit).reverse();

    res.json({
      success: true,
      orders: recentOrders,
      total: orders.length
    });
  } catch (error) {
    console.error('Order history getirme hatası:', error);
    res.status(500).json({
      success: false,
      error: 'Order history getirilemedi'
    });
  }
});

// Update WebSocket message handler to use logging with chunking
const connectBinanceStreamsWithLogging = () => {
  if (MONITORED_SYMBOLS.length === 0) {
    console.log('⚠️ MONITORED_SYMBOLS boş, logging stream başlatılamıyor');
    return;
  }

  // Binance WebSocket has a limit on streams per connection (~200)
  const CHUNK_SIZE = 200;
  const chunks = [];

  for (let i = 0; i < MONITORED_SYMBOLS.length; i += CHUNK_SIZE) {
    chunks.push(MONITORED_SYMBOLS.slice(i, i + CHUNK_SIZE));
  }

  console.log(`🔗 ${chunks.length} WebSocket bağlantısı (logging) oluşturuluyor...`);

  chunks.forEach((chunk, index) => {
    const streams = chunk.map(symbol =>
      `${symbol.toLowerCase()}@kline_1m`
    ).join('/');

    const wsUrl = `${BINANCE_STREAM_BASE}?streams=${streams}`;

    const ws = new WebSocket(wsUrl);

    ws.on('open', () => {
      console.log(`🔗 Logging WebSocket ${index + 1}/${chunks.length} bağlandı - ${chunk.length} sembol`);
      if (index === 0) {
        console.log(`📊 Logging: Toplam ${MONITORED_SYMBOLS.length} USDT çifti takip ediliyor`);
      }
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        if (message.stream && message.data) {
          const streamName = message.stream;
          const klineData = message.data;

          if (streamName.includes('@kline_1m') && klineData.k) {
            const symbol = klineData.k.s;
            const kline = klineData.k;

            // Only process completed klines
            if (kline.x) {
              console.log(`📊 Processing completed kline for ${symbol}: ${kline.c}`);
              updateMarketData(symbol, kline);

              // OLD SPIKE DETECTION DISABLED - Using Financial Detector instead
              // const spike = detectSpikeWithLogging(symbol, kline, marketData);
              // if (spike) { ... } - OLD SPIKE HANDLING DISABLED
            }

            // Emit real-time kline data
            io.emit('kline', {
              symbol,
              price: parseFloat(kline.c),
              volume: parseFloat(kline.v),
              timestamp: kline.t
            });
          }
        }
      } catch (error) {
        console.error('WebSocket message parsing error:', error);
      }
    });

    ws.on('close', () => {
      console.log(`🔌 Logging WebSocket ${index + 1} disconnected, reconnecting...`);
      setTimeout(() => connectBinanceStreamsWithLogging(), 5000);
    });

    ws.on('error', (error) => {
      console.error(`❌ Logging WebSocket ${index + 1} error:`, error);
    });

    binanceWsConnections.set(`logging_stream_${index}`, ws);
  });
};

// Socket.IO connection handler
io.on('connection', (socket) => {
  console.log('Yeni Socket.IO bağlantısı:', socket.id);

  // Join user to their room
  socket.on('join', (username) => {
    if (username) {
      socket.join(username);
      console.log(`Kullanıcı ${username} room'a katıldı`);

      // 🚀 KRİTİK EKSİKLİK DÜZELTİLDİ: UserSettings Otomatik Oluşturma
      if (!userSettings.has(username)) {
        const defaultSettings = {
          autoTrade: true, // Kullanıcı login olduğunda otomatik aktif
          maxPositions: dashboardConfig.maksPozisyon || 3,
          tradeAmount: dashboardConfig.islemMiktari || 6, // 20x kaldıraçla 6 USD = 0.3 USD margin
          takeProfitPercent: dashboardConfig.tpYuzdesi || 0.5,
          stopLossPercent: dashboardConfig.slYuzdesi || 0.5,
          spikeThreshold: dashboardConfig.spikeEsigi || 1.1,
          volumeMultiplier: dashboardConfig.hacimCarpani || 1.5,
          maPeriyodu: dashboardConfig.maPeriyodu || 10,
          isActive: dashboardConfig.islemBaslatildi || false,
          leverage: 20,
          minSlope: 0.00001
        };

        userSettings.set(username, defaultSettings);
        console.log(`✅ ${username} için userSettings oluşturuldu:`, defaultSettings);
      } else {
        // Mevcut ayarları dashboard config ile senkronize et
        const settings = userSettings.get(username);
        settings.autoTrade = true; // Kullanıcı login olduğunda otomatik aktif
        settings.maxPositions = dashboardConfig.maksPozisyon || 3;
        settings.tradeAmount = dashboardConfig.islemMiktari || 5;
        settings.takeProfitPercent = dashboardConfig.tpYuzdesi || 0.5;
        settings.stopLossPercent = dashboardConfig.slYuzdesi || 0.5;
        settings.spikeThreshold = dashboardConfig.spikeEsigi || 1.1;
        settings.volumeMultiplier = dashboardConfig.hacimCarpani || 1.5;
        settings.maPeriyodu = dashboardConfig.maPeriyodu || 10;
        settings.isActive = dashboardConfig.islemBaslatildi || false;
        console.log(`🔄 ${username} ayarları senkronize edildi`);
      }

      // Send initial connection confirmation
      socket.emit('connection-confirmed', {
        message: 'Bağlantı başarılı',
        username,
        timestamp: Date.now()
      });

      // Start user data stream if not already started
      if (!userDataStreams.has(username)) {
        startUserDataStream(username).catch(err => {
          console.log(`⚠️ User data stream başlatılamadı: ${username} - ${err.message}`);
        });
      }
    }
  });

  // Handle user disconnect
  socket.on('disconnect', () => {
    console.log('Socket.IO bağlantısı kesildi:', socket.id);
  });

  // Handle manual balance refresh
  socket.on('refresh-balance', async (username) => {
    if (username) {
      try {
        const credentials = getUserCredentials(username);

        if (!credentials.apiKey || !credentials.secretKey) {
          socket.emit('error', {
            message: 'API credentials eksik',
            error: 'Lütfen API key\'leri kontrol edin'
          });
          return;
        }

        try {
          const accountInfo = await binanceRequest('/fapi/v2/account', {}, 'GET', credentials);
          const usdtBalance = accountInfo.assets.find(asset => asset.asset === 'USDT');

          if (usdtBalance) {
            const balanceData = {
              asset: 'USDT',
              balance: parseFloat(usdtBalance.walletBalance),
              availableBalance: parseFloat(usdtBalance.availableBalance),
              crossUnPnl: parseFloat(usdtBalance.crossUnPnl),
              timestamp: Date.now()
            };

            socket.emit('balance-update', balanceData);
          }
        } catch (apiError) {
          if (apiError.response?.status === 401) {
            console.log('❌ API key geçersiz (Socket) - Gerçek bakiye alınamıyor');
            socket.emit('api-error', {
              error: 'API key geçersiz. Lütfen Binance API ayarlarınızı kontrol ediniz.',
              needsApiKey: true,
              timestamp: Date.now()
            });
          } else {
            throw apiError;
          }
        }
      } catch (error) {
        console.error('Manual balance refresh hatası:', error);
        socket.emit('error', {
          message: 'Bakiye yenilenemedi',
          error: error.message
        });
      }
    }
  });

  // Spike analysis control
  socket.on('start-spike-analysis', (data) => {
    const { username } = data;
    console.log(`📊 Spike analizi başlatıldı: ${username}`);

    // Join user to spike analysis room
    socket.join(`spike-analysis-${username}`);

    // Emit confirmation
    socket.emit('spike-analysis-started', { username });
  });

  socket.on('stop-spike-analysis', (data) => {
    const { username } = data;
    console.log(`📊 Spike analizi durduruldu: ${username}`);

    // Leave spike analysis room
    socket.leave(`spike-analysis-${username}`);

    // Emit confirmation
    socket.emit('spike-analysis-stopped', { username });
  });

  // Handle spike settings update
  socket.on('update-spike-settings', (data) => {
    const { username, settings } = data;
    console.log(`⚙️ Spike ayarları güncellendi: ${username}`, settings);

    // Update user settings
    if (userSettings.has(username)) {
      const currentSettings = userSettings.get(username);
      userSettings.set(username, { ...currentSettings, ...settings });
    } else {
      userSettings.set(username, settings);
    }

    // Update spike detector settings
    updateSpikeDetectorSettings(username, userSettings.get(username));

    // Emit confirmation
    socket.emit('spike-settings-updated', {
      username,
      settings: userSettings.get(username)
    });

    console.log(`✅ ${username} için spike ayarları kaydedildi`);
  });

  // Test event handlers for WebSocket testing
  socket.on('test-account-update', (data) => {
    console.log('🧪 Test account update received:', data.username);
    if (data.username && data.data) {
      handleAccountUpdate(data.username, data.data);
    }
  });

  socket.on('test-order-update', (data) => {
    console.log('🧪 Test order update received:', data.username);
    if (data.username && data.data) {
      handleOrderUpdate(data.username, data.data);
    }
  });

  socket.on('test-position-close', (data) => {
    console.log('🧪 Test position close received:', data.username);
    if (data.username && data.symbol) {
      io.to(data.username).emit('position-closed', {
        symbol: data.symbol,
        reason: data.reason || 'TEST',
        pnl: data.pnl || { netPnl: 0 },
        timestamp: Date.now()
      });
    }
  });

  socket.on('authenticate', (data) => {
    console.log('🔐 Authentication attempt:', data.username);
    if (data.username) {
      socket.join(data.username);
      socket.emit('authenticated', {
        success: true,
        username: data.username,
        timestamp: Date.now()
      });
    }
  });

  // Handle position opening from spike
  socket.on('open-position-from-spike', async (data) => {
    try {
      const { username, tradeParams } = data;
      console.log(`📊 Opening position from spike for ${username}:`, tradeParams);

      // Check position limit again (double check)
      const userPositions = await getUserOpenPositions(username);
      const openPositionsCount = userPositions.length;
      const maxPositions = 3; // Should get from user settings

      if (openPositionsCount >= maxPositions) {
        socket.emit('position-open-failed', {
          reason: 'MAX_POSITIONS_REACHED',
          currentPositions: openPositionsCount,
          maxPositions
        });
        return;
      }

      // Execute the trade
      await executeSpikeTrade(username, tradeParams);

    } catch (error) {
      console.error('❌ Error opening position from spike:', error.message);
      socket.emit('position-open-failed', {
        reason: 'EXECUTION_ERROR',
        error: error.message
      });
    }
  });
});

// Global trading control variables
let AUTO_TRADING_ACTIVE = false;
const MAX_CONCURRENT_POSITIONS = 3; // Default, can be overridden per user

// Initialize new spike detector with WebSocket
function startSpikeDetector() {
  console.log('🔍 Starting WebSocket-based spike detection system...');
  console.log(`📊 Will monitor ${MONITORED_SYMBOLS.length} USDT pairs for spike detection`);

  // Connect to WebSocket kline stream with all monitored symbols
  spikeDetector.connect(MONITORED_SYMBOLS).then(() => {
    console.log('✅ SpikeDetector WebSocket connected for all symbols');
  }).catch(error => {
    console.error('❌ SpikeDetector connection failed:', error.message);
  });

  // Listen for spike events
  spikeDetector.on('spike', async (spikeData) => {
    console.log('🚀 WebSocket spike detected:', spikeData);

    // Emit spike to all connected clients first
    const turkishSpike = {
      id: spikeData.id || `${spikeData.sembol}_${Date.now()}`,
      sembol: spikeData.sembol,
      zaman: spikeData.zaman || Date.now(),
      fiyat: spikeData.fiyat || 0,
      ma5: spikeData.ma5 || 0,
      ma10: spikeData.ma10 || 0,
      egim: spikeData.egim || 0,
      hacim: spikeData.hacim || 0,
      ortalamHacim: spikeData.ortalamHacim || 0,
      guvenilirlik: spikeData.confidence > 80 ? 'YUKSEK' : spikeData.confidence > 60 ? 'ORTA' : 'DUSUK',
      durum: 'TESPIT_EDILDI',
      confidence: spikeData.confidence,
      algorithm: spikeData.algorithm,
      tradeDirection: spikeData.tradeDirection,
      spikeType: spikeData.spikeType,
      side: spikeData.side,
      rsi: spikeData.rsi
    };

    io.emit('spike-detected', turkishSpike);

    // Check and execute spike trades with position limit control
    await executeAutoTradeFromSpike(spikeData);

    // Add to recent spikes array
    recentSpikes.unshift(turkishSpike);

    // Keep only last 100 spikes
    if (recentSpikes.length > 100) {
      recentSpikes.pop();
    }

    // Emit to spike analysis rooms
    io.to('spike-analysis').emit('spike-update', {
      spike: turkishSpike,
      totalSpikes: recentSpikes.length
    });
  });

  // 📊 Listen for price updates from kline stream
  spikeDetector.on('price-update', (priceData) => {
    // Update positions with current prices and emit P&L updates
    updatePositionPrices(priceData);
  });

  // Check if any users have auto-trade enabled and execute trades with position limit control
  // This will be handled by executeAutoTradeFromSpike function

  // Connect to spike detector
  spikeDetector.connect();
}

// Get user's open positions for position limit control
async function getUserOpenPositions(username) {
  try {
    const credentials = getUserCredentials(username);
    if (!credentials) return [];

    const baseUrl = credentials.testnet ? BINANCE_TESTNET_BASE : BINANCE_API_BASE;
    const timestamp = Date.now();
    const recvWindow = 60000;

    const queryString = `timestamp=${timestamp}&recvWindow=${recvWindow}`;
    const signature = crypto
      .createHmac('sha256', credentials.secretKey)
      .update(queryString)
      .digest('hex');

    const response = await axios.get(`${baseUrl}/fapi/v2/account`, {
      headers: { 'X-MBX-APIKEY': credentials.apiKey },
      params: { timestamp, recvWindow, signature }
    });

    // Filter only open positions (positionAmt != 0)
    const openPositions = response.data.positions.filter(pos =>
      parseFloat(pos.positionAmt) !== 0
    );

    return openPositions;
  } catch (error) {
    console.error(`❌ Error getting positions for ${username}:`, error.message);
    return [];
  }
}

// Execute auto trade from spike with position limit control
async function executeAutoTradeFromSpike(spikeData) {
  try {
    console.log(`🔍 Checking auto-trade for spike: ${spikeData.sembol} (confidence: ${spikeData.confidence})`);
    console.log(`📊 Spike data:`, JSON.stringify(spikeData, null, 2));
    console.log(`👥 Total users with settings: ${userSettings.size}`);

    // Check each user's trading status and position limits
    for (const [username, settings] of userSettings) {
      console.log(`👤 Checking user: ${username}, autoTrade: ${settings.autoTrade}, isActive: ${settings.isActive}, dashboardTrading: ${dashboardConfig.islemBaslatildi}`);

      if (settings.autoTrade && settings.isActive && dashboardConfig.islemBaslatildi) {
        console.log(`🤖 Auto-trading for ${username} - ${spikeData.spikeType} spike detected on ${spikeData.sembol}`);

        try {
          // Simplified position check - skip for now to avoid API issues
          const openPositionsCount = 0; // TODO: Implement proper position tracking
          const userMaxPositions = settings.maxPositions || 3;

          console.log(`📈 ${username}: Open positions: ${openPositionsCount}/${userMaxPositions}`);

          // Check if spike meets user's criteria
          const spikePercent = spikeData.priceGap || spikeData.maDifference || 0;
          const meetsThreshold = spikePercent >= (settings.spikeThreshold || 0.25);
          const meetsSlope = (spikeData.egim || 0) >= (settings.minSlope || 0.00001);

          console.log(`🎯 ${username} criteria check:`);
          console.log(`   - Spike percent: ${spikePercent}% (threshold: ${settings.spikeThreshold || 0.25}%) = ${meetsThreshold}`);
          console.log(`   - Slope: ${spikeData.egim || 0} (min: ${settings.minSlope || 0.00001}) = ${meetsSlope}`);

          if (meetsThreshold && meetsSlope) {
            console.log(`✅ ${username}: ${spikeData.spikeType} spike meets criteria - Opening ${spikeData.tradeDirection} position (${openPositionsCount + 1}/${userMaxPositions})`);

            // Execute spike trade with user settings
            const tradeParams = {
              symbol: spikeData.sembol,
              side: spikeData.tradeDirection || 'SHORT',
              quantity: settings.tradeAmount || 5,
              leverage: settings.leverage || 20,
              stopLossPercent: settings.stopLossPercent || 0.5,
              takeProfitPercent: settings.takeProfitPercent || 0.5,
              confidence: spikeData.confidence,
              spikePrice: spikeData.fiyat,
              spikeType: spikeData.spikeType,
              rsi: spikeData.rsi
            };

            console.log(`🚀 Executing trade for ${username}:`, tradeParams);
            await executeSpikeTrade(username, tradeParams);
          } else {
            console.log(`❌ ${spikeData.spikeType} spike doesn't meet ${username}'s criteria (threshold: ${meetsThreshold}, slope: ${meetsSlope})`);
          }
        } catch (error) {
          console.error(`❌ Error processing spike for ${username}:`, error.message);
        }
      } else {
        if (!dashboardConfig.islemBaslatildi) {
          console.log(`⏸️ ${username}: Trading durduruldu - Dashboard'dan işlem başlatılmamış`);
        } else if (!settings.autoTrade) {
          console.log(`⏸️ ${username}: AutoTrade kapalı`);
        } else if (!settings.isActive) {
          console.log(`⏸️ ${username}: User aktif değil`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error in auto trade from spike:', error.message);
  }
}

// Update position prices and calculate P&L
function updatePositionPrices(priceData) {
  try {
    const { symbol, price } = priceData;

    // Emit price update to all connected clients for position tracking
    io.emit('price-update', {
      symbol: symbol,
      price: parseFloat(price),
      timestamp: Date.now()
    });

    // Log price updates for debugging (only for symbols with potential positions)
    if (Math.random() < 0.01) { // Log 1% of price updates for better debugging
      console.log(`💰 Price update: ${symbol} @ ${price}`);
    }

  } catch (error) {
    console.error('❌ Error updating position prices:', error.message);
  }
}

// 🚀 Real-time Ticker Stream for instant price updates
const TICKER_SYMBOLS = [
  'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT', 'SOLUSDT', 'DOGEUSDT', 'AVAXUSDT', 'DOTUSDT', 'MATICUSDT',
  'LINKUSDT', 'LTCUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'TRXUSDT', 'ETCUSDT', 'XLMUSDT', 'VETUSDT', 'ICPUSDT',
  'FTMUSDT', 'HBARUSDT', 'ALGOUSDT', 'AXSUSDT', 'SANDUSDT', 'MANAUSDT', 'APEUSDT', 'GALAUSDT', 'CHZUSDT', 'ENJUSDT',
  'API3USDT', 'ARUSDT', 'ARPAUSDT', 'ASTRUSDT', 'AUDIOUSDT', 'BADGERUSDT', 'BALUSDT', 'BANDUSDT', 'BATUSDT', 'BELUSDT'
];
let tickerWs = null;

function startRealTimeTickerStream() {
  try {
    const streams = TICKER_SYMBOLS.map(symbol => `${symbol.toLowerCase()}@ticker`).join('/');
    const wsUrl = `wss://fstream.binance.com/stream?streams=${streams}`;

    tickerWs = new WebSocket(wsUrl);

    tickerWs.on('open', () => {
      console.log(`🎯 Real-time ticker stream started for ${TICKER_SYMBOLS.length} symbols`);
      // Removed detailed symbol list to reduce log spam
    });

    tickerWs.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        if (message.stream && message.data) {
          const ticker = message.data;
          const symbol = ticker.s;
          const price = parseFloat(ticker.c); // Current price

          // Emit real-time price update (throttled to reduce browser load)
          if (Math.random() < 0.05) { // Only emit 5% of price updates to prevent browser slowdown
            io.emit('price-update', {
              symbol: symbol,
              price: price,
              change: parseFloat(ticker.P), // Price change percentage
              volume: parseFloat(ticker.v), // Volume
              timestamp: Date.now()
            });
          }

          // Removed frequent price update logs to reduce console spam
        }
      } catch (error) {
        console.error('❌ Ticker stream parsing error:', error);
      }
    });

    tickerWs.on('close', () => {
      console.log('🔌 Ticker stream disconnected, reconnecting in 10s...');
      setTimeout(startRealTimeTickerStream, 10000); // Increased from 5s to 10s
    });

    tickerWs.on('error', (error) => {
      console.error('❌ Ticker stream error:', error);
    });

  } catch (error) {
    console.error('❌ Failed to start ticker stream:', error);
  }
}

// Start real-time ticker stream
startRealTimeTickerStream();

// Execute spike trade with TP/SL orders - ULTRA-FAST VERSION ⚡⚡⚡
async function executeSpikeTrade(username, tradeParams) {
  const totalStartTime = Date.now(); // Track total execution time

  try {
    console.log(`🚀 ULTRA-FAST executeSpikeTrade for ${username}:`, tradeParams);

    const credentials = getUserCredentials(username);
    if (!credentials) {
      console.log(`❌ ${username}: User credentials not found`);
      throw new Error('User credentials not found');
    }

    console.log(`🔑 ${username}: Credentials found, testnet: ${credentials.testnet}`);

    const baseUrl = credentials.testnet ? BINANCE_TESTNET_BASE : BINANCE_API_BASE;
    const timestamp = Date.now();
    const recvWindow = 60000;

    // Get symbol precision and calculate position size
    const precision = await getSymbolPrecision(tradeParams.symbol, credentials);
    const quantity = calculatePositionSize(tradeParams, precision);
    const side = tradeParams.side || 'SHORT'; // Default SHORT for high spikes
    const currentPrice = tradeParams.spikePrice;

    console.log(`🎯 Executing ${side} trade for ${tradeParams.symbol} at ${currentPrice}`);
    console.log(`📊 Calculated quantity: ${quantity} (baseAmount: ${tradeParams.quantity}, leverage: ${tradeParams.leverage})`);

    // REDIS-BASED POSITION MANAGEMENT
    // 1. Acquire position lock to prevent race conditions
    const lockAcquired = await redisManager.acquirePositionLock(tradeParams.symbol, username, 5000);
    if (!lockAcquired) {
      console.log(`⚠️ ${username}: Position lock acquisition failed for ${tradeParams.symbol}`);
      throw new Error('Position lock acquisition failed - another trade in progress');
    }

    try {
      // 2. Check REAL position count from Binance (more reliable than Redis)
      const maxPositions = 3; // From config
      let currentPositions = 0;

      try {
        // Get actual open positions from Binance
        const positionsResponse = await binanceRequest('/fapi/v2/positionRisk', {}, 'GET', credentials);

        if (positionsResponse && Array.isArray(positionsResponse)) {
          // Count positions with non-zero size
          currentPositions = positionsResponse.filter(pos =>
            parseFloat(pos.positionAmt) !== 0
          ).length;

          console.log(`📊 ${username}: Real Binance positions: ${currentPositions}/${maxPositions}`);

          // Sync Redis count with real positions
          try {
            await redisManager.redis?.set(`positions:count:${username}`, currentPositions);
            console.log(`🔄 ${username}: Redis position count synced to ${currentPositions}`);
          } catch (syncError) {
            console.warn(`⚠️ ${username}: Failed to sync Redis position count:`, syncError.message);
          }
        } else {
          console.warn(`⚠️ ${username}: Failed to get positions from Binance, using Redis fallback`);
          currentPositions = await redisManager.getPositionCount(username);
        }
      } catch (positionError) {
        console.warn(`⚠️ ${username}: Binance position check failed, using Redis fallback:`, positionError.message);
        currentPositions = await redisManager.getPositionCount(username);
      }

      if (currentPositions >= maxPositions) {
        console.log(`⚠️ ${username}: Position limit reached (${currentPositions}/${maxPositions})`);
        throw new Error(`Position limit reached: ${currentPositions}/${maxPositions}`);
      }

      console.log(`✅ ${username}: Position check passed (${currentPositions}/${maxPositions})`);

      // 3. Increment position count atomically (will be synced after trade)
      await redisManager.incrementPositionCount(username);

      // 4. SIMULTANEOUS EXECUTION: Market + TP + SL ⚡⚡⚡
      console.log(`🚀⚡ ${username}: SIMULTANEOUS Market+TP+SL execution for ${tradeParams.symbol}`);

      // Get symbol precision (fast lookup)
      const getSymbolPrecision = (symbol) => {
        // Common symbol precision mapping for speed
        const precisionMap = {
          'BTCUSDT': 2, 'ETHUSDT': 2, 'BNBUSDT': 2,
          'ADAUSDT': 4, 'SOLUSDT': 3, 'XRPUSDT': 4,
          'DOTUSDT': 3, 'LINKUSDT': 3, 'LTCUSDT': 2,
          'BCHUSDT': 2, 'UNIUSDT': 3, 'MATICUSDT': 4,
          'AVAXUSDT': 3, 'ATOMUSDT': 3, 'FILUSDT': 3,
          'HOMEUSDT': 5, 'CUSDT': 5, 'VELVETUSDT': 5
        };

        // Check if symbol is in map
        if (precisionMap[symbol]) {
          return precisionMap[symbol];
        }

        // Auto-detect based on symbol pattern
        if (symbol.includes('1000')) return 6; // 1000FLOKIUSDT etc.
        if (symbol.includes('USDT')) return 4; // Most USDT pairs

        return 4; // Default fallback
      };

      const symbolPrecision = getSymbolPrecision(tradeParams.symbol);

      // Calculate TP/SL prices using current price (before market order)
      const tpPercent = tradeParams.takeProfitPercent || 0.5;
      const slPercent = tradeParams.stopLossPercent || 0.5;

      let takeProfitPrice, stopLossPrice;
      if (side === 'SHORT') {
        takeProfitPrice = currentPrice * (1 - tpPercent / 100);
        stopLossPrice = currentPrice * (1 + slPercent / 100);
      } else {
        takeProfitPrice = currentPrice * (1 + tpPercent / 100);
        stopLossPrice = currentPrice * (1 - slPercent / 100);
      }

      console.log(`💰 ${username}: Pre-calc prices - Entry: ${currentPrice}, TP: ${takeProfitPrice}, SL: ${stopLossPrice}`);
      console.log(`🎯 ${username}: TP/SL Details:`);
      console.log(`   📊 Symbol: ${tradeParams.symbol}`);
      console.log(`   📈 Side: ${side} (${side === 'SHORT' ? 'TP/SL = BUY' : 'TP/SL = SELL'})`);
      console.log(`   🎯 TP Price: ${takeProfitPrice.toFixed(symbolPrecision)} (${tpPercent}% below entry)`);
      console.log(`   🛑 SL Price: ${stopLossPrice.toFixed(symbolPrecision)} (${slPercent}% above entry)`);
      console.log(`   ⚙️ Working Type: CONTRACT_PRICE (Last Price)`);
      console.log(`   🔧 Precision: ${symbolPrecision} decimals`);

      // Prepare all 3 orders simultaneously
      const marketParams = {
        symbol: tradeParams.symbol,
        side: side === 'SHORT' ? 'SELL' : 'BUY',
        type: 'MARKET',
        quantity: quantity.toString(),
        timestamp,
        recvWindow: 1000
      };

      const tpParams = {
        symbol: tradeParams.symbol,
        side: side === 'SHORT' ? 'BUY' : 'SELL',
        type: 'TAKE_PROFIT_MARKET',
        quantity: quantity.toString(),
        stopPrice: takeProfitPrice.toFixed(symbolPrecision), // ✅ Symbol precision kullan
        reduceOnly: true,
        workingType: 'CONTRACT_PRICE', // ✅ CONTRACT_PRICE kullan
        timestamp: timestamp + 1,
        recvWindow: 1000
      };

      const slParams = {
        symbol: tradeParams.symbol,
        side: side === 'SHORT' ? 'BUY' : 'SELL',
        type: 'STOP_MARKET',
        quantity: quantity.toString(),
        stopPrice: stopLossPrice.toFixed(symbolPrecision), // ✅ Symbol precision kullan
        reduceOnly: true,
        workingType: 'CONTRACT_PRICE', // ✅ CONTRACT_PRICE kullan
        timestamp: timestamp + 2,
        recvWindow: 1000
      };

      // Sign all orders inline for maximum speed
      const signOrder = (params) => {
        const query = Object.keys(params).map(k => `${k}=${params[k]}`).join('&');
        const sig = crypto.createHmac('sha256', credentials.secretKey).update(query).digest('hex');
        return `${query}&signature=${sig}`;
      };

      const marketData = signOrder(marketParams);
      const tpData = signOrder(tpParams);
      const slData = signOrder(slParams);

      // EXECUTE ALL 3 ORDERS SIMULTANEOUSLY ⚡⚡⚡
      const simultaneousStartTime = Date.now();
      console.log(`⚡⚡⚡ ${username}: Executing Market+TP+SL SIMULTANEOUSLY...`);

      const [marketResult, tpResult, slResult] = await Promise.allSettled([
        axios({
          method: 'POST',
          url: `${baseUrl}/fapi/v1/order`,
          headers: { 'X-MBX-APIKEY': credentials.apiKey, 'Content-Type': 'application/x-www-form-urlencoded' },
          data: marketData,
          timeout: 1500
        }),
        axios({
          method: 'POST',
          url: `${baseUrl}/fapi/v1/order`,
          headers: { 'X-MBX-APIKEY': credentials.apiKey, 'Content-Type': 'application/x-www-form-urlencoded' },
          data: tpData,
          timeout: 1500
        }),
        axios({
          method: 'POST',
          url: `${baseUrl}/fapi/v1/order`,
          headers: { 'X-MBX-APIKEY': credentials.apiKey, 'Content-Type': 'application/x-www-form-urlencoded' },
          data: slData,
          timeout: 1500
        })
      ]);

      const simultaneousTime = Date.now() - simultaneousStartTime;
      console.log(`🚀⚡ ${username}: SIMULTANEOUS execution completed in ${simultaneousTime}ms`);

      // Process all results
      let response = null;
      let tpOrderResult = null;
      let slOrderResult = null;

      // Market order result
      if (marketResult.status === 'fulfilled' && marketResult.value.data?.orderId) {
        response = marketResult.value;
        console.log(`✅ ${username}: Market order SUCCESS - OrderID: ${response.data.orderId}`);
      } else {
        console.error(`❌ ${username}: Market order FAILED:`, marketResult.reason?.response?.data || marketResult.reason?.message);
        throw new Error('Market order failed in simultaneous execution');
      }

      // TP order result
      if (tpResult.status === 'fulfilled' && tpResult.value.data?.orderId) {
        tpOrderResult = tpResult.value.data;
        console.log(`✅ ${username}: TP order SUCCESS - OrderID: ${tpOrderResult.orderId}`);
      } else {
        console.error(`❌ ${username}: TP order FAILED:`, tpResult.reason?.response?.data || tpResult.reason?.message);
      }

      // SL order result
      if (slResult.status === 'fulfilled' && slResult.value.data?.orderId) {
        slOrderResult = slResult.value.data;
        console.log(`✅ ${username}: SL order SUCCESS - OrderID: ${slOrderResult.orderId}`);
      } else {
        console.error(`❌ ${username}: SL order FAILED:`, slResult.reason?.response?.data || slResult.reason?.message);
      }

      console.log(`🎯 ${username}: SIMULTANEOUS EXECUTION SUMMARY:`);
      console.log(`   ⚡ Total time: ${simultaneousTime}ms`);
      console.log(`   📈 Market: ${response ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   🎯 TP: ${tpOrderResult ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   🛑 SL: ${slOrderResult ? 'SUCCESS' : 'FAILED'}`);

      // Calculate executed price
      let executedPrice = currentPrice;
      if (response.data.avgPrice) {
        executedPrice = parseFloat(response.data.avgPrice);
      } else if (response.data.fills && response.data.fills.length > 0) {
        const totalQty = response.data.fills.reduce((sum, fill) => sum + parseFloat(fill.qty), 0);
        const totalValue = response.data.fills.reduce((sum, fill) => sum + (parseFloat(fill.price) * parseFloat(fill.qty)), 0);
        executedPrice = totalValue / totalQty;
      }

    if (response && response.data && response.data.orderId) {
      console.log(`✅ ${username}: ${side} position opened for ${tradeParams.symbol} at ${executedPrice}`);

      // Send success notification to user
      io.to(username).emit('position-opened', {
        symbol: tradeParams.symbol,
        side: side,
        quantity: quantity,
        price: executedPrice,
        orderId: response.data.orderId,
        confidence: tradeParams.confidence,
        spikeType: tradeParams.spikeType,
        takeProfitPrice: takeProfitPrice || 0,
        stopLossPrice: stopLossPrice || 0,
        tpOrderId: tpOrderResult?.orderId || null,
        slOrderId: slOrderResult?.orderId || null,
        tpOrderStatus: tpOrderResult ? 'SUCCESS' : (takeProfitPrice > 0 ? 'FAILED' : 'SKIPPED'),
        slOrderStatus: slOrderResult ? 'SUCCESS' : (stopLossPrice > 0 ? 'FAILED' : 'SKIPPED'),
        timestamp: Date.now()
      });

      return response.data;
    }

    } catch (lockError) {
      console.error(`❌ ${username}: Position lock error:`, lockError.message);
      // Decrement position count if lock error occurred after increment
      await redisManager.decrementPositionCount(username);
      throw lockError;
    }

  } catch (error) {
    console.error(`❌ Error executing spike trade for ${username}:`, error.message);
    console.error(`❌ Error details:`, error.response?.data || error);

    // Decrement position count on error
    try {
      await redisManager.decrementPositionCount(username);
    } catch (decrementError) {
      console.error(`❌ Failed to decrement position count:`, decrementError.message);
    }

    // Send error notification to user
    io.to(username).emit('position-open-failed', {
      symbol: tradeParams.symbol,
      reason: 'EXECUTION_ERROR',
      error: error.message,
      timestamp: Date.now()
    });

    throw error;
  } finally {
    // REDIS CLEANUP - Always release position lock
    try {
      await redisManager.releasePositionLock(tradeParams.symbol, username);
      console.log(`🔓 ${username}: Position lock released for ${tradeParams.symbol}`);
    } catch (lockError) {
      console.error(`❌ ${username}: Failed to release position lock:`, lockError.message);
    }

    // Record execution metrics in Redis
    const totalExecutionTime = Date.now() - totalStartTime;
    try {
      await redisManager.recordExecutionTime('trade_execution', totalExecutionTime);
    } catch (metricsError) {
      console.error(`❌ Failed to record execution metrics:`, metricsError.message);
    }

    // Log total execution time for performance monitoring
    console.log(`⚡ ${username}: TOTAL TRADE EXECUTION TIME: ${totalExecutionTime}ms for ${tradeParams.symbol}`);

    // Performance alert if too slow
    if (totalExecutionTime > 5000) {
      console.warn(`⚠️ ${username}: SLOW EXECUTION WARNING - ${totalExecutionTime}ms for ${tradeParams.symbol}`);
    } else if (totalExecutionTime < 1000) {
      console.log(`🚀 ${username}: ULTRA-FAST EXECUTION - ${totalExecutionTime}ms for ${tradeParams.symbol}`);
    }
  }
}

// Calculate position size based on user settings
async function getSymbolPrecision(symbol, credentials) {
  try {
    const baseUrl = credentials.testnet ?
      'https://testnet.binancefuture.com' :
      'https://fapi.binance.com';

    const response = await axios.get(`${baseUrl}/fapi/v1/exchangeInfo`);
    const symbolInfo = response.data.symbols.find(s => s.symbol === symbol);

    if (symbolInfo) {
      // Get quantity precision from filters
      const lotSizeFilter = symbolInfo.filters.find(f => f.filterType === 'LOT_SIZE');
      if (lotSizeFilter) {
        const stepSize = parseFloat(lotSizeFilter.stepSize);
        const precision = stepSize.toString().split('.')[1]?.length || 0;
        return precision;
      }
    }

    // Default precision if not found
    return 3;
  } catch (error) {
    console.error(`❌ Error getting symbol precision for ${symbol}:`, error.message);
    return 3; // Default precision
  }
}

function calculatePositionSize(tradeParams, precision = 3) {
  // Use frontend config or fallback to minimum
  const baseAmount = tradeParams.quantity || 6; // Default 6 USD (20x kaldıraçla 0.3 USD margin)
  const leverage = tradeParams.leverage || 20;

  // Calculate quantity based on current price and leverage
  const price = tradeParams.spikePrice || 1;
  const quantity = (baseAmount * leverage) / price;

  // Ensure minimum notional value (Binance requirement)
  const minNotional = 5; // Minimum 5 USD notional (Binance futures requirement)
  const notionalValue = quantity * price;

  if (notionalValue < minNotional) {
    console.log(`⚠️ Notional value ${notionalValue.toFixed(2)} USD is below minimum ${minNotional} USD`);
    // Adjust quantity to meet minimum notional
    const adjustedQuantity = minNotional / price;
    return parseFloat(adjustedQuantity.toFixed(precision));
  }

  // Round to appropriate decimal places based on symbol precision
  return parseFloat(quantity.toFixed(precision));
}

// Place Take Profit order - OPTIMIZED FOR SPEED ⚡
async function placeTakeProfitOrder(username, orderParams) {
  try {
    const { symbol, side, quantity, price, credentials, baseUrl } = orderParams;
    const timestamp = Date.now();
    const recvWindow = 5000; // Reduced for faster execution

    const tpOrderParams = {
      symbol,
      side, // SHORT için BUY TP
      type: 'LIMIT',
      timeInForce: 'GTC',
      quantity: quantity.toString(),
      price: price.toFixed(8),
      reduceOnly: true, // Pozisyonu kapatmak için
      timestamp,
      recvWindow
    };

    const queryString = Object.keys(tpOrderParams)
      .map(key => `${key}=${tpOrderParams[key]}`)
      .join('&');

    const signature = crypto
      .createHmac('sha256', credentials.secretKey)
      .update(queryString)
      .digest('hex');

    const signedParams = { ...tpOrderParams, signature };

    // Use faster request method
    const response = await axios({
      method: 'POST',
      url: `${baseUrl}/fapi/v1/order`,
      headers: {
        'X-MBX-APIKEY': credentials.apiKey,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: Object.keys(signedParams).map(key => `${key}=${signedParams[key]}`).join('&'),
      timeout: 3000 // 3 second timeout for speed
    });

    console.log(`✅ ${username}: TP order placed for ${symbol} at ${price} - ID: ${response.data.orderId}`);
    return response.data;

  } catch (error) {
    console.error(`❌ TP order failed for ${username}:`, error.response?.data || error.message);
    throw error;
  }
}

// Place Stop Loss order - OPTIMIZED FOR SPEED ⚡
async function placeStopLossOrder(username, orderParams) {
  try {
    const { symbol, side, quantity, stopPrice, credentials, baseUrl } = orderParams;
    const timestamp = Date.now();
    const recvWindow = 5000; // Reduced for faster execution

    const slOrderParams = {
      symbol,
      side, // SHORT için BUY SL
      type: 'STOP_MARKET',
      quantity: quantity.toString(),
      stopPrice: stopPrice.toFixed(8),
      reduceOnly: true, // Pozisyonu kapatmak için
      timestamp,
      recvWindow
    };

    const queryString = Object.keys(slOrderParams)
      .map(key => `${key}=${slOrderParams[key]}`)
      .join('&');

    const signature = crypto
      .createHmac('sha256', credentials.secretKey)
      .update(queryString)
      .digest('hex');

    const signedParams = { ...slOrderParams, signature };

    // Use faster request method
    const response = await axios({
      method: 'POST',
      url: `${baseUrl}/fapi/v1/order`,
      headers: {
        'X-MBX-APIKEY': credentials.apiKey,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: Object.keys(signedParams).map(key => `${key}=${signedParams[key]}`).join('&'),
      timeout: 3000 // 3 second timeout for speed
    });

    console.log(`✅ ${username}: SL order placed for ${symbol} at ${stopPrice} - ID: ${response.data.orderId}`);
    return response.data;

  } catch (error) {
    console.error(`❌ SL order failed for ${username}:`, error.response?.data || error.message);
    throw error;
  }
}

// ULTRA-FAST Parallel TP/SL Implementation ⚡⚡⚡
// Note: Binance Futures doesn't support OCO orders like Spot trading
// We use ultra-fast parallel TP/SL orders instead
async function placeTPSLOrdersUltraFast(username, orderParams) {
  try {
    const { symbol, side, quantity, takeProfitPrice, stopLossPrice, credentials, baseUrl } = orderParams;
    const timestamp = Date.now();
    const recvWindow = 2000; // Ultra-fast window

    console.log(`⚡⚡⚡ ${username}: Placing ULTRA-FAST parallel TP/SL orders for ${symbol}`);
    console.log(`🎯 TP: ${takeProfitPrice} | 🛑 SL: ${stopLossPrice}`);

    const startTime = Date.now();

    // Create both orders simultaneously
    const tpOrderParams = {
      symbol,
      side: side === 'SHORT' ? 'BUY' : 'SELL',
      type: 'TAKE_PROFIT_MARKET',
      quantity: quantity.toString(),
      stopPrice: takeProfitPrice.toFixed(8),
      reduceOnly: true,
      workingType: 'CONTRACT_PRICE', // ✅ CONTRACT_PRICE kullan
      timestamp,
      recvWindow
    };

    const slOrderParams = {
      symbol,
      side: side === 'SHORT' ? 'BUY' : 'SELL',
      type: 'STOP_MARKET',
      quantity: quantity.toString(),
      stopPrice: stopLossPrice.toFixed(8),
      reduceOnly: true,
      workingType: 'CONTRACT_PRICE', // ✅ CONTRACT_PRICE kullan
      timestamp: timestamp + 1, // Slightly different timestamp
      recvWindow
    };

    // Sign both orders
    const tpQueryString = Object.keys(tpOrderParams)
      .map(key => `${key}=${tpOrderParams[key]}`)
      .join('&');
    const tpSignature = crypto
      .createHmac('sha256', credentials.secretKey)
      .update(tpQueryString)
      .digest('hex');
    const tpSignedParams = { ...tpOrderParams, signature: tpSignature };

    const slQueryString = Object.keys(slOrderParams)
      .map(key => `${key}=${slOrderParams[key]}`)
      .join('&');
    const slSignature = crypto
      .createHmac('sha256', credentials.secretKey)
      .update(slQueryString)
      .digest('hex');
    const slSignedParams = { ...slOrderParams, signature: slSignature };

    // Execute both orders in parallel - MAXIMUM SPEED ⚡⚡⚡
    const [tpResponse, slResponse] = await Promise.all([
      axios({
        method: 'POST',
        url: `${baseUrl}/fapi/v1/order`,
        headers: {
          'X-MBX-APIKEY': credentials.apiKey,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: Object.keys(tpSignedParams).map(key => `${key}=${tpSignedParams[key]}`).join('&'),
        timeout: 1500 // 1.5 second timeout for maximum speed
      }),
      axios({
        method: 'POST',
        url: `${baseUrl}/fapi/v1/order`,
        headers: {
          'X-MBX-APIKEY': credentials.apiKey,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: Object.keys(slSignedParams).map(key => `${key}=${slSignedParams[key]}`).join('&'),
        timeout: 1500 // 1.5 second timeout for maximum speed
      })
    ]);

    const executionTime = Date.now() - startTime;
    console.log(`🚀 ${username}: ULTRA-FAST TP/SL orders placed in ${executionTime}ms`);
    console.log(`✅ TP OrderID: ${tpResponse.data.orderId} | SL OrderID: ${slResponse.data.orderId}`);

    return {
      tpOrder: tpResponse.data,
      slOrder: slResponse.data,
      executionTime
    };

  } catch (error) {
    console.error(`❌ Ultra-fast TP/SL orders failed for ${username}:`, error.response?.data || error.message);
    throw error;
  }
}

// Debug function for MKRUSDT
const debugMKRUSDT = () => {
  console.log('\n🔍 DEBUGGING MKRUSDT:');
  const symbolData = marketData.get('MKRUSDT');

  if (!symbolData) {
    console.log('❌ MKRUSDT data not found');
    console.log('📊 Available symbols:', Array.from(marketData.keys()).slice(0, 10));
    return;
  }

  const { closes, volumes } = symbolData;
  const ma5 = calculateMA(closes, 5);
  const ma10 = calculateMA(closes, 10);
  const currentPrice = closes[closes.length - 1];
  const currentVolume = volumes[volumes.length - 1];

  console.log('📊 MKRUSDT Debug Data:');
  console.log(`   Data Length: ${closes.length}`);
  console.log(`   Current Price: ${currentPrice}`);
  console.log(`   Current Volume: ${currentVolume}`);
  console.log(`   MA5: ${ma5}`);
  console.log(`   MA10: ${ma10}`);
  console.log(`   Last 5 closes:`, closes.slice(-5));
  console.log(`   Last 5 volumes:`, volumes.slice(-5));
};

// Initialize market data and start streams
const startSystem = async () => {
  console.log('🚀 Starting ParaBOT system...');

  // Wait for market data initialization
  await initializeMarketData();

  // Start WebSocket-based spike detector (replaces old HTTP API method)
  startSpikeDetector();

  // OLD: Start WebSocket streams after data is ready (DISABLED - using SpikeDetector WebSocket)
  // connectBinanceStreamsWithLogging();

  console.log('✅ ParaBOT system fully initialized with WebSocket spike detection!');
};

// Start the system
startSystem().catch(error => {
  console.error('❌ System startup failed:', error);
});

// Note: startSpikeDetector() is now called from within startSystem()

// Debug MKRUSDT after 15 seconds
setTimeout(() => {
  debugMKRUSDT();
}, 15000);

export default app;