/**
 * SPIKE DETECTION WORKER
 * High-performance spike detection in separate thread
 * Prevents blocking main thread during intensive calculations
 */

const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

// Simple technical indicators for worker
function calculateMA(values, period) {
  if (!values || values.length < period) {
    return null;
  }

  const lastValues = values.slice(-period);
  const sum = lastValues.reduce((acc, val) => acc + (parseFloat(val) || 0), 0);
  return sum / period;
}

function calculateSlope(values) {
  if (!values || values.length < 2) {
    return 0;
  }

  const n = values.length;
  let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

  for (let i = 0; i < n; i++) {
    sumX += i;
    sumY += values[i];
    sumXY += i * values[i];
    sumXX += i * i;
  }

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  return slope || 0;
}

if (!isMainThread) {
  console.log('🔧 Spike Detection Worker started');

  // Worker configuration
  const config = {
    priceThreshold: 0.4,    // 0.4% above MA10
    slopeThreshold: 0.1,    // MA10 slope > 0.1
    volumeMultiplier: 1.5,  // 1.5x average volume
    ma10Period: 10,
    volumePeriod: 20,
    minDataPoints: 10,
    ...workerData?.config
  };

  // Worker-local market data cache
  const marketDataCache = new Map();

  /**
   * Advanced spike detection algorithm
   */
  function detectSpike(symbol, klineData, marketData) {
    try {
      const currentPrice = parseFloat(klineData.c);
      const currentVolume = parseFloat(klineData.v);
      const { closes, volumes } = marketData;

      if (!closes || closes.length < config.minDataPoints) {
        return null;
      }

      // 1. Calculate MA10
      const ma10 = calculateMA(closes, config.ma10Period);
      if (!ma10) return null;

      // 2. Price condition: Current price > MA10 by threshold
      const priceAboveMA = ((currentPrice - ma10) / ma10) * 100;
      if (priceAboveMA <= config.priceThreshold) {
        return null;
      }

      // 3. MA10 slope condition
      const ma10Values = [];
      for (let i = closes.length - config.ma10Period; i >= Math.max(0, closes.length - config.ma10Period - 5); i--) {
        const slice = closes.slice(Math.max(0, i - config.ma10Period + 1), i + 1);
        if (slice.length === config.ma10Period) {
          ma10Values.push(calculateMA(slice, config.ma10Period));
        }
      }

      if (ma10Values.length < 2) return null;

      const ma10Slope = calculateSlope(ma10Values);
      if (ma10Slope <= config.slopeThreshold) {
        return null;
      }

      // 4. Volume condition
      const avgVolume = calculateMA(volumes, config.volumePeriod);
      if (!avgVolume || currentVolume <= avgVolume * config.volumeMultiplier) {
        return null;
      }

      // 5. Calculate confidence score
      const priceScore = Math.min(priceAboveMA / config.priceThreshold, 3) * 25;
      const slopeScore = Math.min(ma10Slope / config.slopeThreshold, 3) * 25;
      const volumeScore = Math.min(currentVolume / (avgVolume * config.volumeMultiplier), 3) * 25;
      const confidence = Math.min(priceScore + slopeScore + volumeScore, 100);

      // 6. Create spike object
      const spike = {
        symbol,
        price: currentPrice,
        ma10,
        ma10Slope,
        priceAboveMA,
        volume: currentVolume,
        avgVolume,
        volumeRatio: currentVolume / avgVolume,
        confidence: Math.round(confidence),
        timestamp: Date.now(),
        spikeType: 'YUKSEK',
        tradeDirection: 'SHORT'
      };

      return spike;

    } catch (error) {
      console.error(`❌ Worker spike detection error for ${symbol}:`, error.message);
      return null;
    }
  }

  /**
   * Update market data cache
   */
  function updateMarketDataCache(symbol, klineData) {
    if (!marketDataCache.has(symbol)) {
      marketDataCache.set(symbol, {
        closes: [],
        volumes: [],
        highs: [],
        lows: [],
        lastUpdate: Date.now()
      });
    }

    const data = marketDataCache.get(symbol);
    const close = parseFloat(klineData.c);
    const volume = parseFloat(klineData.v);
    const high = parseFloat(klineData.h);
    const low = parseFloat(klineData.l);

    // Add new data
    data.closes.push(close);
    data.volumes.push(volume);
    data.highs.push(high);
    data.lows.push(low);

    // Keep only last 50 data points for performance
    const maxDataPoints = 50;
    if (data.closes.length > maxDataPoints) {
      data.closes = data.closes.slice(-maxDataPoints);
      data.volumes = data.volumes.slice(-maxDataPoints);
      data.highs = data.highs.slice(-maxDataPoints);
      data.lows = data.lows.slice(-maxDataPoints);
    }

    data.lastUpdate = Date.now();
    marketDataCache.set(symbol, data);
  }

  // Listen for messages from main thread
  parentPort.on('message', (message) => {
    const { type, symbol, klineData, marketData, cooldownCheck } = message;

    try {
      if (type === 'DETECT_SPIKE') {
        // Update local cache
        updateMarketDataCache(symbol, klineData);
        
        // Use provided market data or local cache
        const dataToUse = marketData || marketDataCache.get(symbol);
        
        if (!dataToUse) {
          return; // No data available
        }

        // Check cooldown in main thread to avoid race conditions
        if (cooldownCheck && cooldownCheck.inCooldown) {
          return;
        }

        // Perform spike detection
        const spike = detectSpike(symbol, klineData, dataToUse);
        
        if (spike) {
          // Send spike back to main thread
          parentPort.postMessage({
            type: 'SPIKE_DETECTED',
            spike,
            processingTime: Date.now() - message.timestamp
          });
        }
      }
      
      if (type === 'UPDATE_CONFIG') {
        Object.assign(config, message.config);
        console.log('🔧 Worker config updated:', config);
      }

      if (type === 'HEALTH_CHECK') {
        parentPort.postMessage({
          type: 'HEALTH_RESPONSE',
          status: 'healthy',
          cacheSize: marketDataCache.size,
          config
        });
      }

    } catch (error) {
      console.error('❌ Worker message processing error:', error.message);
      parentPort.postMessage({
        type: 'ERROR',
        error: error.message,
        symbol
      });
    }
  });

  // Worker ready signal
  parentPort.postMessage({
    type: 'WORKER_READY',
    workerId: workerData?.workerId || 'spike-worker'
  });

} else {
  // Main thread - export worker creation function
  function createSpikeDetectionWorker(config = {}) {
    const worker = new Worker(__filename, {
      workerData: { config }
    });

    return worker;
  }

  module.exports = { createSpikeDetectionWorker };
}
