import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { LoginPage } from '@/components/auth/LoginPage';
import { DashboardMain } from './DashboardMain';

export const DashboardLayout = () => {
  const { authState } = useAuth();

  if (authState.loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!authState.isAuthenticated) {
    return <LoginPage />;
  }

  return <DashboardMain />;
};