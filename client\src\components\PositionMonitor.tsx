import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  TrendingUp,
  TrendingDown,
  Target,
  Shield,
  X,
  Clock,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { Pozisyon } from '@/types/turkish-trading';
import {
  formatPrice,
  formatPriceWithCurrency,
  formatPriceDifference,
  formatCurrency,
  formatQuantity,
  formatTimeDuration,
  calculateAndFormatPnL
} from '@/utils/priceFormatter';

interface PositionMonitorProps {
  positions: Pozisyon[];
  onClosePosition: (positionId: string) => void;
  onCloseAllPositions: () => void;
  onRefreshPositions: () => void;
}

export const PositionMonitor: React.FC<PositionMonitorProps> = ({
  positions,
  onClosePosition,
  onCloseAllPositions,
  onRefreshPositions
}) => {
  const [selectedPosition, setSelectedPosition] = useState<string | null>(null);

  // Removed old formatPrice and formatTime - using utility functions now

  const getPnLColor = (pnl: number): string => {
    return pnl >= 0 ? 'pnl-positive' : 'pnl-negative';
  };

  const getPnLBgColor = (pnl: number): string => {
    return pnl >= 0 ? 'pnl-bg-positive' : 'pnl-bg-negative';
  };

  const getPnLBadgeVariant = (pnl: number): "default" | "secondary" | "destructive" | "outline" => {
    return pnl >= 0 ? 'default' : 'destructive';
  };

  const getSideIcon = (side: string) => {
    if (side === 'LONG' || side === 'BUY') {
      return <TrendingUp className="h-4 w-4 side-long" />;
    }
    return <TrendingDown className="h-4 w-4 side-short" />;
  };

  const getSideColor = (side: string): string => {
    if (side === 'LONG' || side === 'BUY') {
      return 'side-long';
    }
    return 'side-short';
  };

  // Calculate detailed position metrics with precise formatting
  const calculatePositionMetrics = (position: Pozisyon) => {
    const entryPrice = position.girisFiyati;
    const currentPrice = position.guncelFiyat || position.markFiyati || entryPrice;
    const quantity = position.miktar;
    const leverage = position.kaldirac;
    const side = position.yon as 'LONG' | 'SHORT';
    const symbol = position.sembol;

    // Use utility function for accurate PnL calculation
    const pnlData = calculateAndFormatPnL(entryPrice, currentPrice, quantity, side, leverage, symbol);

    // Position value calculations
    const positionValue = entryPrice * quantity;
    const currentValue = currentPrice * quantity;
    const marginUsed = positionValue / leverage;
    const roe = (pnlData.pnlUSD / marginUsed) * 100;

    // Price change calculations
    const priceChange = currentPrice - entryPrice;
    const priceChangePercent = ((currentPrice - entryPrice) / entryPrice) * 100;
    const priceDiff = formatPriceDifference(priceChange, symbol);

    return {
      ...pnlData,
      positionValue,
      currentValue,
      marginUsed,
      roe,
      priceChange,
      priceChangePercent,
      priceDiff,
      formattedEntryPrice: formatPrice(entryPrice, symbol),
      formattedCurrentPrice: formatPrice(currentPrice, symbol),
      formattedQuantity: formatQuantity(quantity, symbol),
      formattedPositionValue: formatCurrency(positionValue),
      formattedMarginUsed: formatCurrency(marginUsed),
      formattedROE: `${roe >= 0 ? '+' : ''}${roe.toFixed(2)}%`
    };
  };

  // Removed old formatCurrency - using utility function now

  const totalPnL = positions.reduce((sum, pos) => sum + pos.pnl, 0);
  const totalPnLPercentage = positions.reduce((sum, pos) => sum + pos.pnlYuzdesi, 0);

  return (
    <Card className="w-full bg-gradient-to-br from-primary/5 to-position-bg border-primary/30 shadow-2xl hover:shadow-primary/30 transition-shadow duration-300">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-primary drop-shadow flex items-center gap-2 text-sm">
            <TrendingUp className="h-4 w-4" />
            Pozisyonlar
            <Badge variant="secondary" className="ml-2 text-xs">
              {positions.length}
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefreshPositions}
              className="h-6 px-2 text-xs hover:bg-primary/10 hover:text-primary hover:border-primary/30"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Yenile
            </Button>
            {positions.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCloseAllPositions}
                className="h-6 px-2 text-xs hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
              >
                <X className="h-3 w-3 mr-1" />
                Tümünü Kapat
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {/* Summary Stats */}
        {positions.length > 0 && (
          <div className="p-2 bg-muted/10 rounded-lg border border-border/20">
            <div className="grid grid-cols-3 gap-2 text-center">
              <div>
                <div className="text-xs text-muted-foreground">Toplam PnL</div>
                <div className={`font-mono font-bold text-sm ${getPnLColor(totalPnL)} ${getPnLBgColor(totalPnL)} px-2 py-1 rounded border`}>
                  {totalPnL >= 0 ? '+' : ''}{formatCurrency(totalPnL)}
                </div>
              </div>
              <div>
                <div className="text-xs text-muted-foreground">Toplam %</div>
                <div className={`font-mono font-bold text-sm ${getPnLColor(totalPnLPercentage)} ${getPnLBgColor(totalPnLPercentage)} px-2 py-1 rounded border`}>
                  {totalPnLPercentage >= 0 ? '+' : ''}{totalPnLPercentage.toFixed(2)}%
                </div>
              </div>
              <div>
                <div className="text-xs text-muted-foreground">Pozisyonlar</div>
                <div className="font-bold text-sm">{positions.length}/5</div>
              </div>
            </div>
          </div>
        )}

        {/* Positions List */}
        {positions.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            <AlertTriangle className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Açık pozisyon bulunmuyor</p>
            <p className="text-xs mt-1">İşlemler gerçekleştirildikçe pozisyonlar burada görünecek</p>
          </div>
        ) : (
          <div className="space-y-2">
            {positions.map((position) => {
              const metrics = calculatePositionMetrics(position);

              return (
                <div key={position.id} className="space-y-2">
                  <div
                    className={`p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                      selectedPosition === position.id
                        ? 'bg-muted/30 border-primary/30'
                        : metrics.pnlUSD >= 0
                          ? 'pnl-bg-positive border-profit/30 hover:bg-profit/5'
                          : 'pnl-bg-negative border-loss/30 hover:bg-loss/5'
                      }`}
                    onClick={() => setSelectedPosition(
                      selectedPosition === position.id ? null : position.id
                    )}
                  >
                    {/* Position Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          {getSideIcon(position.yon)}
                          <span className="font-bold text-sm">{position.sembol}</span>
                        </div>
                        <Badge
                          variant="outline"
                          className={`${getSideColor(position.yon)} border-current text-xs font-semibold`}
                        >
                          {position.yon}
                        </Badge>
                        <Badge variant="outline" className="border-primary/30 text-primary text-xs">
                          {position.kaldirac}x
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {formatTimeDuration(position.acilisZamani)}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className={`font-mono font-bold text-sm ${metrics.className} ${getPnLBgColor(metrics.pnlUSD)} px-2 py-1 rounded border`}>
                          {metrics.formattedPnL}
                        </div>
                        <div className={`text-xs font-semibold ${metrics.className} mt-1`}>
                          {metrics.formattedPercent}
                        </div>
                      </div>
                    </div>

                    {/* Position Info - Enhanced with Precise Formatting */}
                    <div className="grid grid-cols-4 gap-2 text-xs mb-2">
                      <div>
                        <div className="text-muted-foreground">Miktar</div>
                        <div className="font-mono font-semibold">{metrics.formattedQuantity}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Giriş</div>
                        <div className="font-mono">${metrics.formattedEntryPrice}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Güncel</div>
                        <div className="font-mono">${metrics.formattedCurrentPrice}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Değişim</div>
                        <div className={`font-mono ${metrics.priceDiff.className}`}>
                          ${metrics.priceDiff.formatted}
                        </div>
                      </div>
                    </div>

                    {/* Additional Metrics with Precise Values */}
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div>
                        <div className="text-muted-foreground">Pozisyon Değeri</div>
                        <div className="font-mono">{metrics.formattedPositionValue}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Kullanılan Marj</div>
                        <div className="font-mono">{metrics.formattedMarginUsed}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">ROE</div>
                        <div className={`font-mono font-semibold ${getPnLColor(metrics.roe)}`}>
                          {metrics.formattedROE}
                        </div>
                      </div>
                    </div>

                    {/* TP/SL Info */}
                    <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
                    <div className="flex items-center gap-1">
                      <Target className="h-3 w-3 text-profit" />
                      <span className="text-muted-foreground">TP:</span>
                      <span className="font-mono text-profit">
                        {position.takeProfit ? formatPrice(position.takeProfit, position.sembol) : 'Yok'}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Shield className="h-3 w-3 text-loss" />
                      <span className="text-muted-foreground">SL:</span>
                      <span className="font-mono text-loss">
                        {position.stopLoss ? formatPrice(position.stopLoss, position.sembol) : 'Yok'}
                      </span>
                    </div>
                  </div>

                  {/* Position Footer */}
                  <div className="flex items-center justify-between mt-2 pt-2 border-t border-border/20">
                    <div className="flex items-center gap-1 text-muted-foreground text-xs">
                      <Clock className="h-3 w-3" />
                      {formatTimeDuration(position.acilisZamani)}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onClosePosition(position.id);
                      }}
                      className="h-6 px-2 text-xs hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
                    >
                      <X className="h-3 w-3 mr-1" />
                      Kapat
                    </Button>
                  </div>
                </div>

                {/* Expanded Details */}
                {selectedPosition === position.id && (
                  <div className="ml-2 p-2 bg-muted/20 rounded-lg border border-border/20 space-y-2">
                    <h4 className="font-medium text-xs">Position Details</h4>

                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <div className="text-muted-foreground">Position ID</div>
                        <div className="font-mono text-xs">{position.id}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Status</div>
                        <Badge variant="outline" className="text-xs">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {position.durum}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <div className="text-muted-foreground">Entry Time</div>
                        <div className="text-xs">
                          {new Date(position.acilisZamani).toLocaleString()}
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Position Value</div>
                        <div className="font-mono">
                          ${(position.miktar * position.girisFiyati).toFixed(2)}
                        </div>
                      </div>
                    </div>

                    {/* Risk Metrics */}
                    <Separator className="bg-border/30" />
                    <div className="space-y-1">
                      <div className="text-xs font-medium">Risk Metrics</div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <div className="text-muted-foreground">Max Profit</div>
                          <div className="text-profit font-mono">
                            {position.takeProfit ?
                              `$${(Math.abs(position.takeProfit - position.girisFiyati) * position.miktar).toFixed(2)}`
                              : 'Unlimited'
                            }
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Max Loss</div>
                          <div className="text-loss font-mono">
                            {position.stopLoss ?
                              `$${(Math.abs(position.stopLoss - position.girisFiyati) * position.miktar).toFixed(2)}`
                              : 'Unlimited'
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
            })}
          </div>
        )}

        {/* Risk Warning */}
        {positions.length > 0 && totalPnL < -50 && (
          <Alert className="border-loss/30 bg-loss/5">
            <AlertTriangle className="h-4 w-4 text-loss" />
            <AlertDescription className="text-loss">
              <strong>Risk Warning:</strong> Your total losses exceed $50. Consider reviewing your risk management strategy.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};