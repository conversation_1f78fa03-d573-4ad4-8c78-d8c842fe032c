# 🔄 WebSocket Events - Real-time Position & Order Updates

Ana paneldeki pozisyonlar ve emirler artık WebSocket eventları ile gerçek zamanlı olarak güncellenmektedir. Bu sistem, Binance Futures API'sinden gelen güncellemeleri anında frontend'e iletir.

## 📊 Implemented Events

### 1. 💰 Balance Updates
**Event**: `balance-update`
```javascript
{
  asset: 'USDT',
  balance: 1000.00,
  availableBalance: 950.00,
  timestamp: 1234567890
}
```

### 2. 📈 Position Updates
**Event**: `positions-update` (Bulk update)
```javascript
{
  positions: [
    {
      s: 'BTCUSDT',        // symbol
      pa: '0.001',         // position amount
      ep: '45000.00',      // entry price
      mp: '45500.00',      // mark price
      up: '0.50',          // unrealized PnL
      l: '20'              // leverage
    }
  ],
  timestamp: 1234567890
}
```

**Event**: `position-update` (Individual update)
```javascript
{
  symbol: 'BTCUSDT',
  positionAmt: 0.001,
  entryPrice: 45000,
  markPrice: 45500,
  unRealizedProfit: 0.5,
  leverage: 20,
  timestamp: 1234567890
}
```

**Event**: `position-closed`
```javascript
{
  symbol: 'BTCUSDT',
  reason: 'TAKE_PROFIT', // or 'STOP_LOSS', 'MANUAL_CANCEL'
  pnl: { netPnl: 10.50 },
  timestamp: 1234567890
}
```

### 3. 📋 Order Updates
**Event**: `orders-update` (Bulk update)
```javascript
{
  orders: [
    {
      i: 12345678,         // order id
      s: 'BTCUSDT',        // symbol
      o: 'LIMIT',          // order type
      S: 'BUY',            // side
      q: '0.001',          // quantity
      p: '44000.00',       // price
      X: 'NEW',            // status
      T: 1234567890        // time
    }
  ],
  timestamp: 1234567890
}
```

**Event**: `order-update` (Individual update)
```javascript
{
  symbol: 'BTCUSDT',
  orderId: 12345678,
  type: 'LIMIT',
  side: 'BUY',
  status: 'NEW',
  quantity: 0.001,
  price: 44000,
  executedQty: 0,
  timestamp: 1234567890
}
```

## 🔧 Frontend Implementation

### DashboardMain.tsx Updates

```typescript
// Position update listener
newSocket.on('positions-update', (data) => {
  if (data.positions) {
    const formattedPositions = data.positions.map((pos: any) => ({
      id: `${pos.s}_${Date.now()}`,
      sembol: pos.s,
      yon: parseFloat(pos.pa) > 0 ? 'LONG' : 'SHORT',
      miktar: Math.abs(parseFloat(pos.pa)),
      girisFiyati: parseFloat(pos.ep),
      guncelFiyat: parseFloat(pos.mp || pos.ep),
      pnl: parseFloat(pos.up),
      pnlYuzdesi: parseFloat(pos.up) / (parseFloat(pos.ep) * Math.abs(parseFloat(pos.pa))) * 100,
      kaldirac: parseInt(pos.l) || 20,
      durum: parseFloat(pos.pa) === 0 ? 'KAPALI' : 'ACIK'
    }));
    setPositions(formattedPositions);
  }
});

// Order update listener
newSocket.on('order-update', (data) => {
  const formattedOrder = {
    id: data.orderId.toString(),
    sembol: data.symbol,
    tip: data.type === 'LIMIT' ? 'LIMIT' : data.type === 'STOP_MARKET' ? 'STOP_LOSS' : 'MARKET',
    yon: data.side,
    miktar: parseFloat(data.executedQty || data.quantity || '0'),
    fiyat: parseFloat(data.price || '0'),
    durum: data.status,
    olusturulmaZamani: data.timestamp,
    guncellemeZamani: Date.now()
  };

  if (data.status === 'FILLED' || data.status === 'CANCELED' || data.status === 'EXPIRED') {
    // Remove completed/cancelled orders
    setOpenOrders(prev => prev.filter(order => order.id !== formattedOrder.id));
  } else if (data.status === 'NEW' || data.status === 'PARTIALLY_FILLED') {
    // Add or update active orders
    setOpenOrders(prev => {
      const existingIndex = prev.findIndex(order => order.id === formattedOrder.id);
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = formattedOrder;
        return updated;
      } else {
        return [formattedOrder, ...prev];
      }
    });
  }
});
```

## 🔄 Backend Implementation

### Server.js Updates

```javascript
// Enhanced handleOrderUpdate function
const handleOrderUpdate = (username, message) => {
  const order = message.o;
  
  // Emit detailed order update
  io.to(username).emit('order-update', {
    symbol: order.s,
    orderId: order.i,
    type: order.o,
    side: order.S,
    status: order.X,
    quantity: parseFloat(order.q || 0),
    price: parseFloat(order.p || 0),
    executedQty: parseFloat(order.z || 0),
    timestamp: Date.now()
  });

  // Refresh open orders list
  refreshUserOrders(username);
};

// Enhanced handleAccountUpdate function
const handleAccountUpdate = (username, message) => {
  const { B: balances, P: positions } = message.a;

  // Emit position updates with detailed formatting
  if (positions) {
    const activePos = positions.filter(p => parseFloat(p.pa) !== 0);
    
    io.to(username).emit('positions-update', {
      positions: activePos,
      timestamp: Date.now()
    });

    // Also emit individual position updates
    activePos.forEach(pos => {
      io.to(username).emit('position-update', {
        symbol: pos.s,
        positionAmt: parseFloat(pos.pa),
        entryPrice: parseFloat(pos.ep),
        markPrice: parseFloat(pos.mp),
        unRealizedProfit: parseFloat(pos.up),
        leverage: parseInt(pos.l),
        timestamp: Date.now()
      });
    });
  }
};
```

## 🧪 Testing

Test dosyası ile WebSocket eventlarını test edebilirsiniz:

```bash
# Server'ı başlatın
node server/server.js

# Başka bir terminalde test çalıştırın
node test-websocket-events.js
```

### Test Sonuçları
```
✅ Socket.IO bağlantısı kuruldu
💰 Balance Update Event: USDT - 1000.00 available
📊 Positions Update Event: 2 positions
📈 Individual Position Updates: BTCUSDT, ETHUSDT
📝 Order Update Event: BTCUSDT LIMIT BUY
🔒 Position Closed Event: BTCUSDT TAKE_PROFIT
```

## 🔄 Real-time Flow

1. **Binance WebSocket** → User Data Stream
2. **Server.js** → handleUserDataUpdate()
3. **Event Handlers** → handleOrderUpdate(), handleAccountUpdate()
4. **Socket.IO Emit** → Frontend events
5. **Frontend State** → setPositions(), setOpenOrders()
6. **UI Update** → Real-time display

## 📋 Event Types

| Event | Trigger | Frequency |
|-------|---------|-----------|
| `balance-update` | Account balance change | On balance change |
| `positions-update` | Position bulk update | On account update |
| `position-update` | Individual position change | Real-time |
| `position-closed` | Position closed (TP/SL) | On position close |
| `orders-update` | Orders bulk refresh | On order change |
| `order-update` | Individual order change | Real-time |

## 🎯 Benefits

- **Real-time Updates**: Anında pozisyon ve emir güncellemeleri
- **Automatic Refresh**: Manuel yenileme gereksiz
- **Live P&L**: Gerçek zamanlı kar/zarar takibi
- **Order Status**: Emir durumu anlık güncelleme
- **Position Tracking**: Pozisyon değişiklikleri otomatik takip

## 🔧 Configuration

WebSocket eventları otomatik olarak aktiftir. Kullanıcı giriş yaptığında:

1. User data stream başlatılır
2. Socket.IO room'a katılır
3. Event listener'lar aktif olur
4. Real-time güncellemeler başlar

## 🚨 Error Handling

- **Connection Lost**: Otomatik yeniden bağlanma
- **API Errors**: Graceful error handling
- **Invalid Data**: Data validation
- **Rate Limiting**: Automatic retry with backoff
