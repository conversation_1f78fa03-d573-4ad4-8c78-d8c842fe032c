/**
 * REDIS MANAGER
 * High-performance shared state management for HFT
 * Handles positions, market data, locks, and caching
 */

import Redis from 'ioredis';

class RedisManager {
  constructor() {
    this.redis = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 2; // Reduced to prevent spam
    
    // Performance optimized Redis config
    this.config = {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: 0,
      
      // Performance optimizations
      lazyConnect: true,
      maxRetriesPerRequest: 1, // Fast fail for HFT
      retryDelayOnFailover: 50,
      connectTimeout: 1000,
      commandTimeout: 500,
      
      // Connection pooling
      family: 4,
      keepAlive: true,
      
      // Disable some features for speed
      enableReadyCheck: false,
      maxLoadingTimeout: 1000
    };
  }

  /**
   * Initialize Redis connection
   */
  async connect() {
    try {
      this.redis = new Redis(this.config);
      
      this.redis.on('connect', () => {
        console.log('🔗 Redis connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
      });

      this.redis.on('error', (error) => {
        console.error('❌ Redis error:', error.message);
        this.isConnected = false;
      });

      this.redis.on('close', () => {
        console.log('🔌 Redis connection closed');
        this.isConnected = false;
        this.attemptReconnect();
      });

      // Test connection
      await this.redis.ping();
      console.log('✅ Redis manager initialized');
      
      return true;
    } catch (error) {
      console.error('❌ Redis connection failed:', error.message);
      return false;
    }
  }

  /**
   * Attempt reconnection with exponential backoff
   */
  async attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max Redis reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);
    
    console.log(`🔄 Attempting Redis reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('❌ Redis reconnection failed:', error.message);
      }
    }, delay);
  }

  /**
   * POSITION MANAGEMENT
   */

  // Atomic position lock acquisition
  async acquirePositionLock(symbol, username, ttl = 5000) {
    if (!this.isConnected) return false;
    
    try {
      const lockKey = `lock:position:${symbol}:${username}`;
      const result = await this.redis.set(lockKey, Date.now(), 'PX', ttl, 'NX');
      return result === 'OK';
    } catch (error) {
      console.error('❌ Position lock acquisition failed:', error.message);
      return false;
    }
  }

  // Release position lock
  async releasePositionLock(symbol, username) {
    if (!this.isConnected) return false;
    
    try {
      const lockKey = `lock:position:${symbol}:${username}`;
      await this.redis.del(lockKey);
      return true;
    } catch (error) {
      console.error('❌ Position lock release failed:', error.message);
      return false;
    }
  }

  // Check position count for user
  async getPositionCount(username) {
    if (!this.isConnected) return 0;
    
    try {
      const count = await this.redis.get(`positions:count:${username}`);
      return parseInt(count || 0);
    } catch (error) {
      console.error('❌ Position count check failed:', error.message);
      return 0;
    }
  }

  // Increment position count atomically
  async incrementPositionCount(username) {
    if (!this.isConnected) return false;
    
    try {
      await this.redis.incr(`positions:count:${username}`);
      await this.redis.expire(`positions:count:${username}`, 86400); // 24 hours TTL
      return true;
    } catch (error) {
      console.error('❌ Position count increment failed:', error.message);
      return false;
    }
  }

  // Decrement position count atomically
  async decrementPositionCount(username) {
    if (!this.isConnected) return false;
    
    try {
      const current = await this.redis.get(`positions:count:${username}`);
      if (parseInt(current || 0) > 0) {
        await this.redis.decr(`positions:count:${username}`);
      }
      return true;
    } catch (error) {
      console.error('❌ Position count decrement failed:', error.message);
      return false;
    }
  }

  /**
   * MARKET DATA CACHING
   */

  // Cache market data with TTL
  async cacheMarketData(symbol, data, ttl = 300) {
    if (!this.isConnected) return false;
    
    try {
      const key = `market:${symbol}`;
      await this.redis.setex(key, ttl, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('❌ Market data caching failed:', error.message);
      return false;
    }
  }

  // Get cached market data
  async getCachedMarketData(symbol) {
    if (!this.isConnected) return null;
    
    try {
      const data = await this.redis.get(`market:${symbol}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('❌ Market data retrieval failed:', error.message);
      return null;
    }
  }

  /**
   * SPIKE COOLDOWN MANAGEMENT
   */

  // Check if symbol is in spike cooldown
  async isInSpikeCooldown(symbol, cooldownPeriod = 30000) {
    if (!this.isConnected) return false;
    
    try {
      const lastSpike = await this.redis.get(`spike:cooldown:${symbol}`);
      if (!lastSpike) return false;
      
      const timeSinceLastSpike = Date.now() - parseInt(lastSpike);
      return timeSinceLastSpike < cooldownPeriod;
    } catch (error) {
      console.error('❌ Spike cooldown check failed:', error.message);
      return false;
    }
  }

  // Set spike cooldown
  async setSpikeCooldown(symbol, cooldownPeriod = 30000) {
    if (!this.isConnected) return false;
    
    try {
      const key = `spike:cooldown:${symbol}`;
      await this.redis.setex(key, Math.ceil(cooldownPeriod / 1000), Date.now());
      return true;
    } catch (error) {
      console.error('❌ Spike cooldown set failed:', error.message);
      return false;
    }
  }

  /**
   * PERFORMANCE METRICS
   */

  // Record execution time
  async recordExecutionTime(operation, time) {
    if (!this.isConnected) return false;
    
    try {
      const key = `metrics:execution:${operation}`;
      await this.redis.lpush(key, time);
      await this.redis.ltrim(key, 0, 99); // Keep last 100 measurements
      await this.redis.expire(key, 3600); // 1 hour TTL
      return true;
    } catch (error) {
      console.error('❌ Execution time recording failed:', error.message);
      return false;
    }
  }

  // Get average execution time
  async getAverageExecutionTime(operation) {
    if (!this.isConnected) return 0;
    
    try {
      const key = `metrics:execution:${operation}`;
      const times = await this.redis.lrange(key, 0, -1);
      if (times.length === 0) return 0;
      
      const sum = times.reduce((acc, time) => acc + parseFloat(time), 0);
      return sum / times.length;
    } catch (error) {
      console.error('❌ Average execution time calculation failed:', error.message);
      return 0;
    }
  }

  /**
   * HEALTH CHECK
   */
  async healthCheck() {
    try {
      const start = Date.now();
      await this.redis.ping();
      const latency = Date.now() - start;
      
      return {
        connected: this.isConnected,
        latency,
        reconnectAttempts: this.reconnectAttempts
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message,
        reconnectAttempts: this.reconnectAttempts
      };
    }
  }

  /**
   * Graceful shutdown
   */
  async disconnect() {
    if (this.redis) {
      await this.redis.quit();
      console.log('🔌 Redis disconnected gracefully');
    }
  }
}

// Singleton instance
export const redisManager = new RedisManager();

// Auto-connect on import (with fallback)
redisManager.connect().catch(error => {
  console.warn('⚠️ Redis not available, falling back to in-memory state');
});

export default redisManager;
