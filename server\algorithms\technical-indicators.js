/**
 * Technical Indicators Library
 * Teknik analiz göstergeleri hesaplama fonksiyonları
 */

/**
 * Simple Moving Average (Basit Hareketli Ortalama)
 * @param {number[]} values - Fiyat dizisi
 * @param {number} period - Periyot (5, 10, 20, vb.)
 * @returns {number|null} MA değeri
 */
export const calculateMA = (values, period) => {
  if (!values || values.length < period) {
    return null;
  }
  
  const lastValues = values.slice(-period);
  const sum = lastValues.reduce((acc, val) => acc + (parseFloat(val) || 0), 0);
  return sum / period;
};

/**
 * Exponential Moving Average (Üssel Hareketli Ortalama)
 * @param {number[]} values - Fiyat dizisi
 * @param {number} period - Periyot
 * @returns {number|null} EMA değeri
 */
export const calculateEMA = (values, period) => {
  if (!values || values.length < period) {
    return null;
  }
  
  const multiplier = 2 / (period + 1);
  let ema = calculateMA(values.slice(0, period), period);
  
  for (let i = period; i < values.length; i++) {
    ema = (values[i] * multiplier) + (ema * (1 - multiplier));
  }
  
  return ema;
};

/**
 * Relative Strength Index (Göreceli Güç Endeksi)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number} period - Periyot (varsayılan 14)
 * @returns {number|null} RSI değeri (0-100)
 */
export const calculateRSI = (closes, period = 14) => {
  if (!closes || closes.length < period + 1) {
    return null;
  }
  
  let gains = 0;
  let losses = 0;
  
  // İlk periyot için ortalama hesapla
  for (let i = 1; i <= period; i++) {
    const change = closes[i] - closes[i - 1];
    if (change > 0) {
      gains += change;
    } else {
      losses += Math.abs(change);
    }
  }
  
  let avgGain = gains / period;
  let avgLoss = losses / period;
  
  // Sonraki değerler için smoothed average
  for (let i = period + 1; i < closes.length; i++) {
    const change = closes[i] - closes[i - 1];
    const gain = change > 0 ? change : 0;
    const loss = change < 0 ? Math.abs(change) : 0;
    
    avgGain = ((avgGain * (period - 1)) + gain) / period;
    avgLoss = ((avgLoss * (period - 1)) + loss) / period;
  }
  
  if (avgLoss === 0) return 100;
  
  const rs = avgGain / avgLoss;
  return 100 - (100 / (1 + rs));
};

/**
 * MACD (Moving Average Convergence Divergence)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number} fastPeriod - Hızlı EMA periyodu (varsayılan 12)
 * @param {number} slowPeriod - Yavaş EMA periyodu (varsayılan 26)
 * @param {number} signalPeriod - Sinyal EMA periyodu (varsayılan 9)
 * @returns {Object|null} {macd, signal, histogram}
 */
export const calculateMACD = (closes, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) => {
  if (!closes || closes.length < slowPeriod + signalPeriod) {
    return null;
  }
  
  const fastEMA = calculateEMA(closes, fastPeriod);
  const slowEMA = calculateEMA(closes, slowPeriod);
  
  if (!fastEMA || !slowEMA) return null;
  
  const macd = fastEMA - slowEMA;
  
  // MACD değerlerini hesapla
  const macdValues = [];
  for (let i = slowPeriod - 1; i < closes.length; i++) {
    const fast = calculateEMA(closes.slice(0, i + 1), fastPeriod);
    const slow = calculateEMA(closes.slice(0, i + 1), slowPeriod);
    if (fast && slow) {
      macdValues.push(fast - slow);
    }
  }
  
  const signal = calculateEMA(macdValues, signalPeriod);
  const histogram = signal ? macd - signal : 0;
  
  return {
    macd: parseFloat(macd.toFixed(8)),
    signal: parseFloat((signal || 0).toFixed(8)),
    histogram: parseFloat(histogram.toFixed(8))
  };
};

/**
 * Average True Range (Ortalama Gerçek Aralık)
 * @param {number[]} highs - Yüksek fiyatlar
 * @param {number[]} lows - Düşük fiyatlar
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number} period - Periyot (varsayılan 14)
 * @returns {number|null} ATR değeri
 */
export const calculateATR = (highs, lows, closes, period = 14) => {
  if (!highs || !lows || !closes || highs.length < period + 1) {
    return null;
  }
  
  const trueRanges = [];
  
  for (let i = 1; i < highs.length; i++) {
    const high = highs[i];
    const low = lows[i];
    const prevClose = closes[i - 1];
    
    const tr1 = high - low;
    const tr2 = Math.abs(high - prevClose);
    const tr3 = Math.abs(low - prevClose);
    
    trueRanges.push(Math.max(tr1, tr2, tr3));
  }
  
  return calculateMA(trueRanges, period);
};

/**
 * MA Slope (Hareketli Ortalama Eğimi)
 * @param {number[]} values - Değer dizisi
 * @param {number} period - MA periyodu
 * @param {number} lookback - Kaç periyot geriye bakılacak
 * @returns {number|null} Eğim değeri
 */
export const calculateSlope = (values, period = 10, lookback = 1) => {
  if (!values || values.length < period + lookback) {
    return null;
  }
  
  const currentMA = calculateMA(values, period);
  const previousMA = calculateMA(values.slice(0, -lookback), period);
  
  if (!currentMA || !previousMA) return null;
  
  return currentMA - previousMA;
};

/**
 * Bollinger Bands (Bollinger Bantları)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number} period - Periyot (varsayılan 20)
 * @param {number} stdDev - Standart sapma çarpanı (varsayılan 2)
 * @returns {Object|null} {upper, middle, lower}
 */
export const calculateBollingerBands = (closes, period = 20, stdDev = 2) => {
  if (!closes || closes.length < period) {
    return null;
  }
  
  const middle = calculateMA(closes, period);
  if (!middle) return null;
  
  const lastValues = closes.slice(-period);
  const variance = lastValues.reduce((acc, val) => {
    return acc + Math.pow(val - middle, 2);
  }, 0) / period;
  
  const standardDeviation = Math.sqrt(variance);
  
  return {
    upper: middle + (standardDeviation * stdDev),
    middle: middle,
    lower: middle - (standardDeviation * stdDev)
  };
};

/**
 * Volume Weighted Average Price (Hacim Ağırlıklı Ortalama Fiyat)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number[]} volumes - Hacim değerleri
 * @param {number} period - Periyot
 * @returns {number|null} VWAP değeri
 */
export const calculateVWAP = (closes, volumes, period) => {
  if (!closes || !volumes || closes.length < period || volumes.length < period) {
    return null;
  }
  
  const lastCloses = closes.slice(-period);
  const lastVolumes = volumes.slice(-period);
  
  let totalPriceVolume = 0;
  let totalVolume = 0;
  
  for (let i = 0; i < period; i++) {
    totalPriceVolume += lastCloses[i] * lastVolumes[i];
    totalVolume += lastVolumes[i];
  }
  
  return totalVolume > 0 ? totalPriceVolume / totalVolume : null;
};

/**
 * Price Velocity (Fiyat Hızı)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number} minutes - Dakika cinsinden süre
 * @returns {number|null} Yüzde değişim
 */
export const calculatePriceVelocity = (closes, minutes = 1) => {
  if (!closes || closes.length < minutes + 1) {
    return null;
  }
  
  const currentPrice = closes[closes.length - 1];
  const pastPrice = closes[closes.length - 1 - minutes];
  
  return ((currentPrice - pastPrice) / pastPrice) * 100;
};
