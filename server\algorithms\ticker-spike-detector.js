/**
 * Ticker-Based Real-time Spike Detector
 * Context7 node-binance-api ile sürekli ticker monitoring
 */

const { EventEmitter } = require('events');
const Binance = require('node-binance-api');

class TickerSpikeDetector extends EventEmitter {
  constructor(options = {}) {
    super();
    
    // Configuration
    this.config = {
      priceThreshold: options.priceThreshold || 0.2,    // 0.2% above MA10
      slopeThreshold: options.slopeThreshold || 0.05,   // MA10 slope > 0.05
      volumeMultiplier: options.volumeMultiplier || 1.2, // 1.2x average volume
      ma10Period: 10,
      volumePeriod: 20,
      cooldownPeriod: 60000, // 1 dakika
      ...options
    };

    // Binance API setup - Düzeltilmiş
    this.binance = Binance().options({
      APIKEY: process.env.BINANCE_API_KEY || '',
      APISECRET: process.env.BINANCE_API_SECRET || '',
      useServerTime: true,
      recvWindow: 60000,
      verbose: false,
      log: () => {} // Boş log function
    });

    // Market data storage
    this.marketData = new Map();
    this.lastSpikeTime = new Map();
    this.tickerData = new Map();
    this.isActive = false;
    this.symbols = [];

    console.log('🎯 Ticker Spike Detector initialized');
    console.log(`   Price threshold: ${this.config.priceThreshold}%`);
    console.log(`   Volume multiplier: ${this.config.volumeMultiplier}x`);
  }

  /**
   * Start ticker monitoring
   */
  async start(symbols = []) {
    this.symbols = symbols;
    this.isActive = true;

    console.log('🚀 Starting ticker-based spike detection...');
    console.log(`📊 Monitoring ${symbols.length} symbols`);

    // Initialize market data for each symbol
    for (const symbol of symbols) {
      this.marketData.set(symbol, {
        prices: [],
        volumes: [],
        ma10History: [],
        lastUpdate: 0
      });
    }

    // Start ticker WebSocket stream
    this.startTickerStream();

    // Get initial kline data for MA calculations
    await this.initializeMarketData();

    this.emit('started');
  }

  /**
   * Start ticker WebSocket stream - Düzeltilmiş
   */
  startTickerStream() {
    console.log('📡 Starting ticker WebSocket stream...');

    try {
      // Subscribe to mini ticker for all symbols - Düzeltilmiş callback
      this.binance.websockets.miniTicker((ticker) => {
        try {
          if (!this.isActive) return;

          const symbol = ticker.symbol;
          if (!this.symbols.includes(symbol)) return;

          // Update ticker data
          this.tickerData.set(symbol, {
            symbol: ticker.symbol,
            close: parseFloat(ticker.close),
            open: parseFloat(ticker.open),
            high: parseFloat(ticker.high),
            low: parseFloat(ticker.low),
            volume: parseFloat(ticker.volume),
            count: parseInt(ticker.count),
            timestamp: Date.now()
          });

          // Process ticker for spike detection
          this.processTicker(symbol, ticker);
        } catch (error) {
          console.error('❌ Ticker processing error:', error.message);
        }
      });

      console.log('✅ Ticker WebSocket stream started');
    } catch (error) {
      console.error('❌ Failed to start ticker stream:', error.message);
      // Fallback: Disable ticker detector
      this.isActive = false;
    }
  }

  /**
   * Initialize market data with recent klines - Düzeltilmiş
   */
  async initializeMarketData() {
    console.log('📈 Initializing market data...');

    for (const symbol of this.symbols) {
      try {
        // Get last 50 1-minute klines for MA calculations - Düzeltilmiş callback
        this.binance.candlesticks(symbol, '1m', (error, ticks, symbol) => {
          if (error) {
            console.error(`❌ Failed to get klines for ${symbol}:`, error.message);
            return;
          }

          try {
            const data = this.marketData.get(symbol);
            if (data && ticks && ticks.length > 0) {
              data.prices = ticks.slice(-50).map(k => parseFloat(k[4])); // Close prices
              data.volumes = ticks.slice(-50).map(k => parseFloat(k[5])); // Volumes

              // Calculate initial MA10
              if (data.prices.length >= 10) {
                const ma10 = this.calculateMA(data.prices, 10);
                data.ma10History = [ma10];
              }

              console.log(`📊 ${symbol}: Initialized with ${data.prices.length} klines`);
            }
          } catch (processError) {
            console.error(`❌ Failed to process klines for ${symbol}:`, processError.message);
          }
        }, { limit: 50 });

      } catch (error) {
        console.error(`❌ Failed to initialize ${symbol}:`, error.message);
      }
    }

    // Wait a bit for async operations
    setTimeout(() => {
      console.log('✅ Market data initialization complete');
    }, 2000);
  }

  /**
   * Process ticker data for spike detection
   */
  processTicker(symbol, ticker) {
    const data = this.marketData.get(symbol);
    if (!data) return;

    const currentPrice = parseFloat(ticker.close);
    const currentVolume = parseFloat(ticker.volume);

    // Update price and volume arrays
    data.prices.push(currentPrice);
    data.volumes.push(currentVolume);

    // Keep only last 50 data points
    if (data.prices.length > 50) {
      data.prices.shift();
      data.volumes.shift();
    }

    // Need at least 10 prices for MA10
    if (data.prices.length < 10) return;

    // Calculate MA10
    const ma10 = this.calculateMA(data.prices, 10);
    data.ma10History.push(ma10);

    // Keep only last 20 MA10 values for slope
    if (data.ma10History.length > 20) {
      data.ma10History.shift();
    }

    // Check for spike
    this.checkForSpike(symbol, currentPrice, currentVolume, ma10);

    // Emit real-time MA10 update
    this.emit('ma10-update', {
      symbol,
      price: currentPrice,
      ma10,
      timestamp: Date.now()
    });
  }

  /**
   * Spike detection logic
   */
  checkForSpike(symbol, currentPrice, currentVolume, ma10) {
    const data = this.marketData.get(symbol);
    if (!data || data.ma10History.length < 3) return;

    // Cooldown check
    const lastSpike = this.lastSpikeTime.get(symbol) || 0;
    if (Date.now() - lastSpike < this.config.cooldownPeriod) return;

    // 1. Price above MA10 check
    const priceAboveMA = ((currentPrice - ma10) / ma10) * 100;
    if (priceAboveMA < this.config.priceThreshold) return;

    // 2. MA10 slope check
    const slope = this.calculateSlope(data.ma10History);
    if (slope < this.config.slopeThreshold) return;

    // 3. Volume check
    const avgVolume = this.calculateMA(data.volumes, this.config.volumePeriod);
    const volumeRatio = avgVolume > 0 ? currentVolume / avgVolume : 0;
    if (volumeRatio < this.config.volumeMultiplier) return;

    // SPIKE DETECTED!
    const spikeData = {
      symbol,
      price: currentPrice,
      spikePercent: parseFloat(priceAboveMA.toFixed(2)),
      slope: parseFloat(slope.toFixed(4)),
      volumeRatio: parseFloat(volumeRatio.toFixed(2)),
      ma10: parseFloat(ma10.toFixed(8)),
      avgVolume: parseFloat(avgVolume.toFixed(2)),
      timestamp: Date.now(),
      confidence: this.calculateConfidence(priceAboveMA, slope, volumeRatio),
      detectorType: 'TICKER'
    };

    // Set cooldown
    this.lastSpikeTime.set(symbol, Date.now());

    console.log(`🎯 TICKER SPIKE DETECTED: ${symbol}`);
    console.log(`   Price: $${currentPrice} (${priceAboveMA.toFixed(2)}% above MA10)`);
    console.log(`   MA10: $${ma10.toFixed(4)} | Slope: ${slope.toFixed(4)}`);
    console.log(`   Volume: ${volumeRatio.toFixed(1)}x average`);
    console.log(`   Confidence: ${spikeData.confidence}%`);

    this.emit('spike', spikeData);
  }

  /**
   * Calculate Moving Average
   */
  calculateMA(values, period) {
    if (values.length < period) return 0;
    const slice = values.slice(-period);
    const sum = slice.reduce((acc, val) => acc + val, 0);
    return sum / period;
  }

  /**
   * Calculate slope of MA10
   */
  calculateSlope(ma10History) {
    if (ma10History.length < 3) return 0;
    const recent = ma10History.slice(-3);
    return recent[2] - recent[0]; // Simple slope over 3 periods
  }

  /**
   * Calculate confidence score
   */
  calculateConfidence(priceAboveMA, slope, volumeRatio) {
    let confidence = 0;

    // Price component (0-40 points)
    if (priceAboveMA > 1.0) confidence += 40;
    else if (priceAboveMA > 0.5) confidence += 30;
    else if (priceAboveMA > 0.3) confidence += 20;
    else confidence += 10;

    // Slope component (0-30 points)
    if (slope > 0.2) confidence += 30;
    else if (slope > 0.1) confidence += 20;
    else if (slope > 0.05) confidence += 15;
    else confidence += 10;

    // Volume component (0-30 points)
    if (volumeRatio > 2.0) confidence += 30;
    else if (volumeRatio > 1.5) confidence += 20;
    else if (volumeRatio > 1.2) confidence += 15;
    else confidence += 10;

    return Math.min(100, confidence);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Ticker detector config updated:', newConfig);
    this.emit('configUpdated', this.config);
  }

  /**
   * Update from dashboard
   */
  updateFromDashboard(dashboardConfig) {
    const detectorConfig = {
      volumeMultiplier: dashboardConfig.hacimCarpani || this.config.volumeMultiplier,
      priceThreshold: dashboardConfig.spikeEsigi || this.config.priceThreshold
    };
    
    this.updateConfig(detectorConfig);
    console.log('📊 Ticker detector updated from dashboard:', detectorConfig);
  }

  /**
   * Stop monitoring
   */
  stop() {
    this.isActive = false;
    console.log('🔴 Ticker Spike Detector stopped');
    this.emit('stopped');
  }

  /**
   * Get current ticker data
   */
  getTickerData(symbol) {
    return this.tickerData.get(symbol);
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      symbolsTracked: this.symbols.length,
      isActive: this.isActive,
      config: this.config,
      tickerDataCount: this.tickerData.size
    };
  }
}

module.exports = { TickerSpikeDetector };
