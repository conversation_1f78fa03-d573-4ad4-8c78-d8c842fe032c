import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import fs from "fs";
import path from "path";
import crypto from "crypto";
import session from "express-session";

// Extend session interface
declare module 'express-session' {
  interface SessionData {
    user?: {
      username: string;
      id: string;
    };
  }
}

// Helper functions
function getUserCredentials(username: string) {
  try {
    const usersPath = path.join(import.meta.dirname, 'users.json');
    const usersData = fs.readFileSync(usersPath, 'utf-8');
    const usersJson = JSON.parse(usersData);

    const user = usersJson.users.find((u: any) => u.username === username);
    if (!user) return null;

    return {
      apiKey: user.apiKey,
      secretKey: user.secretKey
    };
  } catch (error) {
    console.error('Error reading user credentials:', error);
    return null;
  }
}

function requireAuth(req: Request, res: Response, next: any) {
  if (!req.session?.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
}

async function binanceRequest(endpoint: string, params: any = {}, method: string = 'GET', credentials: any) {
  const timestamp = Date.now();
  const queryString = new URLSearchParams({
    ...params,
    timestamp: timestamp.toString()
  }).toString();

  const signature = crypto
    .createHmac('sha256', credentials.secretKey)
    .update(queryString)
    .digest('hex');

  const signedQueryString = `${queryString}&signature=${signature}`;
  const url = `https://fapi.binance.com${endpoint}?${signedQueryString}`;

  const response = await fetch(url, {
    method,
    headers: {
      'X-MBX-APIKEY': credentials.apiKey,
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    throw new Error(`Binance API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Session middleware
  app.use(session({
    secret: 'your-secret-key-here',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 hours
  }));

  // Authentication endpoints
  app.post('/api/auth/login', async (req: Request, res: Response) => {
    try {
      const { kullaniciAdi, sifre } = req.body;

      if (!kullaniciAdi || !sifre) {
        return res.status(400).json({ error: 'Kullanıcı adı ve şifre gereklidir' });
      }

      // Read users from JSON file
      const usersPath = path.join(import.meta.dirname, 'users.json');
      const usersData = fs.readFileSync(usersPath, 'utf-8');
      const usersJson = JSON.parse(usersData);
      const users = usersJson.users;

      const user = users.find((u: any) =>
        u.username === kullaniciAdi &&
        u.password === sifre
      );

      if (user) {
        const { password: _, ...userWithoutPassword } = user;
        const token = `token_${user.username}_${Date.now()}`;

        // Set session
        req.session.user = {
          username: user.username,
          id: user.id || user.username
        };

        res.json({
          user: {
            ...userWithoutPassword,
            sonGiris: Date.now()
          },
          token
        });
      } else {
        res.status(401).json({ error: 'Kullanıcı adı veya şifre hatalı' });
      }
    } catch (error) {
      res.status(500).json({ error: 'Sunucu hatası' });
    }
  });

  // Proxy crypto server endpoints with proper forwarding
  app.use('/api/account', (req: Request, res: Response) => {
    const url = `http://parabot.fun:3001${req.originalUrl}`;
    console.log(`🔗 Proxying ${req.method} ${req.originalUrl} to ${url}`);
    
    // Forward the request with proper headers and session
    const options = {
      method: req.method,
      headers: {
        ...req.headers,
        'Cookie': req.headers.cookie || '',
        'Content-Type': 'application/json'
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    };
    
    fetch(url, options)
      .then(response => {
        console.log(`📡 Response from crypto server: ${response.status}`);
        return response.json();
      })
      .then(data => {
        res.json(data);
      })
      .catch(error => {
        console.error('Proxy error:', error);
        res.status(500).json({ error: 'Proxy error' });
      });
  });

  app.use('/api/stream', (req: Request, res: Response) => {
    const url = `http://parabot.fun:3001${req.originalUrl}`;
    console.log(`🔗 Proxying ${req.method} ${req.originalUrl} to ${url}`);
    
    const options = {
      method: req.method,
      headers: {
        ...req.headers,
        'Cookie': req.headers.cookie || '',
        'Content-Type': 'application/json'
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    };
    
    fetch(url, options)
      .then(response => response.json())
      .then(data => {
        res.json(data);
      })
      .catch(error => {
        console.error('Proxy error:', error);
        res.status(500).json({ error: 'Proxy error' });
      });
  });

  app.use('/api/debug', (req: Request, res: Response) => {
    const url = `http://parabot.fun:3001${req.originalUrl}`;
    console.log(`🔗 Proxying ${req.method} ${req.originalUrl} to ${url}`);
    
    const options = {
      method: req.method,
      headers: {
        ...req.headers,
        'Cookie': req.headers.cookie || '',
        'Content-Type': 'application/json'
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    };
    
    fetch(url, options)
      .then(response => response.json())
      .then(data => {
        res.json(data);
      })
      .catch(error => {
        console.error('Proxy error:', error);
        res.status(500).json({ error: 'Proxy error' });
      });
  });

  // Refresh test endpoint
  app.get('/api/refresh/test', (req: Request, res: Response) => {
    console.log('🔧 Refresh test endpoint hit');
    res.json({
      success: true,
      message: 'Refresh test endpoint working',
      timestamp: Date.now()
    });
  });

  // Refresh positions API
  console.log('🔧 Registering /api/refresh/positions endpoint');
  app.get('/api/refresh/positions', requireAuth, async (req: Request, res: Response) => {
    try {
      console.log('🔄 /api/refresh/positions endpoint çağrıldı');
      const username = req.session.user!.username;
      console.log(`👤 Username: ${username}`);
      const credentials = getUserCredentials(username);

      if (!credentials) {
        return res.status(400).json({
          success: false,
          error: 'Kullanıcı credentials bulunamadı'
        });
      }

      console.log(`🔄 ${username}: Refreshing positions from Binance...`);

      const positions = await binanceRequest('/fapi/v2/positionRisk', {}, 'GET', credentials);

      // Filter only positions with size > 0
      const activePositions = positions.filter((pos: any) =>
        parseFloat(pos.positionAmt) !== 0
      );

      console.log(`📊 ${username}: Found ${activePositions.length} active positions`);

      const formattedPositions = activePositions.map((pos: any) => {
        const positionAmt = parseFloat(pos.positionAmt);
        const side = positionAmt > 0 ? 'LONG' : 'SHORT';
        const entryPrice = parseFloat(pos.entryPrice);
        const markPrice = parseFloat(pos.markPrice);
        const unrealizedPnl = parseFloat(pos.unRealizedProfit);
        const pnlPercent = parseFloat(pos.percentage || '0');

        return {
          id: `${pos.symbol}_${Date.now()}`,
          sembol: pos.symbol,
          yon: side,
          miktar: Math.abs(positionAmt),
          girisFiyati: entryPrice,
          guncelFiyat: markPrice,
          pnl: unrealizedPnl,
          pnlYuzdesi: pnlPercent,
          kaldirac: parseFloat(pos.leverage || '20'),
          durum: 'ACIK',
          acilisZamani: Date.now() - (24 * 60 * 60 * 1000), // Approximate
          stopLoss: null,
          takeProfit: null
        };
      });

      res.json({
        success: true,
        positions: formattedPositions,
        timestamp: Date.now()
      });

    } catch (error: any) {
      const username = req.session?.user?.username || 'Unknown';
      console.error(`❌ ${username}: Position refresh error:`, error.message);
      res.status(500).json({
        success: false,
        error: 'Pozisyonlar yenilenemedi. Lütfen tekrar deneyin.'
      });
    }
  });

  // Refresh orders API
  console.log('🔧 Registering /api/refresh/orders endpoint');
  app.get('/api/refresh/orders', requireAuth, async (req: Request, res: Response) => {
    try {
      const username = req.session.user!.username;
      const credentials = getUserCredentials(username);

      console.log(`🔄 ${username}: Refreshing orders from Binance...`);

      const orders = await binanceRequest('/fapi/v1/openOrders', {}, 'GET', credentials);

      console.log(`📋 ${username}: Found ${orders.length} open orders`);

      const formattedOrders = orders.map((order: any) => ({
        id: order.orderId.toString(),
        sembol: order.symbol,
        tip: order.type === 'LIMIT' ? 'LIMIT' : order.type === 'STOP_MARKET' ? 'STOP_LOSS' : 'MARKET',
        yon: order.side,
        miktar: parseFloat(order.origQty),
        fiyat: parseFloat(order.price || '0'),
        durum: order.status,
        olusturulmaZamani: order.time,
        guncellemeZamani: order.updateTime
      }));

      res.json({
        success: true,
        orders: formattedOrders,
        timestamp: Date.now()
      });

    } catch (error: any) {
      const username = req.session?.user?.username || 'Unknown';
      console.error(`❌ ${username}: Order refresh error:`, error.message);
      res.status(500).json({
        success: false,
        error: 'Emirler yenilenemedi. Lütfen tekrar deneyin.'
      });
    }
  });

  // Cancel specific order API
  console.log('🔧 Registering /api/order/cancel endpoint');
  app.post('/api/order/cancel', requireAuth, async (req: Request, res: Response) => {
    try {
      const { orderId, symbol } = req.body;
      const username = req.session.user!.username;
      const credentials = getUserCredentials(username);

      if (!orderId || !symbol) {
        return res.status(400).json({
          success: false,
          error: 'OrderId ve Symbol gereklidir'
        });
      }

      console.log(`🗑️ ${username}: Cancelling order ${orderId} for ${symbol}`);

      // REAL Binance order cancellation
      const cancelResult = await binanceRequest('/fapi/v1/order', {
        symbol: symbol,
        orderId: orderId
      }, 'DELETE', credentials);

      console.log(`✅ ${username}: Order ${orderId} cancelled successfully`);

      res.json({
        success: true,
        message: 'Emir başarıyla iptal edildi',
        cancelledOrder: cancelResult
      });

    } catch (error: any) {
      const username = req.session?.user?.username || 'Unknown';
      console.error(`❌ ${username}: Order cancellation error for ${req.body.orderId}:`, error.response?.data || error.message);
      res.status(500).json({
        success: false,
        error: error.response?.data?.msg || error.message || 'Emir iptal edilemedi'
      });
    }
  });

  // Cancel all orders API
  console.log('🔧 Registering /api/orders/cancel-all endpoint');
  app.post('/api/orders/cancel-all', requireAuth, async (req: Request, res: Response) => {
    try {
      const { symbol } = req.body; // Optional: cancel all orders for specific symbol
      const username = req.session.user!.username;
      const credentials = getUserCredentials(username);

      console.log(`🗑️ ${username}: Cancelling all orders${symbol ? ` for ${symbol}` : ''}`);

      let cancelResult;

      if (symbol) {
        // Cancel all orders for specific symbol
        cancelResult = await binanceRequest('/fapi/v1/openOrders', {
          symbol: symbol
        }, 'DELETE', credentials);
      } else {
        // Cancel all open orders
        cancelResult = await binanceRequest('/fapi/v1/allOpenOrders', {}, 'DELETE', credentials);
      }

      console.log(`✅ ${username}: All orders cancelled successfully`);

      res.json({
        success: true,
        message: symbol ? `${symbol} için tüm emirler iptal edildi` : 'Tüm emirler iptal edildi',
        cancelledOrders: cancelResult
      });

    } catch (error: any) {
      const username = req.session?.user?.username || 'Unknown';
      console.error(`❌ ${username}: Cancel all orders error:`, error.response?.data || error.message);
      res.status(500).json({
        success: false,
        error: error.response?.data?.msg || error.message || 'Emirler iptal edilemedi'
      });
    }
  });

  // Position close endpoint
  app.post('/api/position/close', requireAuth, async (req: Request, res: Response) => {
    try {
      const { symbol, positionId } = req.body;
      const username = req.session.user!.username;
      const credentials = getUserCredentials(username);

      if (!credentials) {
        return res.status(400).json({
          success: false,
          error: 'Kullanıcı credentials bulunamadı'
        });
      }

      console.log(`🔄 ${username}: Closing position for ${symbol}`);

      // Get current position info first
      const positions = await binanceRequest('/fapi/v2/positionRisk', {
        symbol: symbol
      }, 'GET', credentials);

      const position = positions.find((pos: any) => pos.symbol === symbol && parseFloat(pos.positionAmt) !== 0);
      
      if (!position) {
        return res.status(400).json({
          success: false,
          error: 'Pozisyon bulunamadı veya zaten kapalı'
        });
      }

      const positionAmt = parseFloat(position.positionAmt);
      const closeQuantity = Math.abs(positionAmt);
      const closeSide = positionAmt > 0 ? 'SELL' : 'BUY'; // Opposite side to close

      // Place market order to close position
      const closeOrder = await binanceRequest('/fapi/v1/order', {
        symbol: symbol,
        side: closeSide,
        type: 'MARKET',
        quantity: closeQuantity.toString(),
        reduceOnly: true
      }, 'POST', credentials);

      console.log(`✅ ${username}: Position closed for ${symbol}:`, closeOrder);

      res.json({
        success: true,
        orderId: closeOrder.orderId,
        symbol: symbol,
        quantity: closeQuantity,
        side: closeSide
      });

    } catch (error: any) {
      const username = req.session?.user?.username || 'Unknown';
      console.error(`❌ ${username}: Position close error:`, error.message);
      res.status(500).json({
        success: false,
        error: 'Pozisyon kapatılamadı. Lütfen tekrar deneyin.'
      });
    }
  });

  // Proxy refresh endpoints (fallback)
  app.use('/api/refresh', (req: Request, res: Response) => {
    const url = `http://parabot.fun:3001${req.originalUrl}`;
    console.log(`🔗 REFRESH PROXY: ${req.method} ${req.originalUrl} → ${url}`);

    const options = {
      method: req.method,
      headers: {
        ...req.headers,
        'Cookie': req.headers.cookie || '',
        'Content-Type': 'application/json'
      },
      body: req.method !== 'GET' ? JSON.stringify(req.body) : undefined
    };

    fetch(url, options)
      .then(response => response.json())
      .then(data => {
        res.json(data);
      })
      .catch(error => {
        console.error('Refresh proxy error:', error);
        res.status(500).json({ 
          success: false, 
          error: 'Endpoint bulunamadı' 
        });
      });
  });

  const httpServer = createServer(app);

  return httpServer;
}
