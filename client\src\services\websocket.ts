// ParaBOT WebSocket Service
import { WebSocketMessage, KlineData, OrderUpdate, AccountUpdate } from '@/types/trading';

export class BinanceWebSocketService {
  private wsConnections: Map<string, WebSocket> = new Map();
  private listeners: Map<string, Set<(data: any) => void>> = new Map();
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000;

  // Binance WebSocket URLs
  private baseUrl = 'wss://fstream.binance.com';
  private userDataStream?: string;

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Handle page visibility change to manage connections
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseConnections();
      } else {
        this.resumeConnections();
      }
    });
  }

  // Market Data Stream
  async subscribeToKline(symbol: string, interval: string = '1m'): Promise<void> {
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`;
    const url = `${this.baseUrl}/ws/${streamName}`;
    
    await this.createConnection(streamName, url, (data) => {
      if (data.k) {
        const klineData: KlineData = {
          symbol: data.k.s,
          openTime: data.k.t,
          closeTime: data.k.T,
          open: parseFloat(data.k.o),
          high: parseFloat(data.k.h),
          low: parseFloat(data.k.l),
          close: parseFloat(data.k.c),
          volume: parseFloat(data.k.v),
          interval: data.k.i
        };
        this.emit('kline', klineData);
      }
    });
  }

  // Mark Price Stream
  async subscribeToMarkPrice(symbol: string): Promise<void> {
    const streamName = `${symbol.toLowerCase()}@markPrice`;
    const url = `${this.baseUrl}/ws/${streamName}`;
    
    await this.createConnection(streamName, url, (data) => {
      this.emit('markPrice', {
        symbol: data.s,
        markPrice: parseFloat(data.p),
        timestamp: data.E
      });
    });
  }

  // User Data Stream (requires listen key from REST API)
  async subscribeToUserData(listenKey: string): Promise<void> {
    if (!listenKey) {
      throw new Error('Listen key required for user data stream');
    }

    this.userDataStream = listenKey;
    const streamName = 'userData';
    const url = `${this.baseUrl}/ws/${listenKey}`;
    
    await this.createConnection(streamName, url, (data) => {
      switch (data.e) {
        case 'ORDER_TRADE_UPDATE':
          const orderUpdate: OrderUpdate = {
            symbol: data.o.s,
            orderId: data.o.i,
            type: data.o.o,
            side: data.o.S,
            status: data.o.X,
            executedQty: parseFloat(data.o.z),
            price: parseFloat(data.o.p),
            timestamp: data.E
          };
          this.emit('orderUpdate', orderUpdate);
          break;

        case 'ACCOUNT_UPDATE':
          const accountUpdate: AccountUpdate = {
            balances: data.a.B.map((b: any) => ({
              asset: b.a,
              walletBalance: parseFloat(b.wb),
              unrealizedPnl: parseFloat(b.up)
            })),
            positions: data.a.P.map((p: any) => ({
              id: `${p.s}_${Date.now()}`,
              symbol: p.s,
              side: parseFloat(p.pa) > 0 ? 'LONG' : 'SHORT',
              size: Math.abs(parseFloat(p.pa)),
              entryPrice: parseFloat(p.ep),
              markPrice: parseFloat(p.mp),
              pnl: parseFloat(p.up),
              pnlPercentage: parseFloat(p.up) / parseFloat(p.ep) * 100,
              leverage: 20, // Default, should come from position mode
              status: parseFloat(p.pa) === 0 ? 'CLOSED' : 'OPEN',
              openTime: Date.now()
            })),
            timestamp: data.E
          };
          this.emit('accountUpdate', accountUpdate);
          break;

        case 'POSITION_UPDATE':
          this.emit('positionUpdate', data);
          break;
      }
    });
  }

  // Generic connection creation
  private async createConnection(
    streamName: string, 
    url: string, 
    messageHandler: (data: any) => void
  ): Promise<void> {
    if (this.wsConnections.has(streamName)) {
      this.closeConnection(streamName);
    }

    return new Promise((resolve, reject) => {
      try {
        const ws = new WebSocket(url);
        
        ws.onopen = () => {
          console.log(`✅ WebSocket connected: ${streamName}`);
          this.reconnectAttempts.set(streamName, 0);
          resolve();
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            messageHandler(data);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        ws.onclose = (event) => {
          console.log(`🔌 WebSocket closed: ${streamName}`, event.code);
          this.wsConnections.delete(streamName);
          
          if (!event.wasClean) {
            this.attemptReconnect(streamName, url, messageHandler);
          }
        };

        ws.onerror = (error) => {
          console.error(`❌ WebSocket error: ${streamName}`, error);
          reject(error);
        };

        this.wsConnections.set(streamName, ws);
      } catch (error) {
        reject(error);
      }
    });
  }

  // Reconnection logic
  private async attemptReconnect(
    streamName: string, 
    url: string, 
    messageHandler: (data: any) => void
  ): Promise<void> {
    const attempts = this.reconnectAttempts.get(streamName) || 0;
    
    if (attempts >= this.maxReconnectAttempts) {
      console.error(`❌ Max reconnection attempts reached for ${streamName}`);
      this.emit('connectionError', { streamName, error: 'Max reconnection attempts reached' });
      return;
    }

    this.reconnectAttempts.set(streamName, attempts + 1);
    
    console.log(`🔄 Attempting to reconnect ${streamName} (${attempts + 1}/${this.maxReconnectAttempts})...`);
    
    setTimeout(async () => {
      try {
        await this.createConnection(streamName, url, messageHandler);
        this.emit('connectionRestored', { streamName });
      } catch (error) {
        console.error(`Failed to reconnect ${streamName}:`, error);
      }
    }, this.reconnectDelay * (attempts + 1));
  }

  // Event system
  private emit(event: string, data: any): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  public on(event: string, listener: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }

  public off(event: string, listener: (data: any) => void): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(listener);
    }
  }

  // Connection management
  private pauseConnections(): void {
    this.wsConnections.forEach((ws, streamName) => {
      if (ws.readyState === WebSocket.OPEN) {
        console.log(`⏸️ Pausing connection: ${streamName}`);
        ws.close(1000, 'Page hidden');
      }
    });
  }

  private resumeConnections(): void {
    console.log('🔄 Resuming WebSocket connections...');
    // Connections will auto-reconnect via onclose handler
  }

  public closeConnection(streamName: string): void {
    const ws = this.wsConnections.get(streamName);
    if (ws) {
      ws.close(1000, 'Manual close');
      this.wsConnections.delete(streamName);
    }
  }

  public closeAllConnections(): void {
    this.wsConnections.forEach((ws, streamName) => {
      this.closeConnection(streamName);
    });
    this.listeners.clear();
  }

  // Health check
  public getConnectionStatus(): Record<string, string> {
    const status: Record<string, string> = {};
    this.wsConnections.forEach((ws, streamName) => {
      switch (ws.readyState) {
        case WebSocket.CONNECTING:
          status[streamName] = 'CONNECTING';
          break;
        case WebSocket.OPEN:
          status[streamName] = 'CONNECTED';
          break;
        case WebSocket.CLOSING:
          status[streamName] = 'CLOSING';
          break;
        case WebSocket.CLOSED:
          status[streamName] = 'CLOSED';
          break;
        default:
          status[streamName] = 'UNKNOWN';
      }
    });
    return status;
  }
}

export const wsService = new BinanceWebSocketService();