# 🚀 Optimized Binance Futures Spike Detection System

Bu sistem, Binance Futures üzerinde çalışan gelişmiş teknik analiz tabanlı spike detection algoritmasıdır. WebSocket bağlantısı ile gerçek zamanlı veri alır ve belirtilen kriterlere göre spike sinyalleri üretir.

## 📊 Teknik Kriterler

Sistem aşağıdaki **5 zorunlu kriteri** kontrol eder:

### 1. 📈 MA10 ve MA5 Farkı
- **Kriter**: MA5 > MA10 (Moving Average Crossover)
- **Açıklama**: 5 periyotluk hareketli ortalama, 10 periyotluk hareketli ortalamadan büyük olmalı
- **Skor**: 0-3 puan (farka göre)

### 2. 🎯 RSI > 60 (Zorunlu)
- **Kriter**: RSI (Relative Strength Index) 60'ın üstünde olmalı
- **Açıklama**: Momentum göstergesi, al<PERSON><PERSON> baskısını gösterir
- **Skor**: 1-3 puan (RSI değerine göre)

### 3. 📊 MACD > 0 (Zorunlu)
- **Kriter**: MACD (Moving Average Convergence Divergence) 0'ın üstünde olmalı
- **Açıklama**: Trend onayı için kullanılır
- **Skor**: 1-3 puan (MACD değerine göre)

### 4. 📈 Volume > 1.5x Ortalama (Zorunlu)
- **Kriter**: Mevcut hacim, 20 periyotluk ortalama hacmin 1.5 katından fazla olmalı
- **Açıklama**: Hacim patlaması, güçlü hareket sinyali
- **Skor**: 1-3 puan (hacim oranına göre)

### 5. ⚡ Fiyat Hızı 1 Dakikada %1+ (Zorunlu)
- **Kriter**: Son 1 dakikada fiyat değişimi %1'den fazla olmalı
- **Açıklama**: Hızlı fiyat hareketi, spike potansiyeli
- **Skor**: 1-3 puan (hız değerine göre)

### 6. 🎯 Güven Skoru %60+ (Zorunlu)
- **Kriter**: Toplam skor %60'ın üstünde olmalı
- **Hesaplama**: (Toplam Skor / 15) * 100
- **Açıklama**: Genel güvenilirlik seviyesi

## ⚙️ Sistem Özellikleri

- **WebSocket Bağlantısı**: Gerçek zamanlı Binance Futures verisi
- **Cooldown Süresi**: 30 saniye (aynı sembol için)
- **Event Emit**: Her spike için "spike" eventi emit edilir
- **ES6 Modül Yapısı**: Modern JavaScript syntax
- **Async/Await Uyumlu**: Asenkron işlemler
- **Class Yapısında**: OOP yaklaşımı

## 🚀 Kullanım

### 1. Temel Kullanım

```javascript
import { SpikeDetector } from './server/algorithms/spike-detection.js';

const detector = new SpikeDetector({
  algorithm: 'advanced',
  cooldownPeriod: 30000, // 30 saniye
  symbols: ['BTCUSDT', 'ETHUSDT'] // İzlenecek semboller
});

detector.on('spike', (spike) => {
  console.log('🚨 Spike detected:', spike);
});

detector.connect();
```

### 2. Demo Çalıştırma

```bash
node demo-spike-detector.js
```

### 3. Test Çalıştırma

```bash
node test-spike-detection.js
```

## 📋 Spike Objesi Formatı

```javascript
{
  id: "BTCUSDT_1234567890",
  sembol: "BTCUSDT",
  zaman: 1234567890,
  fiyat: 45000.00,
  ma5: 45100.50,
  ma10: 44950.25,
  maDifference: 0.3345, // %
  egim: 0.0123,
  hacim: 2500.0,
  ortalamHacim: 1000.0,
  rsi: 65.25,
  macd: 123.45,
  macdSignal: 120.30,
  macdHistogram: 3.15,
  priceGap: 1.25, // %
  velocity1m: 2.50, // %
  volumeRatio: 2.5, // x
  confidence: 80.0, // %
  totalScore: 12,
  scores: {
    maDiff: 2,
    rsi: 2,
    macd: 3,
    volume: 3,
    velocity: 2
  },
  guvenilirlik: "YUKSEK", // YUKSEK, ORTA, DUSUK
  durum: "TESPIT_EDILDI",
  algorithm: "ADVANCED_OPTIMIZED"
}
```

## 🔧 Konfigürasyon Seçenekleri

```javascript
const config = {
  algorithm: 'advanced',        // Algoritma türü
  cooldownPeriod: 30000,       // Cooldown süresi (ms)
  symbols: [],                 // İzlenecek semboller (boşsa tümü)
  minDataPoints: 30,           // Minimum veri noktası
  maxDataPoints: 50,           // Maksimum veri noktası
  cacheTimeout: 5000           // Cache timeout (ms)
};
```

## 📊 Event'ler

- **'connected'**: WebSocket bağlantısı kuruldu
- **'disconnected'**: WebSocket bağlantısı kesildi
- **'spike'**: Spike tespit edildi
- **'error'**: Hata oluştu

## 🧪 Test Sonuçları

Test dosyası çalıştırıldığında örnek çıktı:

```
🚨 SPIKE DETECTED!
📊 Spike Details:
   Symbol: BTCUSDT
   Price: $46,000.00
   MA5: $51,225.48
   MA10: $48,810.99
   MA Difference: 4.9466%
   RSI: 95.16
   MACD: 2024.58540319
   Volume Ratio: 1.96x
   Price Velocity (1m): 2.00%
   Confidence: 80.00%
   Reliability: ORTA
   Algorithm: advanced
```

## 🔍 Algoritma Detayları

### Skor Hesaplama

1. **MA Difference Score** (0-3):
   - 3 puan: %0.5+ fark
   - 2 puan: %0.2-0.5 fark
   - 1 puan: %0-0.2 fark

2. **RSI Score** (1-3):
   - 3 puan: RSI > 70
   - 2 puan: RSI 65-70
   - 1 puan: RSI 60-65

3. **MACD Score** (1-3):
   - 3 puan: MACD > Signal ve Histogram > 0
   - 2 puan: MACD > Signal
   - 1 puan: MACD > 0

4. **Volume Score** (1-3):
   - 3 puan: 3x+ ortalama hacim
   - 2 puan: 2-3x ortalama hacim
   - 1 puan: 1.5-2x ortalama hacim

5. **Velocity Score** (1-3):
   - 3 puan: %3+ hız
   - 2 puan: %2-3 hız
   - 1 puan: %1-2 hız

### Güven Skoru

```
Confidence = (Total Score / 15) * 100
```

- **80%+**: YUKSEK güvenilirlik
- **60-80%**: ORTA güvenilirlik
- **<60%**: Spike tespit edilmez

## 🚨 Önemli Notlar

1. **Zorunlu Kriterler**: RSI > 60, MACD > 0, Volume > 1.5x, Velocity > 1% kriterleri sağlanmazsa spike tespit edilmez
2. **Cooldown**: Aynı sembol için 30 saniye cooldown vardır
3. **Minimum Veri**: En az 30 kline verisi gereklidir
4. **WebSocket**: Binance Futures miniTicker stream kullanılır

## 📈 Performans

- **Gerçek Zamanlı**: WebSocket ile anlık veri
- **Düşük Latency**: Optimize edilmiş hesaplamalar
- **Yüksek Doğruluk**: 5 farklı teknik kriter
- **Anti-Spam**: Cooldown sistemi ile spam önleme
