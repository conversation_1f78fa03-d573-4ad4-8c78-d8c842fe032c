/**
 * Financial Spike Detection Expert System
 * Implements professional-grade spike detection using 1-minute Binance klines
 * 
 * Spike Criteria:
 * - Price > MA10 by more than 0.4%
 * - MA10 slope > 0.1
 * - Volume > 1.5x average volume
 * - Trend direction = UPTREND or BULLISH consolidation
 */

import { EventEmitter } from 'events';
import { calculateMA, calculateSlope } from './technical-indicators.js';
import { getTrendDirection, detectConsolidation } from './trend-analysis.js';

export class FinancialSpikeDetector extends EventEmitter {
  constructor(options = {}) {
    super();
    
    // Configuration - Ana Panel Ayarları ile Senkronize
    this.config = {
      priceThreshold: options.priceThreshold || 1.1,   // Dashboard spikeEsigi ile aynı (1.1%)
      slopeThreshold: options.slopeThreshold || 0.01,   // Düşük slope
      volumeMultiplier: options.volumeMultiplier || 1.5, // Dashboard hacimCarpani ile aynı
      ma10Period: options.ma10Period || 10,             // MA10 period (dashboard maPeriyodu)
      volumePeriod: options.volumePeriod || 20,         // Volume average period
      minDataPoints: options.minDataPoints || 3,        // Az data yeterli
      cooldownPeriod: options.cooldownPeriod || 5000,   // 5 saniye cooldown (daha hızlı)
      enableMA200Filter: options.enableMA200Filter || false, // MA200 filtresi kapalı
      ...options
    };
    
    // State management
    this.marketData = new Map();
    this.lastSpikeTime = new Map();
    this.isActive = false;
    
    console.log('🔬 Financial Spike Detector initialized with criteria:');
    console.log(`   Price threshold: ${this.config.priceThreshold}% above MA10`);
    console.log(`   Slope threshold: ${this.config.slopeThreshold}`);
    console.log(`   Volume multiplier: ${this.config.volumeMultiplier}x`);
  }

  /**
   * Add market data for a symbol
   */
  addMarketData(symbol, klineData) {
    // Debug log every 10th data point for better visibility
    if (Math.random() < 0.1) {
      console.log(`🔬 Financial Detector: Received data for ${symbol} - Price: ${klineData.c}, Volume: ${klineData.v}`);
    }

    if (!this.marketData.has(symbol)) {
      this.marketData.set(symbol, {
        closes: [],
        highs: [],
        lows: [],
        volumes: [],
        timestamps: []
      });
      console.log(`📊 Financial Detector: Started tracking ${symbol}`);
    }

    const data = this.marketData.get(symbol);
    const close = parseFloat(klineData.c);
    const high = parseFloat(klineData.h);
    const low = parseFloat(klineData.l);
    const volume = parseFloat(klineData.v);
    const timestamp = parseInt(klineData.t);

    // Add new data
    data.closes.push(close);
    data.highs.push(high);
    data.lows.push(low);
    data.volumes.push(volume);
    data.timestamps.push(timestamp);

    // Keep only last 100 data points for efficiency
    if (data.closes.length > 100) {
      data.closes.shift();
      data.highs.shift();
      data.lows.shift();
      data.volumes.shift();
      data.timestamps.shift();
    }

    // Check for spike if we have enough data
    if (data.closes.length >= this.config.minDataPoints) {
      this.checkForSpike(symbol, klineData);
    } else if (data.closes.length === this.config.minDataPoints - 1) {
      console.log(`🔄 ${symbol}: Almost ready for spike detection (${data.closes.length}/${this.config.minDataPoints})`);
    }

    // Emit kline update for real-time MA10 tracking (only if we have enough data for MA10)
    if (data.closes.length >= 10) {
      const ma10 = this.calculateMA(data.closes, 10);
      const klineUpdateData = {
        symbol,
        close: parseFloat(klineData.c),
        ma10,
        timestamp: Date.now()
      };

      // Debug log for first few symbols
      if (['BTCUSDT', 'ETHUSDT', 'BNBUSDT'].includes(symbol)) {
        console.log(`📡 Emitting kline-update for ${symbol}: Price=${klineUpdateData.close}, MA10=${ma10.toFixed(4)}`);
      }

      this.emit('kline-update', klineUpdateData);
    }
  }

  /**
   * Main spike detection logic
   */
  checkForSpike(symbol, klineData) {
    const data = this.marketData.get(symbol);
    if (!data) return;

    // Debug log for first spike check
    if (data.closes.length === this.config.minDataPoints) {
      console.log(`🔍 ${symbol}: Starting spike detection with ${data.closes.length} klines`);
    }

    // Debug every spike check for TESTCOIN
    if (symbol === 'TESTCOIN') {
      console.log(`🔬 ${symbol}: Checking spike - closes: ${data.closes.length}, price: ${klineData.c}`);
    }

    // Cooldown check
    const lastSpike = this.lastSpikeTime.get(symbol) || 0;
    if (Date.now() - lastSpike < this.config.cooldownPeriod) {
      return;
    }

    const currentPrice = parseFloat(klineData.c);
    const currentVolume = parseFloat(klineData.v);
    const { closes, volumes } = data;

    // 1. Calculate MA10
    const ma10 = calculateMA(closes, this.config.ma10Period);
    if (!ma10) return;

    // 2. Check Price > MA10 by more than 0.4%
    const priceAboveMA = ((currentPrice - ma10) / ma10) * 100;
    const isPriceConditionMet = priceAboveMA > this.config.priceThreshold;

    // Debug for TESTCOIN
    if (symbol === 'TESTCOIN') {
      console.log(`🔬 ${symbol} Price Check: ${priceAboveMA.toFixed(4)}% > ${this.config.priceThreshold}% = ${isPriceConditionMet}`);
      if (!isPriceConditionMet) {
        console.log(`❌ ${symbol} FAILED Price Check - RETURNING`);
      } else {
        console.log(`✅ ${symbol} PASSED Price Check - CONTINUING`);
      }
    }

    if (!isPriceConditionMet) return;

    // 3. Calculate MA10 slope
    const slope = calculateSlope(closes, this.config.ma10Period);
    const isSlopeConditionMet = slope && slope > this.config.slopeThreshold;

    // Debug for TESTCOIN
    if (symbol === 'TESTCOIN') {
      console.log(`🔬 ${symbol} Slope Check: ${slope ? slope.toFixed(6) : 'null'} > ${this.config.slopeThreshold} = ${isSlopeConditionMet}`);
    }

    if (!isSlopeConditionMet) return;

    // 4. Check Volume > 1.5x average
    const avgVolume = calculateMA(volumes, this.config.volumePeriod);
    const volumeRatio = avgVolume > 0 ? currentVolume / avgVolume : 0;
    const isVolumeConditionMet = volumeRatio > this.config.volumeMultiplier;

    // Debug for TESTCOIN
    if (symbol === 'TESTCOIN') {
      console.log(`🔬 ${symbol} Volume Check: ${volumeRatio.toFixed(2)}x > ${this.config.volumeMultiplier}x = ${isVolumeConditionMet}`);
      console.log(`🔬 ${symbol} Volume Details: current=${currentVolume}, avg=${avgVolume.toFixed(2)}, volumes=[${volumes.slice(-5).join(', ')}]`);
    }

    if (!isVolumeConditionMet) return;

    // 5. Check Trend Direction
    const trend = getTrendDirection(closes);
    const consolidation = detectConsolidation(closes);

    const isTrendConditionMet =
      trend === 'UPTREND' ||
      trend === 'SIDEWAYS' ||  // Allow all SIDEWAYS trends for testing
      (trend === 'SIDEWAYS' && consolidation && consolidation.position === 'UPPER_HALF');

    // Debug for TESTCOIN
    if (symbol === 'TESTCOIN') {
      console.log(`🔬 ${symbol} Trend Check: ${trend} = ${isTrendConditionMet}`);
      if (trend === 'SIDEWAYS') {
        console.log(`🔬 ${symbol} Consolidation: ${consolidation ? consolidation.position : 'none'}`);
      }
      if (isTrendConditionMet) {
        console.log(`✅ ${symbol} PASSED ALL CHECKS - SPIKE DETECTED!`);
      } else {
        console.log(`❌ ${symbol} FAILED Trend Check - RETURNING`);
      }
    }

    if (!isTrendConditionMet) return;

    // All conditions met - SPIKE DETECTED!
    const spikeData = {
      symbol,
      price: currentPrice,
      spikePercent: parseFloat(priceAboveMA.toFixed(2)),
      slope: parseFloat(slope.toFixed(4)),
      volumeRatio: parseFloat(volumeRatio.toFixed(2)),
      ma10: parseFloat(ma10.toFixed(8)),
      avgVolume: parseFloat(avgVolume.toFixed(2)),
      trend,
      consolidation: consolidation?.isConsolidating || false,
      timestamp: Date.now(),
      confidence: this.calculateConfidence(priceAboveMA, slope, volumeRatio, trend)
    };

    // Set cooldown
    this.lastSpikeTime.set(symbol, Date.now());

    // Log detection
    console.log(`🚨 FINANCIAL SPIKE DETECTED: ${symbol}`);
    console.log(`   Price: $${currentPrice} (${priceAboveMA.toFixed(2)}% above MA10)`);
    console.log(`   MA10: $${ma10.toFixed(4)} | Slope: ${slope.toFixed(4)}`);
    console.log(`   Volume: ${volumeRatio.toFixed(1)}x average`);
    console.log(`   Trend: ${trend} | Confidence: ${spikeData.confidence}%`);

    // Emit spike event
    this.emit('spike', spikeData);
  }

  /**
   * Calculate spike confidence score
   */
  calculateConfidence(priceAboveMA, slope, volumeRatio, trend) {
    let confidence = 0;

    // Price component (0-40 points)
    if (priceAboveMA > 2.0) confidence += 40;
    else if (priceAboveMA > 1.0) confidence += 30;
    else if (priceAboveMA > 0.6) confidence += 20;
    else confidence += 10;

    // Slope component (0-25 points)
    if (slope > 0.5) confidence += 25;
    else if (slope > 0.3) confidence += 20;
    else if (slope > 0.2) confidence += 15;
    else confidence += 10;

    // Volume component (0-25 points)
    if (volumeRatio > 3.0) confidence += 25;
    else if (volumeRatio > 2.5) confidence += 20;
    else if (volumeRatio > 2.0) confidence += 15;
    else confidence += 10;

    // Trend component (0-10 points)
    if (trend === 'UPTREND') confidence += 10;
    else confidence += 5;

    return Math.min(100, confidence);
  }

  /**
   * Calculate Moving Average
   */
  calculateMA(prices, period) {
    if (prices.length < period) return 0;
    const slice = prices.slice(-period);
    const sum = slice.reduce((acc, price) => acc + price, 0);
    return sum / period;
  }

  /**
   * Start monitoring
   */
  start() {
    this.isActive = true;
    console.log('🟢 Financial Spike Detector started');
    this.emit('started');
  }

  /**
   * Stop monitoring
   */
  stop() {
    this.isActive = false;
    console.log('🔴 Financial Spike Detector stopped');
    this.emit('stopped');
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      symbolsTracked: this.marketData.size,
      isActive: this.isActive,
      config: this.config,
      dataPoints: Array.from(this.marketData.values())
        .map(data => data.closes.length)
        .reduce((sum, len) => sum + len, 0)
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Financial Spike Detector config updated:', newConfig);
    this.emit('configUpdated', this.config);
  }

  /**
   * Update config from dashboard settings - 🚀 TAM SENKRONİZASYON
   */
  updateFromDashboard(dashboardConfig) {
    const detectorConfig = {
      volumeMultiplier: dashboardConfig.hacimCarpani || this.config.volumeMultiplier,
      priceThreshold: dashboardConfig.spikeEsigi || this.config.priceThreshold,
      slopeThreshold: 0.01, // Çok düşük slope (her zaman geçsin)
      cooldownPeriod: 15000, // 15 saniye (çok hızlı!)
      minDataPoints: 3 // Çok az data (hızlı tespit!)
    };

    this.updateConfig(detectorConfig);
    console.log('� Financial Detector DASHBOARD ile senkronize edildi:', detectorConfig);
    console.log(`📊 Yeni kriterler: Spike=${detectorConfig.priceThreshold}%, Hacim=${detectorConfig.volumeMultiplier}x`);
  }

  /**
   * Clear data for a symbol
   */
  clearSymbolData(symbol) {
    this.marketData.delete(symbol);
    this.lastSpikeTime.delete(symbol);
    console.log(`🗑️ Cleared data for ${symbol}`);
  }

  /**
   * Clear all data
   */
  clearAllData() {
    this.marketData.clear();
    this.lastSpikeTime.clear();
    console.log('🗑️ Cleared all market data');
  }
}

/**
 * Factory function for easy instantiation
 */
export const createFinancialSpikeDetector = (options = {}) => {
  return new FinancialSpikeDetector(options);
};

/**
 * Preset configurations for different trading styles
 */
export const SPIKE_PRESETS = {
  // Conservative: Higher thresholds, less false positives
  CONSERVATIVE: {
    priceThreshold: 0.6,
    slopeThreshold: 0.3,
    volumeMultiplier: 1.0,
    cooldownPeriod: 120000 // 2 minutes
  },

  // Standard: Balanced approach (default)
  STANDARD: {
    priceThreshold: 0.4,
    slopeThreshold: 0.1,
    volumeMultiplier: 1.5,
    cooldownPeriod: 300000 // 5 minutes
  },

  // Aggressive: Lower thresholds, more signals
  AGGRESSIVE: {
    priceThreshold: 0.3,
    slopeThreshold: 0.08,
    volumeMultiplier: 1.3,
    cooldownPeriod: 180000 // 3 minutes
  },

  // Scalping: Very sensitive for quick trades
  SCALPING: {
    priceThreshold: 0.2,
    slopeThreshold: 0.05,
    volumeMultiplier: 1.2,
    cooldownPeriod: 60000 // 1 minute
  }
};

export default FinancialSpikeDetector;
