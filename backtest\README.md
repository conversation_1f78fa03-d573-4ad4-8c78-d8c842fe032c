# Crypto Spike Backtest

Bu proje, kripto para birimlerinde fiyat spike'larını tespit eden ve bunların karlılığını test eden bir backtest aracıdır.

## Özellikler

- Binance API'sinden gerçek zamanlı veri çekme
- Teknik analiz göstergeleri (Moving Average, Slope)
- Configurable spike tespit kriterleri
- Take Profit / Stop Loss analizi
- Çoklu sembol desteği
- Cooldown mekanizması

## Kurulum

```bash
npm install
```

## Kullanım

```bash
npm start
```

veya

```bash
node main.js
```

## Konfigürasyon

`main.js` dosyasında aşağıdaki parametreleri ayarlayabilirsiniz:

- `symbols`: Test edilecek kripto çiftleri
- `interval`: Zaman dilimi (1m, 5m, 15m, 1h, vb.)
- `threshold.priceGap`: Fiyat fark<PERSON> (%)
- `threshold.slope`: <PERSON><PERSON><PERSON>
- `cooldown`: <PERSON><PERSON>ı sembo<PERSON> i<PERSON><PERSON> be<PERSON> (ms)
- `tpPercent`: Take Profit yüzdesi
- `slPercent`: Stop Loss yüzdesi
- `maxLookahead`: Kaç dakika sonrasına kadar bakılacağı

## Dosya Yapısı

- `main.js`: Ana çalıştırma dosyası
- `spikeBacktester.js`: Backtest mantığı
- `technical-indicators.js`: Teknik analiz fonksiyonları

## Örnek Çıktı

```
🚀 Backtest başlatılıyor...

🔍 Testing BTCUSDT...
  Spike Sayısı: 2
  ✅ TP: 0 (%0.0)
  ❌ SL: 1 (%50.0)
  ⚠️ Neutral: 1 (%50.0)

📊 Backtest Sonuçları
----------------------
🧪 Test edilen spike sayısı: 18
✅ Başarılı işlemler: 2
❌ Zararlı işlemler: 5
🎯 Başarı oranı: %11.11
```
