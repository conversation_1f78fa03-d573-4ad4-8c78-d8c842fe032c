import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { TrendingUp, TrendingDown, Zap, Eye, RefreshCw, BarChart3 } from 'lucide-react';
import { MarketData } from '@/types/trading';
import { formatPrice, formatVolume, formatPercentage } from '@/utils/priceFormatter';

interface MarketPanelProps {
  isConnected: boolean;
}

export const MarketPanel: React.FC<MarketPanelProps> = ({ isConnected }) => {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [selectedSymbol, setSelectedSymbol] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // REAL market data from Binance API
  useEffect(() => {
    const fetchRealMarketData = async () => {
      try {
        console.log('📊 Fetching REAL market data from Binance...');

        // Get real market data from backend
        const response = await fetch('/api/market/ticker-24hr');
        if (!response.ok) {
          throw new Error(`Market API error: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.tickers) {
          // Filter for major USDT pairs
          const majorSymbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'ADAUSDT', 'XRPUSDT'];
          const realMarketData: MarketData[] = data.tickers
            .filter((ticker: any) => majorSymbols.includes(ticker.symbol))
            .map((ticker: any) => ({
              symbol: ticker.symbol,
              price: parseFloat(ticker.lastPrice),
              change24h: parseFloat(ticker.priceChangePercent),
              volume24h: parseFloat(ticker.volume),
              high24h: parseFloat(ticker.highPrice),
              low24h: parseFloat(ticker.lowPrice),
              markPrice: parseFloat(ticker.lastPrice), // Use last price as mark price
              timestamp: Date.now()
            }));

          setMarketData(realMarketData);
          console.log(`✅ Loaded ${realMarketData.length} real market data points`);
        }
      } catch (error) {
        console.error('❌ Failed to fetch real market data:', error);
        // Don't fallback to mock data - show error instead
        setMarketData([]);
      }
    };

    // Initial fetch
    fetchRealMarketData();

    // Real-time updates every 5 seconds
    const interval = setInterval(() => {
      fetchRealMarketData();
      setLastUpdate(new Date());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Removed old formatPrice and formatVolume - using utility functions now

  const getChangeColor = (change: number): string => {
    return change >= 0 ? 'text-profit' : 'text-loss';
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? (
      <TrendingUp className="h-3 w-3" />
    ) : (
      <TrendingDown className="h-3 w-3" />
    );
  };

  const topGainers = marketData
    .filter(coin => coin.change24h > 0)
    .sort((a, b) => b.change24h - a.change24h)
    .slice(0, 3);

  const topLosers = marketData
    .filter(coin => coin.change24h < 0)
    .sort((a, b) => a.change24h - b.change24h)
    .slice(0, 3);

  return (
    <Card className="w-full bg-gradient-to-br from-primary/5 to-position-bg border-primary/30 shadow-2xl hover:shadow-primary/30 transition-shadow duration-300">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <CardTitle className="text-primary drop-shadow flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Piyasa Paneli
          </CardTitle>
          <Badge className="ml-2 animate-pulse bg-primary/10 text-primary border-primary/30">Canlı</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Top Gainers */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-profit" />
              Top Gainers (24h)
            </h3>
          </div>
          <div className="space-y-2">
            {topGainers.map(coin => (
              <div
                key={coin.symbol}
                className="flex items-center justify-between p-3 bg-muted/20 rounded-lg border border-border/20 hover:bg-muted/30 transition-colors cursor-pointer"
                onClick={() => setSelectedSymbol(selectedSymbol === coin.symbol ? null : coin.symbol)}
              >
                <div className="flex items-center gap-3">
                  <div>
                    <div className="font-medium text-sm">{coin.symbol}</div>
                    <div className="text-xs text-muted-foreground">
                      Vol: {formatVolume(coin.volume24h)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-mono text-sm">
                    ${formatPrice(coin.price, coin.symbol)}
                  </div>
                  <div className={`flex items-center gap-1 text-xs ${getChangeColor(coin.change24h)}`}>
                    {getChangeIcon(coin.change24h)}
                    {formatPercentage(coin.change24h)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator className="bg-border/30" />

        {/* Top Losers */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm flex items-center gap-2">
              <TrendingDown className="h-4 w-4 text-loss" />
              Top Losers (24h)
            </h3>
          </div>
          <div className="space-y-2">
            {topLosers.map(coin => (
              <div
                key={coin.symbol}
                className="flex items-center justify-between p-3 bg-muted/20 rounded-lg border border-border/20 hover:bg-muted/30 transition-colors cursor-pointer"
                onClick={() => setSelectedSymbol(selectedSymbol === coin.symbol ? null : coin.symbol)}
              >
                <div className="flex items-center gap-3">
                  <div>
                    <div className="font-medium text-sm">{coin.symbol}</div>
                    <div className="text-xs text-muted-foreground">
                      Vol: {formatVolume(coin.volume24h)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-mono text-sm">
                    ${formatPrice(coin.price, coin.symbol)}
                  </div>
                  <div className={`flex items-center gap-1 text-xs ${getChangeColor(coin.change24h)}`}>
                    {getChangeIcon(coin.change24h)}
                    {coin.change24h.toFixed(2)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator className="bg-border/30" />

        {/* Spike Alerts */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm flex items-center gap-2">
              <Zap className="h-4 w-4 text-spike-alert" />
              Spike Alerts
            </h3>
            <Badge variant="outline" className="border-spike-alert/30 text-spike-alert text-xs">
              Live
            </Badge>
          </div>
          <div className="p-4 bg-gradient-to-r from-spike-alert/10 to-spike-alert/5 rounded-lg border border-spike-alert/20">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-sm">SOLUSDT</div>
                <div className="text-xs text-muted-foreground">High confidence spike detected</div>
              </div>
              <div className="text-right">
                <div className="text-spike-alert font-medium text-sm">$68.95</div>
                <Badge
                  variant="outline"
                  className="border-spike-alert text-spike-alert animate-spike-alert text-xs"
                >
                  CONFIRMED
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Selected Symbol Details */}
        {selectedSymbol && (
          <div className="space-y-3">
            <Separator className="bg-border/30" />
            <div className="p-4 bg-muted/10 rounded-lg border border-border/20">
              <h3 className="font-medium text-sm mb-3">{selectedSymbol} Details</h3>
              {marketData
                .filter(coin => coin.symbol === selectedSymbol)
                .map(coin => (
                  <div key={coin.symbol} className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Mark Price:</span>
                      <span className="font-mono">${formatPrice(coin.markPrice, coin.symbol)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">24h High:</span>
                      <span className="font-mono">${formatPrice(coin.high24h, coin.symbol)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">24h Low:</span>
                      <span className="font-mono">${formatPrice(coin.low24h, coin.symbol)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">24h Volume:</span>
                      <span className="font-mono">{formatVolume(coin.volume24h)}</span>
                    </div>
                  </div>
                ))
              }
            </div>
          </div>
        )}

        {/* Connection Status */}
        {!isConnected && (
          <div className="p-3 bg-loss/10 rounded-lg border border-loss/20">
            <div className="flex items-center gap-2 text-loss text-sm">
              <div className="h-2 w-2 bg-loss rounded-full animate-pulse" />
              Waiting for market data connection...
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};