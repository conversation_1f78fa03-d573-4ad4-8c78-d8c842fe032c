import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Zap, Activity, Wifi, WifiOff, LogOut, User } from 'lucide-react';
import { TicareKonfigurasyonu, Pozisyon, SpikeSignali, HesapBakiyesi as HesapBakiyesiType, AcikEmir } from '@/types/turkish-trading';
import { SpikeAnalysisSettings } from '@/components/SpikeAnalysisSettings';
import { TradingPanel } from '@/components/TradingPanel';
import { PositionMonitor } from '@/components/PositionMonitor';
import { SpikeSignalList } from '@/components/SpikeSignalList';
import { AcikEmirlerKarti } from '@/components/AcikEmirlerKarti';
import { LogoVeHesapBakiyesi } from '@/components/LogoVeHesapBakiyesi';
import { io, Socket } from 'socket.io-client';
import { fetchAndUpdateSymbolPrecision } from '@/utils/priceFormatter';

// Ana Panel Konfigürasyonu - Kullanıcı Spesifikasyonlarına Göre Güncellendi
const defaultConfig: TicareKonfigurasyonu = {
  kaldirac: 20, // 20x sabit
  tpYuzdesi: 0.5, // Default 0.5%
  slYuzdesi: 0.5, // Default 0.5%
  maksPozisyon: 3, // Aynı anda maksimum 3 açık işlem
  maksGunlukIslem: 20, // Default 20, max 300'e kadar ayarlanabilir
  islemMiktari: 0.3, // 20x kaldıraçla 6 USD = 0.3 USD margin (güvenli başlangıç)
  spikeEsigi: 1.1, // Default 1.1% spike yüzdesi
  hacimCarpani: 1.5, // Hacim çarpanı
  maPeriyodu: 10, // MA ayarı default 10
  islemBaslatildi: false,
};

export const DashboardMain = () => {
  const { authState, cikisYap } = useAuth();
  const [config, setConfig] = useState<TicareKonfigurasyonu>(defaultConfig);
  const [positions, setPositions] = useState<Pozisyon[]>([]);
  const [openOrders, setOpenOrders] = useState<AcikEmir[]>([]);
  const [spikeSignals, setSpikeSignals] = useState<SpikeSignali[]>([]);
  const [accountBalance, setAccountBalance] = useState<HesapBakiyesiType>({
    kullanilabilirBakiye: 0,
    toplamBakiye: 0,
    gerceklesmemisPnl: 0,
    pozisyonMarjini: 0,
    emirMarjini: 0
  });
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');
  const [activeTab, setActiveTab] = useState('ana-panel');
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [spikeAlert, setSpikeAlert] = useState<{ symbol: string, price: number, show: boolean } | null>(null);
  const [spikeSettings, setSpikeSettings] = useState<SpikeAnalysisSettings>({
    priceThreshold: 0.004, // 0.4%
    slopeThreshold: 0.1,
    volumeMultiplier: 1.5,
    minDataPoints: 30,
    cooldownPeriod: 300000, // 5 minutes
    maxPositions: 3,
    autoTrade: false,
    takeProfitPercent: 0.8,
    stopLossPercent: 0.8,
    tradeAmount: 100,
    leverage: 20
  });

  // Initialize socket connection and fetch initial data
  useEffect(() => {
    if (!authState.isAuthenticated) return;

    // Initialize socket connection
    const newSocket = io(window.location.origin, {
      withCredentials: true,
      transports: ['websocket']
    });

    setSocket(newSocket);

    // Socket event listeners
    newSocket.on('connect', () => {
      console.log('Socket.IO bağlantısı kuruldu');
      setIsConnected(true);
      setConnectionStatus('Binance\'a Bağlandı');

      // Join user's room
      newSocket.emit('join', authState.user?.username);

      // Fetch initial balance
      fetchAccountBalance();

      // Start user data stream
      startUserDataStream();

      // Fetch and update symbol precision for accurate price formatting
      fetchAndUpdateSymbolPrecision();
    });

    newSocket.on('disconnect', () => {
      console.log('Socket.IO bağlantısı kesildi');
      setIsConnected(false);
      setConnectionStatus('Bağlantı Kesildi');
    });

    // Balance update listener
    newSocket.on('balance-update', (data) => {
      console.log('Bakiye güncellendi:', data);
      setAccountBalance(prev => ({
        ...prev,
        kullanilabilirBakiye: data.availableBalance,
        toplamBakiye: data.balance,
        gerceklesmemisPnl: data.crossUnPnl || prev.gerceklesmemisPnl
      }));
    });

    // Account update listener
    newSocket.on('account-update', (data) => {
      console.log('Hesap güncellendi:', data);
      if (data.balances) {
        const usdtBalance = data.balances.find((b: any) => b.asset === 'USDT');
        if (usdtBalance) {
          setAccountBalance(prev => ({
            ...prev,
            kullanilabilirBakiye: usdtBalance.availableBalance,
            toplamBakiye: usdtBalance.walletBalance,
            gerceklesmemisPnl: usdtBalance.unrealizedPnl || prev.gerceklesmemisPnl
          }));
        }
      }
    });

    // Position update listener - Binance'den gelen gerçek pozisyon verileri
    newSocket.on('positions-update', (data) => {
      console.log('📊 Pozisyonlar güncellendi (Binance):', data);
      if (data.positions) {
        const formattedPositions = data.positions.map((pos: any) => {
          const positionAmt = parseFloat(pos.pa);
          const entryPrice = parseFloat(pos.ep);
          const markPrice = parseFloat(pos.mp || pos.ep);
          const unrealizedPnl = parseFloat(pos.up);
          const leverage = parseInt(pos.l) || 20;

          // Binance'den gelen P&L zaten doğru hesaplanmış - kaldıraç etkisi dahil değil
          const pnlPercent = entryPrice > 0 ? (unrealizedPnl / (entryPrice * Math.abs(positionAmt))) * 100 : 0;

          return {
            id: `${pos.s}_${Date.now()}`,
            sembol: pos.s,
            yon: positionAmt > 0 ? 'LONG' : 'SHORT',
            boyut: Math.abs(positionAmt),
            miktar: Math.abs(positionAmt),
            girisFiyati: entryPrice,
            guncelFiyat: markPrice,
            markFiyati: markPrice,
            pnl: unrealizedPnl,
            pnlYuzdesi: parseFloat(pnlPercent.toFixed(2)),
            kaldirac: leverage,
            durum: positionAmt === 0 ? 'KAPALI' : 'ACIK',
            acilisZamani: Date.now(),
            stopLoss: null,
            takeProfit: null
          };
        });

        console.log('📊 Formatted positions:', formattedPositions.map((p: any) => ({
          symbol: p.sembol,
          side: p.yon,
          amount: p.miktar,
          entryPrice: p.girisFiyati,
          markPrice: p.markFiyati,
          pnl: p.pnl,
          pnlPercent: p.pnlYuzdesi
        })));
        setPositions(formattedPositions);
      }
    });

    // Order update listener
    newSocket.on('orders-update', (data) => {
      console.log('Emirler güncellendi:', data);
      if (data.orders) {
        const formattedOrders = data.orders.map((order: any) => ({
          id: order.i.toString(),
          sembol: order.s,
          tip: order.o === 'LIMIT' ? 'LIMIT' : order.o === 'STOP_MARKET' ? 'STOP_LOSS' : 'MARKET',
          yon: order.S,
          miktar: parseFloat(order.q),
          fiyat: parseFloat(order.p),
          durum: order.X,
          olusturulmaZamani: order.T,
          guncellemeZamani: Date.now()
        }));
        setOpenOrders(formattedOrders.filter((order: any) =>
          order.durum === 'NEW' || order.durum === 'PARTIALLY_FILLED'
        ));
      }
    });

    // Individual order update listener
    newSocket.on('order-update', (data) => {
      console.log('Emir güncellendi:', data);
      const formattedOrder: AcikEmir = {
        id: data.orderId.toString(),
        sembol: data.symbol,
        tip: data.type === 'LIMIT' ? 'LIMIT' : data.type === 'STOP_MARKET' ? 'STOP_LOSS' : 'MARKET',
        yon: data.side,
        miktar: parseFloat(data.executedQty || data.quantity || '0'),
        fiyat: parseFloat(data.price || '0'),
        durum: data.status,
        olusturmZamani: data.timestamp,
        olusturulmaZamani: data.timestamp,
        guncellemeZamani: Date.now()
      };

      if (data.status === 'FILLED' || data.status === 'CANCELED' || data.status === 'EXPIRED') {
        // Remove completed/cancelled orders
        setOpenOrders(prev => prev.filter(order => order.id !== formattedOrder.id));
      } else if (data.status === 'NEW' || data.status === 'PARTIALLY_FILLED') {
        // Add or update active orders
        setOpenOrders(prev => {
          const existingIndex = prev.findIndex(order => order.id === formattedOrder.id);
          if (existingIndex >= 0) {
            const updated = [...prev];
            updated[existingIndex] = formattedOrder;
            return updated;
          } else {
            return [formattedOrder, ...prev];
          }
        });
      }
    });

    // Individual position update listener - Tekil pozisyon güncellemeleri
    newSocket.on('position-update', (data) => {
      console.log('📈 Tekil pozisyon güncellendi (Binance):', data);
      setPositions(prev => {
        const existingIndex = prev.findIndex(pos => pos.sembol === data.symbol);

        // Binance'den gelen gerçek P&L verilerini kullan
        const positionAmt = data.positionAmt;
        const entryPrice = data.entryPrice;
        const markPrice = data.markPrice;
        const unrealizedPnl = data.unRealizedProfit;
        const leverage = data.leverage || 20;

        // P&L yüzdesi hesaplama - Binance'den gelen değer zaten doğru - kaldıraç etkisi dahil değil
        const pnlPercent = entryPrice > 0 ? (unrealizedPnl / (entryPrice * Math.abs(positionAmt))) * 100 : 0;

        const updatedPosition: Pozisyon = {
          id: existingIndex >= 0 ? prev[existingIndex].id : `${data.symbol}_${Date.now()}`,
          sembol: data.symbol,
          yon: positionAmt > 0 ? 'LONG' : 'SHORT',
          boyut: Math.abs(positionAmt),
          miktar: Math.abs(positionAmt),
          girisFiyati: entryPrice,
          guncelFiyat: markPrice,
          markFiyati: markPrice,
          pnl: unrealizedPnl,
          pnlYuzdesi: parseFloat(pnlPercent.toFixed(2)),
          kaldirac: leverage,
          durum: positionAmt === 0 ? 'KAPALI' : 'ACIK',
          acilisZamani: existingIndex >= 0 ? prev[existingIndex].acilisZamani : Date.now(),
          stopLoss: existingIndex >= 0 ? prev[existingIndex].stopLoss : null,
          takeProfit: existingIndex >= 0 ? prev[existingIndex].takeProfit : null
        };

        console.log(`📈 Updated position ${data.symbol}: P&L=${unrealizedPnl.toFixed(4)}, %=${pnlPercent.toFixed(2)}`);

        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = updatedPosition;
          return updated;
        } else {
          return [updatedPosition, ...prev];
        }
      });
    });

    // Position closed listener - Pozisyon kapatıldığında
    newSocket.on('position-closed', (data) => {
      console.log('🔴 Pozisyon kapatıldı:', data);

      // Pozisyonu listeden kaldır
      setPositions(prev => prev.filter(pos => pos.sembol !== data.symbol));

      // Bildirim göster
      const pnlAmount = data.pnl?.netPnl || 0;
      const pnlText = pnlAmount >= 0 ? `+$${pnlAmount.toFixed(2)}` : `-$${Math.abs(pnlAmount).toFixed(2)}`;

      setSpikeAlert({
        symbol: `${data.symbol} CLOSED (${data.reason})`,
        price: parseFloat(pnlText.replace(/[^0-9.-]/g, '')),
        show: true
      });

      console.log(`✅ Pozisyon kapatıldı: ${data.symbol} - ${data.reason} - PnL: ${pnlText}`);
    });

    // Spike signal listener
    newSocket.on('spike-detected', (data) => {
      console.log('Spike sinyali:', data);
      setSpikeSignals(prev => [data, ...prev.slice(0, 19)]);

      // 🚀 YENİ: Spike Uyarı Sistemi
      const spikeType = data.spikeType || 'YUKSEK';
      const tradeDirection = data.tradeDirection || 'SHORT';
      showSpikeAlert(data.sembol || data.symbol, data.fiyat || data.price, spikeType, tradeDirection);

      // 🎯 Maksimum işlem sayısı kontrolü ve otomatik trading
      if (config.islemBaslatildi) {
        const openPositionsCount = positions.filter(pos => pos.durum === 'ACIK').length;

        if (openPositionsCount >= config.maksPozisyon) {
          console.log(`⚠️ Max position limit reached (${openPositionsCount}/${config.maksPozisyon}). Spike ignored: ${data.sembol || data.symbol}`);

          // UI'da bildirim göster
          setSpikeAlert({
            symbol: `${data.sembol || data.symbol} (IGNORED)`,
            price: data.fiyat || data.price,
            show: true
          });

          return; // Spike'ı görmezden gel
        }

        // Pozisyon açmaya devam et
        console.log(`✅ Opening position for ${data.sembol || data.symbol} (${openPositionsCount + 1}/${config.maksPozisyon})`);
        openPositionFromSpike(data);
      }
    });

    // Spike ignored listener
    newSocket.on('spike-ignored', (data) => {
      console.log('Spike ignored:', data);

      // Ignored spike için özel uyarı
      setSpikeAlert({
        symbol: `${data.symbol} (MAX LIMIT)`,
        price: 0,
        show: true
      });
    });

    // Position opened listener
    newSocket.on('position-opened', (data) => {
      console.log('Position opened:', data);

      // Position opened için bildirim
      setSpikeAlert({
        symbol: `${data.symbol} ${data.side} OPENED`,
        price: data.price,
        show: true
      });

      // Position listesini güncelle
      const newPosition: Pozisyon = {
        id: `${data.symbol}_${Date.now()}`,
        sembol: data.symbol,
        yon: data.side,
        boyut: data.quantity,
        miktar: data.quantity,
        girisFiyati: data.price,
        guncelFiyat: data.price,
        markFiyati: data.price,
        pnl: 0,
        pnlYuzdesi: 0,
        kaldirac: config.kaldirac,
        durum: 'ACIK',
        acilisZamani: Date.now(),
        stopLoss: data.stopLossPrice,
        takeProfit: data.takeProfitPrice
      };

      setPositions(prev => [newPosition, ...prev]);
    });

    // 📊 Price update listener - Real-time P&L güncellemeleri
    newSocket.on('price-update', (priceData) => {
      // Sadece major coin'ler için log (spam önlemek için)
      if (['BTCUSDT', 'ETHUSDT'].includes(priceData.symbol) && Math.random() < 0.01) {
        console.log(`⚡ Real-time price: ${priceData.symbol} @ ${priceData.price}`);
      }

      // Real-time P&L güncelle (throttling yok)
      updatePositionPnL(priceData);
    });

    // 🚀 YENİ: Otomatik Trading Event Listeners
    newSocket.on('auto-trade-executed', (data) => {
      console.log('✅ Otomatik işlem gerçekleşti:', data);

      // Toast notification
      if (typeof window !== 'undefined') {
        alert(`✅ Otomatik İşlem: ${data.spike.sembol} @ $${data.spike.fiyat}`);
      }

      // Log successful trade
      console.log(`✅ Otomatik işlem başarılı: ${data.spike.sembol} @ $${data.spike.fiyat}`);
    });

    newSocket.on('auto-trade-error', (data) => {
      console.error('❌ Otomatik işlem hatası:', data);

      // Toast notification
      if (typeof window !== 'undefined') {
        alert(`❌ Otomatik İşlem Hatası: ${data.spike.sembol} - ${data.error}`);
      }

      // Log error
      console.error(`❌ Otomatik işlem hatası: ${data.spike.sembol} - ${data.error}`);
    });

    // API Error handling - NO DEMO FALLBACK
    newSocket.on('api-error', (data) => {
      console.error('❌ API Hatası:', data);

      if (data.needsApiKey) {
        // Show API key setup modal/alert
        if (typeof window !== 'undefined') {
          alert('❌ Binance API Key geçersiz! Lütfen API ayarlarınızı kontrol ediniz.');
        }

        // Redirect to settings or show API setup
        console.error('🔑 API Key setup required');
      }
    });

    return () => {
      console.log('🔌 Disconnecting socket and cleaning up...');
      newSocket.disconnect();
    };
  }, [authState.isAuthenticated, authState.user?.username]);

  const fetchAccountBalance = async () => {
    try {
      console.log('💰 Fetching account balance...');
      const response = await fetch('/api/account/balance', {
        credentials: 'include'
      });

      console.log('💰 Balance response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('💰 Balance response data:', data);

        if (data.success) {
          const newBalance = {
            kullanilabilirBakiye: data.balance.availableBalance,
            toplamBakiye: data.balance.walletBalance,
            gerceklesmemisPnl: data.balance.crossUnPnl,
            pozisyonMarjini: 0,
            emirMarjini: 0
          };

          console.log('💰 Setting new balance:', newBalance);
          setAccountBalance(newBalance);
        } else {
          console.error('💰 Balance fetch failed:', data.error);
        }
      } else {
        const errorData = await response.json();
        console.error('💰 Balance fetch HTTP error:', response.status, errorData);
      }
    } catch (error) {
      console.error('💰 Bakiye getirme hatası:', error);
    }
  };

  const startUserDataStream = async () => {
    try {
      const response = await fetch('/api/stream/start', {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        console.log('User data stream başlatıldı:', data);
      }
    } catch (error) {
      console.error('User data stream başlatma hatası:', error);
    }
  };

  const handleConfigChange = (newConfig: Partial<TicareKonfigurasyonu>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  };

  const handleTradingStart = async () => {
    try {
      const response = await fetch('/api/dashboard/trading/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        setConfig(prev => ({ ...prev, islemBaslatildi: true }));
        console.log('✅ İşlem tarama başlatıldı!');
      }
    } catch (error) {
      console.error('❌ Trading start error:', error);
    }
  };

  const handleTradingStop = async () => {
    try {
      const response = await fetch('/api/dashboard/trading/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        setConfig(prev => ({ ...prev, islemBaslatildi: false }));
        console.log('⏸️ İşlem tarama durduruldu!');
      }
    } catch (error) {
      console.error('❌ Trading stop error:', error);
    }
  };

  const handleStartAnalysis = () => {
    setIsAnalyzing(true);
    console.log('Spike analizi başlatıldı');
    // Reset signals for new analysis
    setSpikeSignals([]);

    // Start real-time spike monitoring
    if (socket) {
      socket.emit('start-spike-analysis', { username: authState.user?.username });
    }
  };

  const handleStopAnalysis = () => {
    setIsAnalyzing(false);
    console.log('Spike analizi durduruldu');

    // Stop real-time spike monitoring
    if (socket) {
      socket.emit('stop-spike-analysis', { username: authState.user?.username });
    }
  };

  const handleCloseAllPositions = () => {
    if (positions.length === 0) return;

    const confirmClose = window.confirm(`${positions.length} adet pozisyonu kapatmak istediğinizden emin misiniz?`);
    if (confirmClose) {
      positions.forEach(position => {
        handleClosePosition(position.id);
      });
      console.log('Tüm pozisyonlar kapatılıyor...');
    }
  };

  const handleRefreshPositions = async () => {
    try {
      // Refresh test endpoint'ini dene
      const refreshTestResponse = await fetch('/api/refresh/test', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const refreshTestResult = await refreshTestResponse.json();
      console.log('✅ Refresh test endpoint sonucu:', refreshTestResult);

      console.log('🔄 Pozisyonlar yenileniyor...');

      const response = await fetch('/api/refresh/positions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setPositions(result.positions);
        console.log(`✅ ${result.positions.length} pozisyon yenilendi`);
        // Bildirim mesajları kaldırıldı
      } else {
        console.error('❌ Pozisyon yenileme hatası:', result.error);
        // Bildirim mesajları kaldırıldı
      }
    } catch (error) {
      console.error('❌ Pozisyon yenileme API hatası:', error);
      // Bildirim mesajları kaldırıldı
    }
  };

  const handleRefreshOrders = async () => {
    try {
      console.log('🔄 Emirler yenileniyor...');

      const response = await fetch('/api/refresh/orders', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setOpenOrders(result.orders);
        console.log(`✅ ${result.orders.length} emir yenilendi`);
        // Bildirim mesajları kaldırıldı
      } else {
        console.error('❌ Emir yenileme hatası:', result.error);
        // Bildirim mesajları kaldırıldı
      }
    } catch (error) {
      console.error('❌ Emir yenileme API hatası:', error);
      // Bildirim mesajları kaldırıldı
    }
  };

  // Handle cancel single order - REAL BINANCE CANCELLATION
  const handleCancelOrder = async (emirId: string) => {
    try {
      // Find the order to get symbol and orderId
      const order = openOrders.find(emir => emir.id === emirId);
      if (!order) {
        console.error('❌ Order not found:', emirId);
        alert('Emir bulunamadı!');
        return;
      }

      console.log(`🗑️ Cancelling order ${order.id} for ${order.sembol}...`);

      const response = await fetch('/api/order/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: order.id,
          symbol: order.sembol
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Order cancelled:', result);

        // Remove from local state
        setOpenOrders(prev => prev.filter(emir => emir.id !== emirId));

        // Refresh orders to sync with Binance
        await handleRefreshOrders();

        // Show success notification
        alert(`${order.sembol} emri başarıyla iptal edildi!`);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Order cancellation failed');
      }
    } catch (error) {
      console.error('❌ Cancel order error:', error);
      alert(`Emir iptal edilirken hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`);
    }
  };

  // Handle cancel all orders - REAL BINANCE CANCELLATION
  const handleCancelAllOrders = async () => {
    if (openOrders.length === 0) return;

    const confirmCancel = window.confirm(`${openOrders.length} adet emri Binance'den iptal etmek istediğinizden emin misiniz?`);
    if (!confirmCancel) return;

    try {
      console.log('🗑️ Cancelling all orders...');

      const response = await fetch('/api/orders/cancel-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ All orders cancelled:', result);

        // Clear local orders and refresh
        setOpenOrders([]);
        await handleRefreshOrders();

        // Show success notification
        alert('Tüm emirler başarıyla iptal edildi!');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Cancel all orders failed');
      }
    } catch (error) {
      console.error('❌ Cancel all orders error:', error);
      alert(`Emirler iptal edilirken hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`);
    }
  };

  const handleSpikeSettingsChange = (newSettings: SpikeAnalysisSettings) => {
    setSpikeSettings(newSettings);
    console.log('Spike ayarları güncellendi:', newSettings);
  };

  const handleSpikeSettingsSave = () => {
    console.log('Spike ayarları kaydedildi:', spikeSettings);
    // Backend'e ayarları gönder
    if (socket) {
      socket.emit('update-spike-settings', {
        username: authState.user?.username,
        settings: spikeSettings
      });
    }
  };

  const handleSpikeSettingsReset = () => {
    const defaultSettings: SpikeAnalysisSettings = {
      priceThreshold: 0.004, // 0.4%
      slopeThreshold: 0.1,
      volumeMultiplier: 1.5,
      minDataPoints: 30,
      cooldownPeriod: 300000, // 5 minutes
      maxPositions: 3,
      autoTrade: false,
      takeProfitPercent: 0.8,
      stopLossPercent: 0.8,
      tradeAmount: 100,
      leverage: 20
    };
    setSpikeSettings(defaultSettings);
    console.log('Spike ayarları varsayılana döndürüldü');
  };

  // 🚀 YENİ: Spike Uyarı Fonksiyonu
  const showSpikeAlert = (symbol: string, price: number, spikeType: string = 'YUKSEK', tradeDirection: string = 'SHORT') => {
    setSpikeAlert({
      symbol: `${symbol} (${spikeType} - ${tradeDirection})`,
      price,
      show: true
    });

    // 20 saniye sonra uyarıyı kaldır
    setTimeout(() => {
      setSpikeAlert(prev => prev ? { ...prev, show: false } : null);

      // Animasyon bitince tamamen kaldır
      setTimeout(() => {
        setSpikeAlert(null);
      }, 500);
    }, 20000);
  };

  const handleClosePosition = async (positionId: string) => {
    try {
      const position = positions.find(pos => pos.id === positionId);
      if (!position) {
        console.error('Pozisyon bulunamadı:', positionId);
        return;
      }

      console.log('Pozisyon kapatılıyor:', position.sembol);

      const response = await fetch('/api/position/close', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: position.sembol,
          positionId: positionId
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Remove position from UI
        setPositions(prev => prev.filter(pos => pos.id !== positionId));
        console.log('✅ Pozisyon başarıyla kapatıldı:', position.sembol);
        // Bildirim mesajları kaldırıldı
      } else {
        console.error('❌ Pozisyon kapatma hatası:', result.error);
        // Bildirim mesajları kaldırıldı
      }
    } catch (error) {
      console.error('❌ Pozisyon kapatma API hatası:', error);
      // Bildirim mesajları kaldırıldı
    }
  };

  const handleRefreshBalance = () => {
    if (socket && authState.user?.username) {
      socket.emit('refresh-balance', authState.user.username);
    }
  };

  // 🎯 Spike'dan pozisyon açma fonksiyonu
  const openPositionFromSpike = async (spikeData: any) => {
    try {
      if (!socket || !authState.user?.username) {
        console.error('❌ Socket veya kullanıcı bilgisi eksik');
        return;
      }

      // Bakiye kontrolü - Kaldıraçlı işlemler için gerçek hesaplama
      const leverage = config.kaldirac || 20;
      const tradeAmount = config.islemMiktari || 0.3;
      const requiredMargin = tradeAmount / leverage; // Gerçek margin ihtiyacı
      const minRequiredBalance = Math.max(requiredMargin * 1.5, 0.3); // %50 güvenlik marjı + minimum 0.3 USDT

      if (accountBalance.kullanilabilirBakiye < minRequiredBalance) {
        console.warn(`⚠️ Yetersiz bakiye: ${accountBalance.kullanilabilirBakiye.toFixed(2)} USDT (Min: ${minRequiredBalance.toFixed(2)} USDT)`);

        // Kullanıcıya bildirim göster
        setSpikeAlert({
          symbol: 'YETERSIZ BAKİYE',
          price: accountBalance.kullanilabilirBakiye,
          show: true
        });

        setTimeout(() => setSpikeAlert(null), 5000);
        return;
      }

      // 🎯 Spike tipine göre trade direction belirleme
      const tradeDirection = spikeData.tradeDirection || spikeData.side || 'SHORT'; // Yüksek spike = SHORT
      const spikeType = spikeData.spikeType || 'YUKSEK';

      const tradeParams = {
        symbol: spikeData.sembol || spikeData.symbol,
        side: tradeDirection, // 🎯 SHORT işlem
        quantity: config.islemMiktari, // Ana Panel'den
        leverage: config.kaldirac, // Ana Panel'den
        stopLossPercent: config.slYuzdesi, // Ana Panel'den
        takeProfitPercent: config.tpYuzdesi, // Ana Panel'den
        confidence: spikeData.confidence,
        spikePrice: spikeData.fiyat || spikeData.price,
        spikeType: spikeType,
        rsi: spikeData.rsi
      };

      console.log(`📊 Opening ${tradeDirection} position from ${spikeType} spike:`, tradeParams);

      // Backend'e pozisyon açma isteği gönder
      socket.emit('open-position-from-spike', {
        username: authState.user.username,
        tradeParams
      });

    } catch (error) {
      console.error('❌ Error opening position from spike:', error);
    }
  };

  // 📊 Pozisyon P&L güncelleme fonksiyonu - Geliştirilmiş versiyon
  const updatePositionPnL = (priceData: any) => {
    try {
      const { symbol, price } = priceData;
      const currentPrice = parseFloat(price);

      setPositions(prev => {
        const updatedPositions = prev.map(position => {
          if (position.sembol === symbol && position.durum === 'ACIK') {
            const entryPrice = position.girisFiyati;
            const quantity = position.miktar;

            // Futures P&L hesaplama: (Current Price - Entry Price) * Position Size
            let pnl = 0;
            let pnlPercent = 0;

            if (position.yon === 'LONG') {
              // LONG pozisyon: Fiyat yükselirse kar
              const priceDiff = currentPrice - entryPrice;
              pnl = priceDiff * quantity; // Position size zaten contract sayısı
              pnlPercent = (priceDiff / entryPrice) * 100;
            } else if (position.yon === 'SHORT') {
              // SHORT pozisyon: Fiyat düşerse kar
              const priceDiff = entryPrice - currentPrice;
              pnl = priceDiff * quantity; // Position size zaten contract sayısı
              pnlPercent = (priceDiff / entryPrice) * 100;
            }

            // Enhanced debug log
            console.log(`📊 P&L Calculation for ${symbol}:`, {
              yon: position.yon,
              entryPrice,
              currentPrice,
              quantity,
              priceDiff: position.yon === 'LONG' ? (currentPrice - entryPrice) : (entryPrice - currentPrice),
              pnl: pnl.toFixed(4),
              pnlPercent: pnlPercent.toFixed(2),
              originalPnl: position.pnl
            });

            return {
              ...position,
              guncelFiyat: currentPrice,
              markFiyati: currentPrice,
              pnl: parseFloat(pnl.toFixed(4)),
              pnlYuzdesi: parseFloat(pnlPercent.toFixed(2))
            };
          }
          return position;
        });

        // Log if any positions were updated
        const hasUpdates = updatedPositions.some((pos, index) =>
          pos.guncelFiyat !== prev[index]?.guncelFiyat
        );

        if (hasUpdates) {
          console.log(`✅ Updated positions for ${symbol} @ ${currentPrice}`);
        }

        return updatedPositions;
      });

    } catch (error) {
      console.error('❌ Error updating position P&L:', error);
    }
  };

  // Debug log for user, balance and positions
  console.log('🔍 Current user:', authState.user);
  console.log('🔍 Current balance:', accountBalance);
  console.log('🔍 Current positions:', positions);
  console.log('🔍 Is authenticated:', authState.isAuthenticated);
  console.log('🔍 Auth loading:', authState.loading);
  console.log('🔍 LocalStorage token:', localStorage.getItem('parabot_token'));
  console.log('🔍 LocalStorage user:', localStorage.getItem('parabot_user'));

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-muted/30">
      {/* 🚀 YENİ: Spike Uyarı Bildirimi */}
      {spikeAlert && (
        <div className={`fixed top-20 right-4 z-[9999] transition-all duration-500 ${spikeAlert.show ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
          }`}>
          <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white p-4 rounded-lg shadow-2xl border border-yellow-400 min-w-[300px]">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
              <div>
                <div className="font-bold text-lg">🚨 SPIKE TESPİT EDİLDİ!</div>
                <div className="text-sm opacity-90">
                  {spikeAlert.symbol} @ ${spikeAlert.price.toFixed(4)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="border-b-2 border-primary/30 bg-gradient-to-r from-primary/10 to-accent/10 backdrop-blur-sm sticky top-0 z-50 shadow-2xl">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-to-br from-primary to-primary-glow rounded-xl shadow-lg">
                <Zap className="h-7 w-7 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
                  ParaBOT
                </h1>
                <p className="text-sm font-semibold text-foreground">
                  Spike Tespit Trading Bot
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Badge
                variant={isConnected ? "outline" : "destructive"}
                className={`flex items-center gap-2 px-4 py-2 text-sm font-bold shadow-lg ${isConnected
                  ? 'border-primary/30 bg-background text-foreground hover:bg-accent'
                  : 'bg-destructive text-destructive-foreground border-destructive'
                  }`}
              >
                {isConnected ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
                {connectionStatus}
              </Badge>

              <div className="flex items-center gap-2 text-sm bg-card px-3 py-2 rounded-lg border-2 border-border shadow-md">
                <User className="h-5 w-5 text-primary" />
                <span className="font-semibold text-foreground">{authState.user?.displayName}</span>
              </div>



              <Button variant="outline" size="sm" onClick={cikisYap} className="border-2 font-semibold hover:bg-destructive hover:text-destructive-foreground">
                <LogOut className="h-4 w-4 mr-2" />
                Çıkış
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6">
        {/* Status Alert */}
        {!isConnected && (
          <Alert className="mb-6 border-loss/30 bg-loss/5 shadow-lg">
            <WifiOff className="h-4 w-4 text-loss" />
            <AlertDescription className="text-loss">
              <strong>Bağlantı Kesildi:</strong> Binance WebSocket'e bağlanılamıyor.
              Bazı özellikler düzgün çalışmayabilir.
            </AlertDescription>
          </Alert>
        )}

        {/* Ana Dashboard */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-3 mb-6 bg-card border-2 border-primary/20 shadow-xl p-2 rounded-xl">
            <TabsTrigger
              value="ana-panel"
              className="flex items-center gap-2 font-semibold text-sm px-4 py-3 rounded-lg data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-xl transition-all duration-200"
            >
              <Activity className="h-5 w-5" />
              Ana Panel
            </TabsTrigger>

            <TabsTrigger
              value="spike-sinyaller"
              className="flex items-center gap-2 font-semibold text-sm px-4 py-3 rounded-lg data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-xl transition-all duration-200"
            >
              <Zap className="h-5 w-5" />
              Spike Sinyaller
            </TabsTrigger>
          </TabsList>

          <TabsContent value="ana-panel" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-1 space-y-6">
                <LogoVeHesapBakiyesi
                  bakiye={accountBalance}
                  onRefresh={handleRefreshBalance}
                />
                <TradingPanel
                  config={config}
                  onConfigChange={handleConfigChange}
                  isConnected={isConnected}
                  onTradingStart={handleTradingStart}
                  onTradingStop={handleTradingStop}
                />
              </div>
              <div className="lg:col-span-2 space-y-3">
                {/* 🚀 YENİ: Spike Sinyaller Bölümü */}
                <SpikeSignalList
                  signals={spikeSignals}
                  onSignalClick={(signal) => {
                    console.log('Spike sinyali tıklandı:', signal);
                  }}
                  showSettings={false}
                />
                <PositionMonitor
                  positions={positions}
                  onClosePosition={handleClosePosition}
                  onCloseAllPositions={handleCloseAllPositions}
                  onRefreshPositions={handleRefreshPositions}
                />
                <AcikEmirlerKarti
                  emirler={openOrders}
                  onEmirIptal={handleCancelOrder}
                  onCancelAllOrders={handleCancelAllOrders}
                  onRefreshOrders={handleRefreshOrders}
                />
              </div>
            </div>
          </TabsContent>





          <TabsContent value="spike-sinyaller" className="space-y-6">
            <SpikeSignalList
              signals={spikeSignals}
              onStartAnalysis={handleStartAnalysis}
              onStopAnalysis={handleStopAnalysis}
              isAnalyzing={isAnalyzing}
              settings={spikeSettings}
              onSettingsChange={handleSpikeSettingsChange}
              onSettingsSave={handleSpikeSettingsSave}
              onSettingsReset={handleSpikeSettingsReset}
            />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};
