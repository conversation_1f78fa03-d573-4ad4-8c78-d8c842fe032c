import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Clock, X, TrendingDown, Target, Shield, RefreshCw } from 'lucide-react';
import { AcikEmir } from '@/types/turkish-trading';
import { formatPrice, formatCurrency, formatQuantity } from '@/utils/priceFormatter';

interface AcikEmirlerKartiProps {
  emirler: AcikEmir[];
  onEmirIptal: (emirId: string) => void;
  onCancelAllOrders: () => void;
  onRefreshOrders: () => void;
}

export const AcikEmirlerKarti: React.FC<AcikEmirlerKartiProps> = ({
  emirler,
  onEmirIptal,
  onCancelAllOrders,
  onRefreshOrders
}) => {
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEmirTipiIcon = (tip: string) => {
    switch (tip) {
      case 'LIMIT': return <TrendingDown className="w-4 h-4" />;
      case 'STOP_LOSS': return <Shield className="w-4 h-4" />;
      case 'TAKE_PROFIT': return <Target className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getEmirTipiRenk = (tip: string) => {
    switch (tip) {
      case 'LIMIT': return 'bg-primary text-primary-foreground';
      case 'STOP_LOSS': return 'bg-loss text-loss-foreground';
      case 'TAKE_PROFIT': return 'bg-profit text-profit-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getDurumBadge = (durum: string) => {
    switch (durum) {
      case 'BEKLIYOR': return <Badge variant="outline" className="border-yellow-500 text-yellow-600">Bekliyor</Badge>;
      case 'KISMI_DOLU': return <Badge variant="outline" className="border-blue-500 text-blue-600">Kısmi Dolu</Badge>;
      case 'DOLU': return <Badge variant="default" className="bg-profit text-profit-foreground">Dolu</Badge>;
      case 'IPTAL': return <Badge variant="destructive">İptal</Badge>;
      default: return <Badge variant="outline">{durum}</Badge>;
    }
  };

  return (
    <Card className="w-full bg-gradient-to-br from-primary/5 to-secondary/30 border-primary/30 shadow-2xl hover:shadow-primary/30 transition-shadow duration-300">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-primary drop-shadow flex items-center gap-2 text-sm">
            <TrendingDown className="h-4 w-4" />
            Açık Emirler
            <Badge variant="secondary" className="ml-2 text-xs">
              {emirler.length}
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefreshOrders}
              className="h-6 px-2 text-xs hover:bg-primary/10 hover:text-primary hover:border-primary/30"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Yenile
            </Button>
            {emirler.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCancelAllOrders}
                className="h-6 px-2 text-xs hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
              >
                <X className="h-3 w-3 mr-1" />
                Tümünü İptal Et
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-2">
        <div className="space-y-1 max-h-48 overflow-y-auto">
          {emirler.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              <p className="text-sm">Hiç açık emir yok</p>
            </div>
          ) : (
            emirler.map((emir) => (
              <div key={emir.id} className={`p-2 rounded-lg border transition-all duration-200 cursor-pointer bg-muted/10 border-primary/10 hover:bg-primary/5 hover:shadow-md`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className={`p-1 rounded-full ${getEmirTipiRenk(emir.tip)}`}>
                      {getEmirTipiIcon(emir.tip)}
                    </div>
                    <div>
                      <h4 className="font-semibold text-xs">{emir.sembol}</h4>
                      <p className="text-xs text-muted-foreground">{emir.tip}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    {getDurumBadge(emir.durum)}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEmirIptal(emir.id)}
                      className="h-6 w-6 p-0 hover:bg-loss/10 hover:text-loss"
                      disabled={emir.durum === 'DOLU' || emir.durum === 'IPTAL'}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <p className="text-muted-foreground">Miktar</p>
                    <p className="font-medium">{formatQuantity(emir.miktar, emir.sembol)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Fiyat</p>
                    <p className="font-medium">${formatPrice(emir.fiyat, emir.sembol)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Tarih</p>
                    <p className="font-medium">{formatDate(emir.olusturmZamani)}</p>
                  </div>
                  {emir.pnlYuzdesi && (
                    <div>
                      <p className="text-muted-foreground">PnL %</p>
                      <p className={`font-medium ${emir.pnlYuzdesi >= 0 ? 'text-profit' : 'text-loss'}`}>
                        {emir.pnlYuzdesi >= 0 ? '+' : ''}{emir.pnlYuzdesi.toFixed(2)}%
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};