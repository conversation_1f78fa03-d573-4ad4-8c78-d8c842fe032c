import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Wallet, TrendingUp, TrendingDown, Lock, Activity, Zap } from 'lucide-react';
import { HesapBakiyesi as HesapBakiyesiType } from '@/types/turkish-trading';

interface HesapBakiyesiProps {
  bakiye: HesapBakiyesiType;
  logoUrl?: string;
}

export const HesapBakiyesi: React.FC<HesapBakiyesiProps> = ({
  bakiye,
  logoUrl
}) => {
  const formatCurrency = (amount: number): string => {
    return `$${amount.toLocaleString('tr-TR', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}`;
  };

  const getPnlColor = (pnl: number): string => {
    if (pnl > 0) return 'text-profit';
    if (pnl < 0) return 'text-loss';
    return 'text-muted-foreground';
  };

  const getPnlIcon = (pnl: number) => {
    if (pnl > 0) return <TrendingUp className="h-3 w-3" />;
    if (pnl < 0) return <TrendingDown className="h-3 w-3" />;
    return <Activity className="h-3 w-3" />;
  };

  return (
    <Card className="w-full bg-gradient-to-br from-card to-position-bg border-border/50 shadow-lg">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {logoUrl ? (
              <img 
                src={logoUrl} 
                alt="ParaBOT Logo" 
                className="h-8 w-8 rounded-lg"
              />
            ) : (
              <div className="p-2 bg-gradient-to-br from-primary to-primary-glow rounded-lg">
                <Zap className="h-4 w-4 text-primary-foreground" />
              </div>
            )}
            <div>
              <CardTitle className="text-lg text-primary">ParaBOT</CardTitle>
              <p className="text-xs text-muted-foreground">Hesap Bakiyesi</p>
            </div>
          </div>
          <Badge variant="outline" className="border-primary/30 text-primary">
            <Wallet className="h-3 w-3 mr-1" />
            USDT
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Ana Bakiye */}
        <div className="p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/20">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">Toplam Bakiye</p>
            <p className="text-2xl font-bold text-primary">
              {formatCurrency(bakiye.toplamBakiye)}
            </p>
          </div>
        </div>

        {/* Detaylar */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Kullanılabilir:</span>
            <span className="font-medium">
              {formatCurrency(bakiye.kullanilabilirBakiye)}
            </span>
          </div>

          <Separator className="bg-border/30" />

          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground flex items-center gap-1">
              <Lock className="h-3 w-3" />
              Pozisyon Marjini:
            </span>
            <span className="font-medium">
              {formatCurrency(bakiye.pozisyonMarjini)}
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Emir Marjini:</span>
            <span className="font-medium">
              {formatCurrency(bakiye.emirMarjini)}
            </span>
          </div>

          <Separator className="bg-border/30" />

          {/* Gerçekleşmemiş PnL */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">
              Gerçekleşmemiş PnL:
            </span>
            <div className={`flex items-center gap-1 font-medium ${getPnlColor(bakiye.gerceklesmemisPnl)}`}>
              {getPnlIcon(bakiye.gerceklesmemisPnl)}
              {bakiye.gerceklesmemisPnl >= 0 ? '+' : ''}
              {formatCurrency(bakiye.gerceklesmemisPnl)}
            </div>
          </div>
        </div>

        {/* Risk Göstergesi */}
        <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
          <div className="flex justify-between items-center text-sm">
            <span className="text-muted-foreground">Risk Oranı:</span>
            <div className="flex items-center gap-2">
              <span className="font-medium">
                {((bakiye.pozisyonMarjini / bakiye.toplamBakiye) * 100).toFixed(1)}%
              </span>
              <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
                <div 
                  className={`h-full transition-all duration-300 ${
                    (bakiye.pozisyonMarjini / bakiye.toplamBakiye) > 0.8 
                      ? 'bg-loss' 
                      : (bakiye.pozisyonMarjini / bakiye.toplamBakiye) > 0.5
                        ? 'bg-yellow-500'
                        : 'bg-profit'
                  }`}
                  style={{ 
                    width: `${Math.min((bakiye.pozisyonMarjini / bakiye.toplamBakiye) * 100, 100)}%` 
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};