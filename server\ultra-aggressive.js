/**
 * Ultra Aggressive Settings for Testing
 */

const axios = require('axios');

async function setUltraAggressive() {
  try {
    console.log('🔥 Setting ULTRA AGGRESSIVE settings for testing...\n');

    const ultraConfig = {
      priceThreshold: 0.001,    // %0.1 fiyat artışı (çok düşük)
      slopeThreshold: 0.001,    // Çok düşük eğim
      volumeMultiplier: 1.01,   // Sadece %1 hacim artışı
      minDataPoints: 5,         // Minimum veri
      cooldownPeriod: 1000      // 1 saniye cooldown
    };

    const response = await axios.post('http://localhost:3001/api/financial-detector/config', ultraConfig);
    console.log('✅ Ultra aggressive config applied:', response.data.message);
    console.log('📊 New config:', JSON.stringify(response.data.config, null, 2));

    console.log('\n🚀 ULTRA AGGRESSIVE mode active!');
    console.log('   🎯 Price: %0.1+ artı<PERSON>');
    console.log('   📈 Slope: 0.001+ eğim');
    console.log('   📊 Volume: 1.01x+ hacim');
    console.log('   ⏰ Cooldown: 1 saniye');
    console.log('\n💥 Bu ayarlarla çok fazla spike gelecek!');

  } catch (error) {
    console.error('❌ Failed:', error.message);
  }
}

setUltraAggressive();
