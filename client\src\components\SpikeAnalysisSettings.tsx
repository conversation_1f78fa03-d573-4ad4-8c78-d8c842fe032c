import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { financialDetectorService, FinancialDetectorConfig } from '@/services/trading';
import {
    Settings,
    Zap,
    TrendingUp,
    Volume2,
    Target,
    Save,
    RotateCcw,
    Activity,
    Gauge
} from 'lucide-react';

export interface SpikeAnalysisSettings {
    // Financial Detector Settings (Backend)
    priceThreshold: number; // % as decimal (0.01 = 1%)
    slopeThreshold: number; // Slope value
    volumeMultiplier: number; // Volume multiplier
    minDataPoints: number; // Minimum data points
    cooldownPeriod: number; // Cooldown in ms

    // Trading Settings (Frontend only)
    maxPositions: number; // Maksimum pozisyon sayısı
    autoTrade: boolean; // Otomatik ticaret
    takeProfitPercent: number; // Kar alma yüzdesi
    stopLossPercent: number; // Zarar durdurma yüzdesi
    tradeAmount: number; // İşlem miktarı (USDT)
    leverage: number; // Kaldıraç
}

interface SpikeAnalysisSettingsProps {
    onSave?: (settings: SpikeAnalysisSettings) => void;
    onReset?: () => void;
}

export const SpikeAnalysisSettings: React.FC<SpikeAnalysisSettingsProps> = ({
    onSave,
    onReset
}) => {
    const [settings, setSettings] = useState<SpikeAnalysisSettings>({
        // Financial Detector defaults
        priceThreshold: 0.004, // 0.4%
        slopeThreshold: 0.1,
        volumeMultiplier: 1.5,
        minDataPoints: 30,
        cooldownPeriod: 300000, // 5 minutes

        // Trading defaults
        maxPositions: 3,
        autoTrade: false,
        takeProfitPercent: 0.8,
        stopLossPercent: 0.8,
        tradeAmount: 100,
        leverage: 20
    });

    const [stats, setStats] = useState<any>(null);
    const [isConnected, setIsConnected] = useState(false);
    const [loading, setLoading] = useState(false);

    // Load current settings on mount
    useEffect(() => {
        loadCurrentSettings();
        const interval = setInterval(loadStats, 5000); // Update stats every 5s
        return () => clearInterval(interval);
    }, []);

    const loadCurrentSettings = async () => {
        try {
            const response = await financialDetectorService.getStats();
            setStats(response.stats);
            setSettings(prev => ({
                ...prev,
                ...response.stats.config
            }));
            setIsConnected(true);
        } catch (error) {
            console.error('Failed to load settings:', error);
            setIsConnected(false);
        }
    };

    const loadStats = async () => {
        try {
            const response = await financialDetectorService.getStats();
            setStats(response.stats);
            setIsConnected(true);
        } catch (error) {
            setIsConnected(false);
        }
    };

    const handleChange = (key: keyof SpikeAnalysisSettings, value: any) => {
        setSettings(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handleSave = async () => {
        setLoading(true);
        try {
            // Save financial detector config
            const detectorConfig = {
                priceThreshold: settings.priceThreshold,
                slopeThreshold: settings.slopeThreshold,
                volumeMultiplier: settings.volumeMultiplier,
                minDataPoints: settings.minDataPoints,
                cooldownPeriod: settings.cooldownPeriod
            };

            await financialDetectorService.updateConfig(detectorConfig);

            // Call parent save if provided
            if (onSave) {
                onSave(settings);
            }

            // Reload stats
            await loadCurrentSettings();

            console.log('✅ Settings saved successfully');
        } catch (error) {
            console.error('❌ Failed to save settings:', error);
        } finally {
            setLoading(false);
        }
    };

    const handlePreset = async (presetName: 'conservative' | 'standard' | 'aggressive' | 'scalping') => {
        setLoading(true);
        try {
            await financialDetectorService.applyPreset(presetName);
            await loadCurrentSettings();
            console.log(`✅ Applied ${presetName} preset`);
        } catch (error) {
            console.error(`❌ Failed to apply ${presetName} preset:`, error);
        } finally {
            setLoading(false);
        }
    };

    const handleReset = async () => {
        if (onReset) {
            onReset();
        }
        await handlePreset('standard');
    };

    return (
        <Card className="w-full bg-gradient-to-br from-primary/5 to-position-bg border-primary/30 shadow-2xl">
            <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                    <CardTitle className="text-primary drop-shadow flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        Financial Spike Detector
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Badge variant={isConnected ? "default" : "destructive"} className="text-xs">
                            {isConnected ? "Bağlı" : "Bağlantı Yok"}
                        </Badge>
                        {stats && (
                            <Badge variant="outline" className="text-xs">
                                {stats.symbolsTracked} Coin
                            </Badge>
                        )}
                    </div>
                </div>

                {/* Stats Row */}
                {stats && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-primary">{stats.symbolsTracked}</div>
                            <div className="text-xs text-muted-foreground">Tracked Coins</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-green-500">{stats.spikesDetected || 0}</div>
                            <div className="text-xs text-muted-foreground">Spikes Detected</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-blue-500">{stats.dataPoints}</div>
                            <div className="text-xs text-muted-foreground">Data Points</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-orange-500">
                                {stats.config?.cooldownPeriod ? Math.round(stats.config.cooldownPeriod / 1000) : 0}s
                            </div>
                            <div className="text-xs text-muted-foreground">Cooldown</div>
                        </div>
                    </div>
                )}
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Preset Seçici */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                        <Gauge className="h-4 w-4" />
                        Hızlı Ayarlar
                    </h3>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreset('conservative')}
                            disabled={loading}
                            className="text-xs"
                        >
                            Conservative
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreset('standard')}
                            disabled={loading}
                            className="text-xs"
                        >
                            Standard
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreset('aggressive')}
                            disabled={loading}
                            className="text-xs"
                        >
                            Aggressive
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePreset('scalping')}
                            disabled={loading}
                            className="text-xs"
                        >
                            Scalping
                        </Button>
                    </div>
                </div>

                <Separator className="bg-border/30" />

                {/* Financial Detector Ayarları */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                        <Activity className="h-4 w-4" />
                        Spike Detection Ayarları
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="priceThreshold" className="text-sm font-medium">
                                Price Threshold (%)
                            </Label>
                            <div className="flex items-center gap-2">
                                <Slider
                                    id="priceThreshold"
                                    min={0.001}
                                    max={0.05}
                                    step={0.001}
                                    value={[settings.priceThreshold]}
                                    onValueChange={(value) => handleChange('priceThreshold', value[0])}
                                    className="flex-1"
                                />
                                <Input
                                    type="number"
                                    value={(settings.priceThreshold * 100).toFixed(3)}
                                    onChange={(e) => handleChange('priceThreshold', parseFloat(e.target.value) / 100)}
                                    className="w-20"
                                    min={0.1}
                                    max={5}
                                    step={0.001}
                                />
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Fiyatın MA10'dan ne kadar yukarıda olması gerekiyor
                            </p>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="slopeThreshold" className="text-sm font-medium">
                                Slope Threshold
                            </Label>
                            <div className="flex items-center gap-2">
                                <Slider
                                    id="slopeThreshold"
                                    min={0.001}
                                    max={0.5}
                                    step={0.001}
                                    value={[settings.slopeThreshold]}
                                    onValueChange={(value) => handleChange('slopeThreshold', value[0])}
                                    className="flex-1"
                                />
                                <Input
                                    type="number"
                                    value={settings.slopeThreshold.toFixed(3)}
                                    onChange={(e) => handleChange('slopeThreshold', parseFloat(e.target.value))}
                                    className="w-20"
                                    min={0.001}
                                    max={0.5}
                                    step={0.001}
                                />
                            </div>
                            <p className="text-xs text-muted-foreground">
                                MA10 eğiminin minimum değeri (momentum)
                            </p>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="volumeMultiplier" className="text-sm font-medium">
                                Volume Multiplier
                            </Label>
                            <div className="flex items-center gap-2">
                                <Slider
                                    id="volumeMultiplier"
                                    min={1}
                                    max={10}
                                    step={0.1}
                                    value={[settings.volumeMultiplier]}
                                    onValueChange={(value) => handleChange('volumeMultiplier', value[0])}
                                    className="flex-1"
                                />
                                <Input
                                    type="number"
                                    value={settings.volumeMultiplier.toFixed(1)}
                                    onChange={(e) => handleChange('volumeMultiplier', parseFloat(e.target.value))}
                                    className="w-20"
                                    min={1}
                                    max={10}
                                    step={0.1}
                                />
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Ortalama hacmin kaç katı olmalı
                            </p>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="minDataPoints" className="text-sm font-medium">
                                Min Data Points
                            </Label>
                            <div className="flex items-center gap-2">
                                <Slider
                                    id="minDataPoints"
                                    min={5}
                                    max={50}
                                    step={1}
                                    value={[settings.minDataPoints]}
                                    onValueChange={(value) => handleChange('minDataPoints', value[0])}
                                    className="flex-1"
                                />
                                <Input
                                    type="number"
                                    value={settings.minDataPoints}
                                    onChange={(e) => handleChange('minDataPoints', parseInt(e.target.value))}
                                    className="w-20"
                                    min={5}
                                    max={50}
                                />
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Spike tespiti için minimum kline sayısı
                            </p>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="cooldownPeriod" className="text-sm font-medium">
                                Cooldown Period (seconds)
                            </Label>
                            <div className="flex items-center gap-2">
                                <Slider
                                    id="cooldownPeriod"
                                    min={60}
                                    max={600}
                                    step={30}
                                    value={[settings.cooldownPeriod / 1000]}
                                    onValueChange={(value) => handleChange('cooldownPeriod', value[0] * 1000)}
                                    className="flex-1"
                                />
                                <Input
                                    type="number"
                                    value={Math.round(settings.cooldownPeriod / 1000)}
                                    onChange={(e) => handleChange('cooldownPeriod', parseInt(e.target.value) * 1000)}
                                    className="w-20"
                                    min={60}
                                    max={600}
                                />
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Aynı coin için spike arası bekleme süresi
                            </p>
                        </div>
                    </div>
                </div>



                {/* Aksiyon Butonları */}
                <div className="flex items-center justify-between pt-4 border-t border-border/30">
                    <Button
                        variant="outline"
                        onClick={handleReset}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <RotateCcw className="h-4 w-4" />
                        Reset to Standard
                    </Button>

                    <Button
                        onClick={handleSave}
                        disabled={!isConnected || loading}
                        className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary"
                    >
                        <Save className="h-4 w-4" />
                        {loading ? 'Saving...' : 'Save Settings'}
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}; 