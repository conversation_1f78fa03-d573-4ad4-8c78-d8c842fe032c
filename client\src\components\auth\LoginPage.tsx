import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Zap, User, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { LoginCredentials } from '@/types/auth';

export const LoginPage = () => {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    kullaniciAdi: '',
    sifre: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const { authState, girisYap } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await girisYap(credentials);
  };

  const handleChange = (field: keyof LoginCredentials, value: string) => {
    setCredentials(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-muted/30 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo ve Başlık */}
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="p-4 bg-gradient-to-br from-primary to-primary-glow rounded-2xl shadow-2xl animate-pulse" style={{ boxShadow: '0 0 40px 0 hsl(213,100%,54%,0.15)' }}>
              <Zap className="h-12 w-12 text-primary-foreground drop-shadow-lg" />
            </div>
          </div>
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent drop-shadow-md">
              ParaBOT
            </h1>
            <p className="text-muted-foreground mt-2">
              Kripto Trading Bot Sistemi
            </p>
          </div>
        </div>

        {/* Login Formu */}
        <Card className="shadow-2xl border-primary/30 bg-gradient-to-br from-card to-primary/5">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl text-primary drop-shadow">Giriş Yap</CardTitle>
            <p className="text-sm text-muted-foreground">
              Hesabınıza giriş yapın
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Kullanıcı Adı */}
              <div className="space-y-2">
                <Label htmlFor="kullaniciAdi">Kullanıcı Adı</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-primary" />
                  <Input
                    id="kullaniciAdi"
                    type="text"
                    placeholder="Kullanıcı adınızı girin"
                    value={credentials.kullaniciAdi}
                    onChange={(e) => handleChange('kullaniciAdi', e.target.value)}
                    className="pl-10 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md"
                    required
                  />
                </div>
              </div>

              {/* Şifre */}
              <div className="space-y-2">
                <Label htmlFor="sifre">Şifre</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-primary" />
                  <Input
                    id="sifre"
                    type={showPassword ? "text" : "password"}
                    placeholder="Şifrenizi girin"
                    value={credentials.sifre}
                    onChange={(e) => handleChange('sifre', e.target.value)}
                    className="pl-10 pr-10 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 text-primary hover:bg-primary/10"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Hata Mesajı */}
              {authState.error && (
                <Alert variant="destructive">
                  <AlertDescription>{authState.error}</AlertDescription>
                </Alert>
              )}

              {/* Giriş Butonu */}
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-primary to-primary-glow shadow-lg hover:from-primary-glow hover:to-primary text-primary-foreground border-2 border-primary/30 hover:border-primary/60"
                disabled={authState.loading}
              >
                {authState.loading ? 'Giriş yapılıyor...' : 'Giriş Yap'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};