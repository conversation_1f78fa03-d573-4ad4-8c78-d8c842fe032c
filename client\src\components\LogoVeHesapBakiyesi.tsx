import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { TrendingUp, TrendingDown, Wallet, DollarSign, PieChart, Shield, RefreshCw } from 'lucide-react';
import { HesapBakiyesi } from '@/types/turkish-trading';

interface LogoVeHesapBakiyesiProps {
  bakiye: HesapBakiyesi;
  logoUrl?: string;
  onRefresh?: () => void;
}

export const LogoVeHesapBakiyesi: React.FC<LogoVeHesapBakiyesiProps> = ({
  bakiye,
  logoUrl = '/logo.png',
  onRefresh
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const pnlPositive = bakiye.gerceklesmemisPnl >= 0;
  // Kaldıraçlı işlemler için gerçek minimum: 20x kaldıraçla 0.3 USD yeterli
  const isLowBalance = bakiye.kullanilabilirBakiye < 0.5; // Minimum 0.5 USDT (güvenlik marjı ile)

  return (
    <Card className="w-full bg-gradient-to-br from-primary/5 to-secondary/30 border-primary/30 shadow-2xl hover:shadow-primary/30 transition-shadow duration-300">
      <CardContent className="p-4">
        {/* Logo ve Başlık */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center shadow-md">
              <DollarSign className="w-4 h-4 text-primary" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-primary drop-shadow">ParaBOT</h1>
              <p className="text-xs text-muted-foreground">Kripto Trading Bot</p>
            </div>
          </div>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="h-8 w-8 p-0 border-primary/30 hover:border-primary/60 hover:bg-primary/10 text-primary"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
        </div>

        <Separator className="mb-4" />

        {/* Hesap Bakiyesi */}
        <div className="space-y-3">
          {/* Toplam Bakiye */}
          <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
            <div className="flex items-center gap-2">
              <Wallet className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium">Toplam Bakiye</span>
            </div>
            <div className="text-right">
              <p className="text-base font-bold text-primary">
                {formatCurrency(bakiye.toplamBakiye)}
              </p>
            </div>
          </div>

          {/* Kullanılabilir Bakiye */}
          <div className={`flex items-center justify-between p-2 rounded-lg ${
            isLowBalance ? 'bg-destructive/10 border border-destructive/30' : 'bg-muted/10'
          }`}>
            <div className="flex items-center gap-2">
              <DollarSign className={`w-3 h-3 ${isLowBalance ? 'text-destructive' : 'text-muted-foreground'}`} />
              <span className="text-xs">Kullanılabilir</span>
              {isLowBalance && (
                <Badge variant="destructive" className="text-xs px-1 py-0">
                  Düşük
                </Badge>
              )}
            </div>
            <span className={`text-xs font-medium ${isLowBalance ? 'text-destructive' : ''}`}>
              {formatCurrency(bakiye.kullanilabilirBakiye)}
            </span>
          </div>

          {/* Gerçekleşmemiş PnL */}
          <div className="flex items-center justify-between p-2 bg-muted/10 rounded-lg">
            <div className="flex items-center gap-2">
              {pnlPositive ? (
                <TrendingUp className="w-3 h-3 text-profit" />
              ) : (
                <TrendingDown className="w-3 h-3 text-loss" />
              )}
              <span className="text-xs">Gerçekleşmemiş PnL</span>
            </div>
            <Badge
              variant={pnlPositive ? "default" : "destructive"}
              className={`text-xs ${pnlPositive ? "bg-profit text-profit-foreground" : "bg-loss text-loss-foreground"}`}
            >
              {pnlPositive ? '+' : ''}{formatCurrency(bakiye.gerceklesmemisPnl)}
            </Badge>
          </div>

          {/* Pozisyon Marjini */}
          <div className="flex items-center justify-between p-2 bg-muted/10 rounded-lg">
            <div className="flex items-center gap-2">
              <PieChart className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs">Pozisyon Marjini</span>
            </div>
            <span className="text-xs font-medium">
              {formatCurrency(bakiye.pozisyonMarjini)}
            </span>
          </div>

          {/* Emir Marjini */}
          <div className="flex items-center justify-between p-2 bg-muted/10 rounded-lg">
            <div className="flex items-center gap-2">
              <Shield className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs">Emir Marjini</span>
            </div>
            <span className="text-xs font-medium">
              {formatCurrency(bakiye.emirMarjini)}
            </span>
          </div>

          {/* Düşük Bakiye Uyarısı */}
          {isLowBalance && (
            <div className="p-2 bg-destructive/10 border border-destructive/30 rounded-lg">
              <div className="flex items-center gap-2 text-xs text-destructive">
                <Shield className="w-3 h-3" />
                <span className="font-medium">Uyarı: Düşük bakiye!</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                20x kaldıraçla işlem için minimum 0.5 USDT önerilir.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};