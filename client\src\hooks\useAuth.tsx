import React, { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { User, LoginCredentials, AuthState } from '@/types/auth';

// Auth Context
const AuthContext = createContext<{
  authState: AuthState;
  girisYap: (credentials: LoginCredentials) => Promise<boolean>;
  cikisYap: () => void;
} | null>(null);

// Custom Hook
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth Hook Implementation
export const useAuthHook = () => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true,
    error: null,
  });

  // Sayfa yüklendiğinde token kontrolü
  useEffect(() => {
    const token = localStorage.getItem('parabot_token');
    const userData = localStorage.getItem('parabot_user');
    
    if (token && userData) {
      try {
        const user = JSON.parse(userData);
        setAuthState({
          isAuthenticated: true,
          user,
          loading: false,
          error: null,
        });
      } catch (error) {
        localStorage.removeItem('parabot_token');
        localStorage.removeItem('parabot_user');
        setAuthState({
          isAuthenticated: false,
          user: null,
          loading: false,
          error: null,
        });
      }
    } else {
      setAuthState(prev => ({ ...prev, loading: false }));
    }
  }, []);

  const girisYap = async (credentials: LoginCredentials): Promise<boolean> => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (response.ok) {
        // Lokal storage'a kaydet
        localStorage.setItem('parabot_token', data.token);
        localStorage.setItem('parabot_user', JSON.stringify(data.user));

        setAuthState({
          isAuthenticated: true,
          user: data.user,
          loading: false,
          error: null,
        });

        return true;
      } else {
        setAuthState({
          isAuthenticated: false,
          user: null,
          loading: false,
          error: data.error || 'Giriş yapılırken hata oluştu',
        });
        return false;
      }
    } catch (error) {
      setAuthState({
        isAuthenticated: false,
        user: null,
        loading: false,
        error: 'Giriş yapılırken hata oluştu',
      });
      return false;
    }
  };

  const cikisYap = () => {
    localStorage.removeItem('parabot_token');
    localStorage.removeItem('parabot_user');
    setAuthState({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null,
    });
  };

  return {
    authState,
    girisYap,
    cikisYap,
  };
};

// Auth Provider Component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const auth = useAuthHook();
  
  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};