/**
 * Trend Analysis Library
 * Trend analizi ve piyasa durumu tespit fonksiyonları
 */

import { calculateMA, calculateSlope } from './technical-indicators.js';

/**
 * Trend Direction (Trend Yönü)
 * @param {number[]} closes - <PERSON><PERSON><PERSON><PERSON> fiyatları
 * @param {number} shortPeriod - Kısa periyot MA
 * @param {number} longPeriod - Uzun periyot MA
 * @returns {string} 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS'
 */
export const getTrendDirection = (closes, shortPeriod = 10, longPeriod = 20) => {
  if (!closes || closes.length < longPeriod) {
    return 'UNKNOWN';
  }
  
  const shortMA = calculateMA(closes, shortPeriod);
  const longMA = calculateMA(closes, longPeriod);
  const slope = calculateSlope(closes, longPeriod, 3);
  
  if (!shortMA || !longMA || slope === null) {
    return 'UNKNOWN';
  }
  
  // MA crossover + slope analizi
  const maDiff = ((shortMA - longMA) / longMA) * 100;
  
  if (maDiff > 0.5 && slope > 0) {
    return 'UPTREND';
  } else if (maDiff < -0.5 && slope < 0) {
    return 'DOWNTREND';
  } else {
    return 'SIDEWAYS';
  }
};

/**
 * Trend Strength (Trend Gücü)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number} period - Analiz periyodu
 * @returns {number} 0-100 arası güç değeri
 */
export const getTrendStrength = (closes, period = 20) => {
  if (!closes || closes.length < period) {
    return 0;
  }
  
  const ma = calculateMA(closes, period);
  const slope = calculateSlope(closes, period, 5);
  
  if (!ma || slope === null) return 0;
  
  // Fiyatın MA'dan sapması
  const currentPrice = closes[closes.length - 1];
  const deviation = Math.abs((currentPrice - ma) / ma) * 100;
  
  // Slope'un mutlak değeri
  const slopeStrength = Math.abs(slope / ma) * 100;
  
  // Trend gücü hesaplama
  const strength = Math.min(100, (deviation * 30) + (slopeStrength * 70));
  
  return parseFloat(strength.toFixed(2));
};

/**
 * Support/Resistance Levels (Destek/Direnç Seviyeleri)
 * @param {number[]} highs - Yüksek fiyatlar
 * @param {number[]} lows - Düşük fiyatlar
 * @param {number} period - Analiz periyodu
 * @returns {Object} {support: number[], resistance: number[]}
 */
export const getSupportResistanceLevels = (highs, lows, period = 20) => {
  if (!highs || !lows || highs.length < period) {
    return { support: [], resistance: [] };
  }
  
  const recentHighs = highs.slice(-period);
  const recentLows = lows.slice(-period);
  
  // Pivot noktalarını bul
  const pivotHighs = [];
  const pivotLows = [];
  
  for (let i = 2; i < recentHighs.length - 2; i++) {
    // Pivot high: ortadaki değer çevresindeki 4 değerden yüksek
    if (recentHighs[i] > recentHighs[i-1] && 
        recentHighs[i] > recentHighs[i-2] &&
        recentHighs[i] > recentHighs[i+1] && 
        recentHighs[i] > recentHighs[i+2]) {
      pivotHighs.push(recentHighs[i]);
    }
    
    // Pivot low: ortadaki değer çevresindeki 4 değerden düşük
    if (recentLows[i] < recentLows[i-1] && 
        recentLows[i] < recentLows[i-2] &&
        recentLows[i] < recentLows[i+1] && 
        recentLows[i] < recentLows[i+2]) {
      pivotLows.push(recentLows[i]);
    }
  }
  
  // Benzer seviyeleri grupla
  const groupLevels = (levels, tolerance = 0.5) => {
    const grouped = [];
    const sorted = [...levels].sort((a, b) => a - b);
    
    for (const level of sorted) {
      const existing = grouped.find(g => Math.abs(g - level) / level * 100 < tolerance);
      if (!existing) {
        grouped.push(level);
      }
    }
    
    return grouped;
  };
  
  return {
    support: groupLevels(pivotLows),
    resistance: groupLevels(pivotHighs)
  };
};

/**
 * Market Phase Detection (Piyasa Fazı Tespiti)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number[]} volumes - Hacim değerleri
 * @returns {string} 'ACCUMULATION' | 'MARKUP' | 'DISTRIBUTION' | 'MARKDOWN'
 */
export const getMarketPhase = (closes, volumes) => {
  if (!closes || !volumes || closes.length < 20) {
    return 'UNKNOWN';
  }
  
  const trend = getTrendDirection(closes);
  const trendStrength = getTrendStrength(closes);
  
  // Hacim analizi
  const recentVolumes = volumes.slice(-10);
  const avgVolume = recentVolumes.reduce((a, b) => a + b, 0) / recentVolumes.length;
  const currentVolume = volumes[volumes.length - 1];
  const volumeRatio = currentVolume / avgVolume;
  
  // Fiyat volatilitesi
  const recentCloses = closes.slice(-10);
  const priceChanges = [];
  for (let i = 1; i < recentCloses.length; i++) {
    priceChanges.push(Math.abs((recentCloses[i] - recentCloses[i-1]) / recentCloses[i-1]));
  }
  const avgVolatility = priceChanges.reduce((a, b) => a + b, 0) / priceChanges.length;
  
  // Faz belirleme
  if (trend === 'SIDEWAYS' && volumeRatio < 1.2 && avgVolatility < 0.02) {
    return 'ACCUMULATION';
  } else if (trend === 'UPTREND' && trendStrength > 60 && volumeRatio > 1.5) {
    return 'MARKUP';
  } else if (trend === 'SIDEWAYS' && volumeRatio > 1.3 && avgVolatility > 0.025) {
    return 'DISTRIBUTION';
  } else if (trend === 'DOWNTREND' && trendStrength > 40) {
    return 'MARKDOWN';
  }
  
  return 'TRANSITION';
};

/**
 * Breakout Detection (Kırılım Tespiti)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number[]} highs - Yüksek fiyatlar
 * @param {number[]} lows - Düşük fiyatlar
 * @param {number[]} volumes - Hacim değerleri
 * @returns {Object|null} Breakout bilgisi
 */
export const detectBreakout = (closes, highs, lows, volumes) => {
  if (!closes || closes.length < 20) {
    return null;
  }
  
  const currentPrice = closes[closes.length - 1];
  const { support, resistance } = getSupportResistanceLevels(highs, lows);
  
  // Hacim kontrolü
  const avgVolume = volumes.slice(-10).reduce((a, b) => a + b, 0) / 10;
  const currentVolume = volumes[volumes.length - 1];
  const volumeConfirmation = currentVolume > avgVolume * 1.5;
  
  // Direnç kırılımı
  for (const resistanceLevel of resistance) {
    if (currentPrice > resistanceLevel * 1.002 && volumeConfirmation) {
      return {
        type: 'RESISTANCE_BREAKOUT',
        level: resistanceLevel,
        currentPrice,
        strength: getTrendStrength(closes),
        volumeRatio: currentVolume / avgVolume
      };
    }
  }
  
  // Destek kırılımı
  for (const supportLevel of support) {
    if (currentPrice < supportLevel * 0.998 && volumeConfirmation) {
      return {
        type: 'SUPPORT_BREAKDOWN',
        level: supportLevel,
        currentPrice,
        strength: getTrendStrength(closes),
        volumeRatio: currentVolume / avgVolume
      };
    }
  }
  
  return null;
};

/**
 * Consolidation Detection (Konsolidasyon Tespiti)
 * @param {number[]} closes - Kapanış fiyatları
 * @param {number} period - Analiz periyodu
 * @returns {Object|null} Konsolidasyon bilgisi
 */
export const detectConsolidation = (closes, period = 20) => {
  if (!closes || closes.length < period) {
    return null;
  }
  
  const recentCloses = closes.slice(-period);
  const high = Math.max(...recentCloses);
  const low = Math.min(...recentCloses);
  const range = ((high - low) / low) * 100;
  
  // %2'den az hareket varsa konsolidasyon
  if (range < 2) {
    const currentPrice = closes[closes.length - 1];
    const midPoint = (high + low) / 2;
    
    return {
      isConsolidating: true,
      range: parseFloat(range.toFixed(2)),
      high,
      low,
      midPoint,
      currentPrice,
      position: currentPrice > midPoint ? 'UPPER_HALF' : 'LOWER_HALF'
    };
  }
  
  return null;
};
