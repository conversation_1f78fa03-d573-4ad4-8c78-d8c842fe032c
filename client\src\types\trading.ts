// ParaBOT Trading Types

export interface TradingConfig {
  leverage: number;
  tpPercentage: number;
  slPercentage: number;
  maxPositions: number;
  spikeThreshold: number;
  volumeMultiplier: number;
  maxDailyLoss: number;
  autoTrade: boolean;
}

export interface SpikeSignal {
  id: string;
  symbol: string;
  timestamp: number;
  price: number;
  ma5: number;
  ma10: number;
  slope: number;
  volume: number;
  volumeAvg: number;
  confidence: 'LOW' | 'MEDIUM' | 'HIGH' | 'CONFIRMED_SPIKE';
  status: 'DETECTED' | 'TRADING' | 'COMPLETED' | 'FAILED';
}

export interface Position {
  id: string;
  symbol: string;
  side: 'LONG' | 'SHORT';
  size: number;
  entryPrice: number;
  markPrice: number;
  pnl: number;
  pnlPercentage: number;
  leverage: number;
  tpPrice?: number;
  slPrice?: number;
  status: 'OPEN' | 'CLOSING' | 'CLOSED';
  openTime: number;
}

export interface OrderUpdate {
  symbol: string;
  orderId: string;
  type: 'MARKET' | 'LIMIT' | 'STOP_MARKET' | 'TAKE_PROFIT_MARKET';
  side: 'BUY' | 'SELL';
  status: 'NEW' | 'FILLED' | 'CANCELED' | 'REJECTED';
  executedQty: number;
  price: number;
  timestamp: number;
}

export interface MarketData {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  markPrice: number;
  timestamp: number;
}

export interface KlineData {
  symbol: string;
  openTime: number;
  closeTime: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  interval: string;
}

export interface AccountUpdate {
  balances: Array<{
    asset: string;
    walletBalance: number;
    unrealizedPnl: number;
  }>;
  positions: Position[];
  timestamp: number;
}

export interface WebSocketMessage {
  stream: string;
  data: any;
}

export interface TradingStats {
  totalTrades: number;
  winRate: number;
  totalPnl: number;
  dailyPnl: number;
  bestTrade: number;
  worstTrade: number;
  avgHoldTime: number;
}