/**
 * REAL Spike Detection Test
 * Gerçek zamanlı Binance Futures spike detection - GERÇEK ORTAM
 */

import { SpikeDetector } from './server/algorithms/spike-detection.js';

// GERÇEK ORTAM konfigürasyonu
const REAL_CONFIG = {
  algorithm: 'advanced',
  cooldownPeriod: 30 * 1000, // 30 saniye
  symbols: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'], // İzlenecek semboller
  minDataPoints: 30,
  maxDataPoints: 50
};

// İstatistik takibi
let totalSpikes = 0;
let spikesBySymbol = new Map();
let lastSpikeTime = 0;

// GERÇEK spike detector oluştur
const detector = new SpikeDetector(REAL_CONFIG);

// Event listeners
detector.on('connected', () => {
  console.log('🚀 GERÇEK Binance Futures Spike Detector Started!');
  console.log('📊 REAL Configuration:');
  console.log(`   Algorithm: ${REAL_CONFIG.algorithm}`);
  console.log(`   Cooldown: ${REAL_CONFIG.cooldownPeriod / 1000}s`);
  console.log(`   Symbols: ${REAL_CONFIG.symbols.join(', ')}`);
  console.log('');
  console.log('🔍 GERÇEK market data monitoring...');
  console.log('📋 REAL Criteria:');
  console.log('   ✓ MA5 > MA10 (Moving Average Crossover)');
  console.log('   ✓ RSI > 65 (High Momentum for SHORT)');
  console.log('   ✓ MACD > 0 (Trend Confirmation)');
  console.log('   ✓ Volume > 1.5x Average (Volume Spike)');
  console.log('   ✓ Price Velocity > 0.5% in 1min (Price Movement)');
  console.log('   ✓ Confidence Score > 60% (Overall Reliability)');
  console.log('');
  console.log('⚠️  GERÇEK ORTAM - Spike tespit edildiğinde GERÇEK trading yapılabilir!');
  console.log('⏰ Waiting for REAL spikes...\n');
});

detector.on('spike', (spike) => {
  totalSpikes++;
  lastSpikeTime = Date.now();
  
  // Symbol bazlı sayaç
  const symbolCount = spikesBySymbol.get(spike.sembol) || 0;
  spikesBySymbol.set(spike.sembol, symbolCount + 1);
  
  // Spike bilgilerini göster
  console.log('🚨 ═══════════════════════════════════════════════════════════');
  console.log(`🚨 SPIKE DETECTED #${totalSpikes}`);
  console.log('🚨 ═══════════════════════════════════════════════════════════');
  console.log(`📈 Symbol: ${spike.sembol}`);
  console.log(`💰 Price: $${spike.fiyat.toLocaleString()}`);
  console.log(`📊 Time: ${new Date(spike.zaman).toLocaleString()}`);
  console.log('');
  console.log('📋 Technical Analysis:');
  console.log(`   MA5: $${spike.ma5.toLocaleString()}`);
  console.log(`   MA10: $${spike.ma10.toLocaleString()}`);
  console.log(`   MA Difference: ${spike.maDifference.toFixed(4)}%`);
  console.log(`   RSI: ${spike.rsi.toFixed(2)} (>${60})`);
  console.log(`   MACD: ${spike.macd.toFixed(8)} (>0)`);
  console.log(`   MACD Signal: ${spike.macdSignal.toFixed(8)}`);
  console.log(`   MACD Histogram: ${spike.macdHistogram.toFixed(8)}`);
  console.log('');
  console.log('📊 Volume & Price Action:');
  console.log(`   Current Volume: ${spike.hacim.toLocaleString()}`);
  console.log(`   Average Volume: ${spike.ortalamHacim.toLocaleString()}`);
  console.log(`   Volume Ratio: ${spike.volumeRatio.toFixed(2)}x (>1.5x)`);
  console.log(`   Price Velocity (1m): ${spike.velocity1m.toFixed(2)}% (>1%)`);
  console.log(`   Price Gap from MA5: ${spike.priceGap.toFixed(2)}%`);
  console.log('');
  console.log('🎯 Scoring:');
  console.log(`   MA Difference Score: ${spike.scores.maDiff}/3`);
  console.log(`   RSI Score: ${spike.scores.rsi}/3`);
  console.log(`   MACD Score: ${spike.scores.macd}/3`);
  console.log(`   Volume Score: ${spike.scores.volume}/3`);
  console.log(`   Velocity Score: ${spike.scores.velocity}/3`);
  console.log(`   Total Score: ${spike.totalScore}/15`);
  console.log('');
  console.log(`🎯 Confidence: ${spike.confidence.toFixed(2)}% (${spike.guvenilirlik})`);
  console.log(`🤖 Algorithm: ${spike.algorithm}`);
  console.log('🚨 ═══════════════════════════════════════════════════════════\n');
});

detector.on('disconnected', () => {
  console.log('🔌 Disconnected from Binance. Reconnecting...');
});

detector.on('error', (error) => {
  console.error('❌ Error:', error.message);
});

// İstatistik gösterimi (her 5 dakikada)
setInterval(() => {
  const now = Date.now();
  const timeSinceLastSpike = lastSpikeTime ? (now - lastSpikeTime) / 1000 : 0;
  
  console.log('📊 ═══════════════════════════════════════════════════════════');
  console.log('📊 STATISTICS');
  console.log('📊 ═══════════════════════════════════════════════════════════');
  console.log(`🔢 Total Spikes Detected: ${totalSpikes}`);
  console.log(`⏰ Time Since Last Spike: ${timeSinceLastSpike.toFixed(0)}s`);
  console.log(`📈 Detector Status: ${detector.getStats().isConnected ? 'Connected' : 'Disconnected'}`);
  console.log(`📊 Tracked Symbols: ${detector.getStats().trackedSymbols}`);
  console.log('');
  console.log('📋 Spikes by Symbol:');
  
  if (spikesBySymbol.size === 0) {
    console.log('   No spikes detected yet...');
  } else {
    for (const [symbol, count] of spikesBySymbol.entries()) {
      console.log(`   ${symbol}: ${count} spike(s)`);
    }
  }
  
  console.log('📊 ═══════════════════════════════════════════════════════════\n');
}, 5 * 60 * 1000); // 5 dakika

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down spike detector...');
  detector.disconnect();
  
  console.log('\n📊 Final Statistics:');
  console.log(`   Total Spikes: ${totalSpikes}`);
  console.log(`   Runtime: ${process.uptime().toFixed(0)}s`);
  
  process.exit(0);
});

// Detector'ı başlat
console.log('🚀 Starting Binance Futures Spike Detector...');
console.log('📋 Optimized Criteria:');
console.log('   • MA10 ve MA5 farkı (Moving Average Crossover)');
console.log('   • RSI > 60 (Momentum Indicator)');
console.log('   • MACD > 0 (Trend Confirmation)');
console.log('   • Volume > 1.5x ortalama (Volume Explosion)');
console.log('   • Fiyat hızı 1 dakikada %1+ (Price Velocity)');
console.log('   • Güven skoru %60+ (Confidence Threshold)');
console.log('   • Cooldown süresi: 30s (Anti-spam)');
console.log('');

detector.connect().catch((error) => {
  console.error('❌ Failed to start detector:', error.message);
  process.exit(1);
});
