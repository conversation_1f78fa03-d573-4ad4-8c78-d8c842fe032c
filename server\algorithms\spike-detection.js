/**
 * Spike Detection Algorithms
 * Çeşitli spike tespit algoritmaları ve ana seçici fonksiyon
 * Hibrit çözüm: Gerçek zamanlı WebSocket + Gelişmiş algoritmaları
 */

import EventEmitter from 'events';
import WebSocket from 'ws';
import axios from 'axios';

import {
  calculateMA,
  calculateRSI,
  calculateMACD,
  calculateSlope,
  calculatePriceVelocity
} from './technical-indicators.js';

/**
 * REAL-TIME SPIKE DETECTOR CLASS
 * Gerçek zamanlı WebSocket tabanlı spike tespit sistemi
 * Mevcut gelişmiş algoritmaları kullanır
 */
export class SpikeDetector extends EventEmitter {
  constructor(options = {}) {
    super();

    // WebSocket bağlantısı
    this.socket = null;
    this.isConnected = false;

    // Veri depolama
    this.marketData = new Map(); // Symbol -> {closes, highs, lows, volumes}
    this.priceData = new Map();  // Symbol -> current price
    this.volumeData = new Map(); // Symbol -> current volume

    // Spike tracking
    this.lastSpikes = new Map();   // Symbol -> timestamp
    this.klineCache = new Map();   // Symbol -> {data, timestamp}

    // Konfigürasyon - Real-time optimized
    this.config = {
      algorithm: 'advanced',           // advanced, basic, ml
      cooldownPeriod: 10 * 1000,      // 10 saniye (daha hızlı)
      cacheTimeout: 2 * 1000,         // 2 saniye (daha hızlı)
      maxDataPoints: 50,              // Maksimum veri noktası
      minDataPoints: 30,              // Minimum veri noktası (daha hızlı başlama)
      symbols: [],                    // İzlenecek semboller (boşsa tümü)
      ...options
    };

    console.log(`🚀 SpikeDetector initialized with ${this.config.algorithm} algorithm`);
  }

  /**
   * WebSocket bağlantısını başlat - Kline stream ile + Historical data
   */
  async connect(allSymbols = null) {
    try {
      // Önce historical data yükle
      await this.loadHistoricalDataWebSocket(allSymbols);

      // Sonra real-time stream başlat
      await this.connectRealTimeStream(allSymbols);

    } catch (error) {
      console.error('❌ Connection failed:', error.message);
      setTimeout(() => this.connect(allSymbols), 5000);
    }
  }

  /**
   * Historical data'yı batch REST API ile yükle (rate limiting'i minimize etmek için)
   */
  async loadHistoricalDataWebSocket(allSymbols = null) {
    // Eğer allSymbols parametre olarak verilmişse onu kullan, yoksa config'den al
    const symbols = allSymbols || (this.config.symbols.length > 0 ? this.config.symbols : this.getDefaultSymbols());

    console.log(`📈 Loading historical data for ${symbols.length} symbols...`);
    console.log(`⚡ Using optimized batch loading to avoid rate limits`);

    // Batch'ler halinde yükle - Rate limit için daha küçük batch
    const BATCH_SIZE = 3; // Reduced to avoid Binance rate limits
    const batches = [];

    for (let i = 0; i < symbols.length; i += BATCH_SIZE) {
      batches.push(symbols.slice(i, i + BATCH_SIZE));
    }

    let loadedCount = 0;

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      // Reduced logging - only log every 5th batch
      if (i % 5 === 0) {
        console.log(`📦 Loading batch ${i + 1}/${batches.length}`);
      }

      const promises = batch.map(symbol => this.loadSymbolHistoricalDataSafe(symbol));
      const results = await Promise.allSettled(promises);

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          loadedCount++;
        } else {
          console.warn(`⚠️ Failed to load ${batch[index]}: ${result.reason?.message || 'Unknown error'}`);
        }
      });

      // Batch'ler arası bekleme (rate limiting için)
      if (i < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`✅ Historical data loading completed: ${loadedCount}/${symbols.length} symbols`);
  }

  /**
   * Tek sembol için güvenli historical data yükleme
   */
  async loadSymbolHistoricalDataSafe(symbol) {
    try {
      // Minimal REST API kullanımı - MACD için yeterli kline
      const response = await fetch(
        `https://fapi.binance.com/fapi/v1/klines?symbol=${symbol}&interval=1m&limit=${this.config.minDataPoints}`
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const klines = await response.json();

      if (klines && klines.length > 0) {
        const closes = klines.map(k => parseFloat(k[4]));
        const highs = klines.map(k => parseFloat(k[2]));
        const lows = klines.map(k => parseFloat(k[3]));
        const volumes = klines.map(k => parseFloat(k[5]));

        this.marketData.set(symbol, {
          closes,
          highs,
          lows,
          volumes,
          lastUpdate: Date.now()
        });

        // Reduced logging - only log every 20th symbol to prevent spam
        if (Math.random() < 0.05) {
          console.log(`📊 Loaded klines for ${symbol} (${klines.length}/${this.config.minDataPoints})`);
        }
        return true;
      }

      return false;
    } catch (error) {
      // Rate limiting durumunda sessizce geç
      if (error.message.includes('418') || error.message.includes('429')) {
        console.log(`⏭️ ${symbol}: Skipping due to rate limit, will collect via WebSocket`);
        return false;
      }
      throw error;
    }
  }

  /**
   * Real-time stream bağlantısını başlat
   */
  async connectRealTimeStream(allSymbols = null) {
    // Eğer allSymbols parametre olarak verilmişse onu kullan, yoksa config'den al
    const symbols = allSymbols || (this.config.symbols.length > 0 ? this.config.symbols : this.getDefaultSymbols());
    const streams = symbols.map(s => `${s.toLowerCase()}@kline_1m`).join('/');

    this.socket = new WebSocket(`wss://fstream.binance.com/stream?streams=${streams}`);

    this.socket.on('open', () => {
      this.isConnected = true;
      console.log(`📡 Connected to Binance real-time kline stream (${symbols.length} symbols)`);
      console.log(`📊 Monitoring: ${symbols.slice(0, 5).join(', ')}${symbols.length > 5 ? '...' : ''}`);
      this.emit('connected');
    });

    this.socket.on('message', async (data) => {
      await this.handleKlineMessage(data);
    });

    this.socket.on('close', () => {
      this.isConnected = false;
      console.log('🔌 Binance WebSocket disconnected, retrying in 5s...');
      this.emit('disconnected');
      setTimeout(() => this.connect(), 5000);
    });

    this.socket.on('error', (error) => {
      console.error('❌ WebSocket error:', error.message);
      this.emit('error', error);
    });
  }

  /**
   * Default symbols listesi
   */
  getDefaultSymbols() {
    return [
      'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
      'XRPUSDT', 'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'BCHUSDT',
      'UNIUSDT', 'MATICUSDT', 'AVAXUSDT', 'ATOMUSDT', 'FILUSDT'
    ];
  }

  /**
   * Kline WebSocket mesajlarını işle
   */
  async handleKlineMessage(data) {
    try {
      const message = JSON.parse(data);
      if (!message.stream || !message.data) return;

      const klineData = message.data;
      if (!klineData.k) return;

      const kline = klineData.k;
      const symbol = kline.s;

      // Real-time mode: İşle hem tamamlanmış hem de devam eden kline'ları
      // Sadece tamamlanmış kline'lar için spike detection, devam edenler için sadece price update
      const isCompleted = kline.x;

      // Sadece USDT çiftlerini işle
      if (!symbol.endsWith('USDT')) return;

      // Belirli semboller varsa sadece onları işle
      if (this.config.symbols.length > 0 && !this.config.symbols.includes(symbol)) {
        return;
      }

      // 📊 Her zaman price update emit et (real-time pozisyon takibi için)
      this.emit('price-update', {
        symbol: symbol,
        price: parseFloat(kline.c), // Close price
        high: parseFloat(kline.h),
        low: parseFloat(kline.l),
        volume: parseFloat(kline.v),
        timestamp: parseInt(kline.t)
      });

      // Spike detection sadece tamamlanmış kline'lar için
      if (isCompleted) {
        // Cooldown kontrolü
        const now = Date.now();
        const lastSpike = this.lastSpikes.get(symbol) || 0;
        if (now - lastSpike < this.config.cooldownPeriod) return;

        // Market data güncelle (kline verisi ile)
        await this.updateMarketDataFromKline(symbol, kline);

        // Spike tespiti yap
        await this.detectAndEmitSpike(symbol, kline);
      }

    } catch (error) {
      console.error('❌ Kline message handling error:', error.message);
    }
  }

  /**
   * WebSocket mesajlarını işle (eski miniTicker için backward compatibility)
   */
  async handleMessage(data) {
    try {
      const tickers = JSON.parse(data);

      for (const ticker of tickers) {
        const { s: symbol, c: close, v: volume } = ticker;

        // Sadece USDT çiftlerini işle
        if (!symbol.endsWith('USDT')) continue;

        // Belirli semboller varsa sadece onları işle
        if (this.config.symbols.length > 0 && !this.config.symbols.includes(symbol)) {
          continue;
        }

        const currentPrice = parseFloat(close);
        const currentVolume = parseFloat(volume);

        // Veri güncelle
        this.priceData.set(symbol, currentPrice);
        this.volumeData.set(symbol, currentVolume);

        // Cooldown kontrolü
        const now = Date.now();
        const lastSpike = this.lastSpikes.get(symbol) || 0;
        if (now - lastSpike < this.config.cooldownPeriod) continue;

        // Kline verilerini al ve market data güncelle
        await this.updateMarketData(symbol, ticker);

        // Spike tespiti yap
        await this.detectAndEmitSpike(symbol, ticker);
      }
    } catch (error) {
      console.error('❌ Message handling error:', error.message);
    }
  }

  /**
   * Market verilerini güncelle
   */
  async updateMarketData(symbol, ticker) {
    try {
      const klineData = await this.getKlines(symbol);
      if (!klineData || klineData.length < this.config.minDataPoints) return;

      const closes = klineData.map(k => parseFloat(k[4]));
      const highs = klineData.map(k => parseFloat(k[2]));
      const lows = klineData.map(k => parseFloat(k[3]));
      const volumes = klineData.map(k => parseFloat(k[5]));

      // Son veriyi ekle
      closes.push(parseFloat(ticker.c));
      highs.push(parseFloat(ticker.h || ticker.c));
      lows.push(parseFloat(ticker.l || ticker.c));
      volumes.push(parseFloat(ticker.v));

      // Maksimum veri noktası kontrolü
      if (closes.length > this.config.maxDataPoints) {
        closes.splice(0, closes.length - this.config.maxDataPoints);
        highs.splice(0, highs.length - this.config.maxDataPoints);
        lows.splice(0, lows.length - this.config.maxDataPoints);
        volumes.splice(0, volumes.length - this.config.maxDataPoints);
      }

      this.marketData.set(symbol, {
        closes,
        highs,
        lows,
        volumes,
        lastUpdate: Date.now()
      });
    } catch (error) {
      console.warn(`⚠️ Market data update error for ${symbol}:`, error.message);
    }
  }

  /**
   * Market verilerini kline verisi ile güncelle (HTTP API kullanmadan)
   */
  async updateMarketDataFromKline(symbol, kline) {
    try {
      let symbolData = this.marketData.get(symbol);

      if (!symbolData) {
        // İlk kez bu sembol için veri oluştur
        symbolData = {
          closes: [],
          highs: [],
          lows: [],
          volumes: [],
          lastUpdate: Date.now()
        };
        this.marketData.set(symbol, symbolData);
      }

      // Yeni kline verisini ekle
      symbolData.closes.push(parseFloat(kline.c));
      symbolData.highs.push(parseFloat(kline.h));
      symbolData.lows.push(parseFloat(kline.l));
      symbolData.volumes.push(parseFloat(kline.v));

      // Maksimum veri noktası kontrolü
      if (symbolData.closes.length > this.config.maxDataPoints) {
        symbolData.closes.splice(0, symbolData.closes.length - this.config.maxDataPoints);
        symbolData.highs.splice(0, symbolData.highs.length - this.config.maxDataPoints);
        symbolData.lows.splice(0, symbolData.lows.length - this.config.maxDataPoints);
        symbolData.volumes.splice(0, symbolData.volumes.length - this.config.maxDataPoints);
      }

      symbolData.lastUpdate = Date.now();

      // İlk veri toplama aşamasında log
      if (symbolData.closes.length <= this.config.minDataPoints) {
        console.log(`📊 ${symbol}: Collecting data ${symbolData.closes.length}/${this.config.minDataPoints}`);
      }

    } catch (error) {
      console.warn(`⚠️ Market data update error for ${symbol}:`, error.message);
    }
  }

  /**
   * Spike tespiti yap ve emit et
   */
  async detectAndEmitSpike(symbol, klineOrTicker) {
    try {
      const symbolData = this.marketData.get(symbol);
      if (!symbolData || symbolData.closes.length < this.config.minDataPoints) {
        return; // Yeterli veri yok
      }

      // Kline veya ticker formatını normalize et
      const tickerData = this.normalizeTickerData(klineOrTicker);

      const spike = detectSpike(symbol, tickerData, this.marketData, this.config);

      if (spike && validateSpike(spike, this.marketData)) {
        const now = Date.now();
        this.lastSpikes.set(symbol, now);

        console.log(`🚨 SPIKE DETECTED: ${symbol} at ${spike.fiyat} (${spike.confidence}%)`);

        this.emit('spike', {
          ...spike,
          algorithm: this.config.algorithm,
          timestamp: now
        });
      }
    } catch (error) {
      console.error(`❌ Spike detection error for ${symbol}:`, error.message);
    }
  }

  /**
   * Kline veya ticker verisini normalize et
   */
  normalizeTickerData(data) {
    // Eğer kline verisi ise (k property'si var)
    if (data.s && data.c && data.h && data.l && data.v) {
      return {
        s: data.s,  // symbol
        c: data.c,  // close
        h: data.h,  // high
        l: data.l,  // low
        v: data.v   // volume
      };
    }

    // Eğer ticker verisi ise
    return data;
  }

  /**
   * Kline verilerini al (cache ile) - DEPRECATED: WebSocket kullanıyoruz
   */
  async getKlines(symbol) {
    console.warn(`⚠️ getKlines deprecated for ${symbol} - using WebSocket kline stream instead`);
    return null;
  }

  /**
   * Bağlantıyı kapat
   */
  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.isConnected = false;
      console.log('🔌 SpikeDetector disconnected');
    }
  }

  /**
   * Konfigürasyonu güncelle
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Configuration updated:', newConfig);
  }

  /**
   * İstatistikleri al
   */
  getStats() {
    return {
      isConnected: this.isConnected,
      trackedSymbols: this.marketData.size,
      cacheSize: this.klineCache.size,
      lastSpikes: this.lastSpikes.size,
      algorithm: this.config.algorithm
    };
  }
}

/**
 * ADVANCED SPIKE DETECTION ALGORITHM
 * Gelişmiş spike tespit algoritması - Kullanıcı kriterlerine göre optimize edilmiş
 * Kriterler: MA10-MA5 farkı, RSI>60, MACD>0, volume>1.5x, fiyat hızı %1+, güven skoru %60+
 */
export const detectSpikeAdvanced = (symbol, klineData, marketData) => {
  const symbolData = marketData.get(symbol);
  if (!symbolData) {
    console.log(`❌ Symbol data not found: ${symbol}`);
    return null;
  }

  const { closes, volumes } = symbolData;
  if (closes.length < 30) {
    console.log(`📊 ${symbol}: Not enough data (${closes.length}/30)`);
    return null;
  }

  const currentPrice = parseFloat(klineData.c);
  const currentVolume = parseFloat(klineData.v);

  // Calculate indicators
  const ma5 = calculateMA(closes, 5);
  const ma10 = calculateMA(closes, 10);
  const rsi = calculateRSI(closes);
  const macd = calculateMACD(closes);

  // Data validation
  if (!ma5 || !ma10 || currentVolume <= 0 || !rsi || !macd) {
    console.log(`❌ ${symbol}: Invalid data - MA5:${ma5}, MA10:${ma10}, Volume:${currentVolume}, RSI:${rsi}, MACD:${macd}`);
    return null;
  }

  // 1. MA10 ve MA5 FARKI KRİTERİ
  const maDifference = ((ma5 - ma10) / ma10) * 100;
  const maDiffScore = maDifference > 0.5 ? 3 : maDifference > 0.2 ? 2 : maDifference > 0 ? 1 : 0;

  // 2. RSI > 65 KRİTERİ - YÜKSEK SPIKE (SHORT SİNYALİ)
  if (rsi <= 65) {
    // Reduced logging - only log occasionally to prevent spam
    if (Math.random() < 0.01) {
      console.log(`❌ ${symbol}: RSI below spike threshold (${rsi.toFixed(2)} <= 65)`);
    }
    return null;
  }
  const rsiScore = rsi > 80 ? 3 : rsi > 75 ? 2 : rsi > 70 ? 1.5 : 1;

  // 3. MACD > 0 KRİTERİ (Zorunlu)
  if (!macd.macd || macd.macd <= 0) {
    console.log(`❌ ${symbol}: MACD kritik seviyenin altında (${macd.macd} <= 0)`);
    return null;
  }
  const macdScore = macd.macd > macd.signal && macd.histogram > 0 ? 3 : macd.macd > macd.signal ? 2 : 1;

  // 4. VOLUME > 1.5x ORTALAMA KRİTERİ (Zorunlu)
  const avgVolume = calculateMA(volumes, 20);
  const volumeRatio = currentVolume / avgVolume;
  if (volumeRatio < 1.5) {
    console.log(`❌ ${symbol}: Volume kritik seviyenin altında (${volumeRatio.toFixed(2)}x < 1.5x)`);
    return null;
  }
  const volumeScore = volumeRatio > 3 ? 3 : volumeRatio > 2 ? 2 : 1;

  // 5. FİYAT HIZI 1 DAKİKADA %0.5+ KRİTERİ (Zorunlu)
  const velocity1m = calculatePriceVelocity(closes, 1);
  if (!velocity1m || velocity1m < 0.5) {
    console.log(`❌ ${symbol}: Fiyat hızı kritik seviyenin altında (${velocity1m?.toFixed(2)}% < 0.5%)`);
    return null;
  }
  const velocityScore = velocity1m > 3 ? 3 : velocity1m > 2 ? 2 : 1;

  // TOTAL SCORE CALCULATION
  const totalScore = maDiffScore + rsiScore + macdScore + volumeScore + velocityScore;
  const maxScore = 15; // 3+3+3+3+3
  const confidence = Math.min(100, (totalScore / maxScore) * 100);

  // GÜVEN SKORU %60+ KRİTERİ (Zorunlu)
  if (confidence < 60) {
    console.log(`❌ ${symbol}: Güven skoru kritik seviyenin altında (${confidence.toFixed(2)}% < 60%)`);
    return null;
  }

  // Calculate additional metrics
  const slope = calculateSlope(closes, 10);
  const priceGap = ((currentPrice - ma5) / ma5) * 100;

  // 🎯 Trade Direction - Yüksek RSI (>70) = SHORT sinyal
  const tradeDirection = 'SHORT'; // RSI > 70 olduğu için SHORT
  const spikeType = 'YUKSEK'; // Yüksek spike tespit edildi

  return {
    id: `${symbol}_${Date.now()}`,
    sembol: symbol,
    zaman: Date.now(),
    fiyat: currentPrice,
    ma5: parseFloat(ma5.toFixed(8)),
    ma10: parseFloat(ma10.toFixed(8)),
    maDifference: parseFloat(maDifference.toFixed(4)),
    egim: parseFloat((slope || 0).toFixed(8)),
    hacim: currentVolume,
    ortalamHacim: parseFloat(avgVolume.toFixed(2)),
    rsi: parseFloat(rsi.toFixed(2)),
    macd: parseFloat(macd.macd.toFixed(8)),
    macdSignal: parseFloat(macd.signal.toFixed(8)),
    macdHistogram: parseFloat(macd.histogram.toFixed(8)),
    priceGap: parseFloat(priceGap.toFixed(2)),
    velocity1m: parseFloat(velocity1m.toFixed(2)),
    volumeRatio: parseFloat(volumeRatio.toFixed(2)),
    confidence: parseFloat(confidence.toFixed(2)),
    totalScore,
    scores: {
      maDiff: maDiffScore,
      rsi: rsiScore,
      macd: macdScore,
      volume: volumeScore,
      velocity: velocityScore
    },
    guvenilirlik: confidence > 80 ? 'YUKSEK' : confidence > 60 ? 'ORTA' : 'DUSUK',
    durum: 'TESPIT_EDILDI',
    algorithm: 'ADVANCED_OPTIMIZED',
    // 🎯 YENİ: Trade bilgileri
    tradeDirection: tradeDirection,
    spikeType: spikeType,
    side: tradeDirection // Binance API için
  };
};

/**
 * BASIC SPIKE DETECTION ALGORITHM
 * Basit spike tespit algoritması - hızlı ve basit
 */
export const detectSpikeBasic = (symbol, klineData, marketData) => {
  const symbolData = marketData.get(symbol);
  if (!symbolData) return null;

  const { closes } = symbolData;
  if (closes.length < 11) return null;

  const currentPrice = parseFloat(klineData.c);
  const ma10 = calculateMA(closes, 10);
  const slope = calculateSlope(closes, 10);

  if (!ma10 || slope === null) return null;

  // Basit kriter: %0.5 sapma + pozitif slope
  const deviation = ((currentPrice - ma10) / ma10) * 100;
  const isSpike = deviation > 0.5 && slope > 0.01;

  if (isSpike) {
    return {
      id: `${symbol}_${Date.now()}`,
      sembol: symbol,
      zaman: Date.now(),
      fiyat: currentPrice,
      ma5: 0, // Basit algoritmada hesaplanmıyor
      ma10: parseFloat(ma10.toFixed(8)),
      egim: parseFloat(slope.toFixed(8)),
      hacim: parseFloat(klineData.v || 0),
      ortalamHacim: 1,
      priceGap: parseFloat(deviation.toFixed(2)),
      confidence: Math.min(100, deviation * 20),
      guvenilirlik: deviation > 1 ? 'ORTA' : 'DUSUK',
      durum: 'TESPIT_EDILDI'
    };
  }

  return null;
};

/**
 * MACHINE LEARNING SPIKE DETECTION (Gelecek için)
 * Makine öğrenmesi tabanlı spike tespit algoritması
 */
export const detectSpikeML = (symbol, klineData, marketData) => {
  // TODO: Implement ML-based spike detection
  // Features: price patterns, volume patterns, market sentiment, etc.
  console.log(`🤖 ML Spike Detection for ${symbol} - Coming Soon!`);
  return null;
};

/**
 * ALGORITHM SELECTOR
 * Kullanıcı ayarlarına göre algoritma seçici
 */
export const detectSpike = (symbol, klineData, marketData, userSettings = {}) => {
  const algorithm = userSettings.algorithm || 'advanced';
  
  switch (algorithm) {
    case 'basic':
      return detectSpikeBasic(symbol, klineData, marketData);
    
    case 'advanced':
      return detectSpikeAdvanced(symbol, klineData, marketData);
    
    case 'ml':
      return detectSpikeML(symbol, klineData, marketData);
    
    default:
      console.log(`⚠️ Unknown algorithm: ${algorithm}, using advanced`);
      return detectSpikeAdvanced(symbol, klineData, marketData);
  }
};

/**
 * SPIKE VALIDATION
 * Spike'ın geçerliliğini kontrol eder
 */
export const validateSpike = (spike, marketData) => {
  if (!spike) return false;
  
  // Minimum confidence kontrolü
  if (spike.confidence < 50) return false;
  
  // Fiyat kontrolü
  if (spike.fiyat <= 0) return false;
  
  // Hacim kontrolü (advanced algoritma için)
  if (spike.hacim <= 0 && spike.confidence > 70) return false;
  
  return true;
};

/**
 * SPIKE LOGGING
 * Spike detection ile birlikte detaylı loglama
 */
export const detectSpikeWithLogging = (symbol, klineData, marketData, userSettings = {}) => {
  const spike = detectSpike(symbol, klineData, marketData, userSettings);
  
  if (spike && validateSpike(spike, marketData)) {
    console.log(`🚨 SPIKE DETECTED: ${symbol} at ${spike.fiyat}`);
    console.log(`📊 SPIKE DATA:`, {
      confidence: spike.confidence,
      algorithm: userSettings.algorithm || 'advanced',
      ma5: spike.ma5,
      ma10: spike.ma10,
      volume: spike.hacim,
      avgVolume: spike.ortalamHacim,
      trend: spike.trend,
      scores: spike.scores
    });
    
    return spike;
  }
  
  return null;
};

/**
 * CONVENIENCE FUNCTION
 * SpikeDetector instance oluşturmak için kolaylık fonksiyonu
 */
export const createSpikeDetector = (options = {}) => {
  return new SpikeDetector(options);
};

/**
 * USAGE EXAMPLE
 *
 * // Basit kullanım
 * const detector = new SpikeDetector({
 *   algorithm: 'advanced',
 *   symbols: ['BTCUSDT', 'ETHUSDT'], // Boş bırakılırsa tüm USDT çiftleri
 *   cooldownPeriod: 30000
 * });
 *
 * detector.on('spike', (spike) => {
 *   console.log('🚨 Spike detected:', spike);
 * });
 *
 * detector.on('connected', () => {
 *   console.log('✅ Connected to Binance');
 * });
 *
 * detector.connect();
 *
 * // Konfigürasyon güncelleme
 * detector.updateConfig({ algorithm: 'basic' });
 *
 * // İstatistikler
 * console.log(detector.getStats());
 *
 * // Bağlantıyı kapat
 * detector.disconnect();
 */
