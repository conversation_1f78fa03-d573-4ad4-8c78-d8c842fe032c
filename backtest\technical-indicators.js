export function calculateMA(closes, period) {
  if (closes.length < period) return null;
  const slice = closes.slice(-period);
  const sum = slice.reduce((a, b) => a + b, 0);
  return sum / period;
}

export function calculateSlope(closes, period) {
  if (closes.length < period) return null;
  const slice = closes.slice(-period);
  const x = [...Array(period).keys()];
  const avgX = x.reduce((a, b) => a + b, 0) / period;
  const avgY = slice.reduce((a, b) => a + b, 0) / period;

  const numerator = x.map((xi, i) => (xi - avgX) * (slice[i] - avgY)).reduce((a, b) => a + b, 0);
  const denominator = x.map(xi => (xi - avgX) ** 2).reduce((a, b) => a + b, 0);
  return numerator / denominator;
}
