import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { TrendingUp, TrendingDown, Settings, Zap, Target, Shield, Play, Pause, BarChart3 } from 'lucide-react';
import { TicareKonfigurasyonu } from '@/types/turkish-trading';

interface TicaretPaneliProps {
  config: TicareKonfigurasyonu;
  onConfigChange: (config: Partial<TicareKonfigurasyonu>) => void;
  isConnected: boolean;
  onTradingStart: () => void;
  onTradingStop: () => void;
}

export const TradingPanel: React.FC<TicaretPaneliProps> = ({
  config,
  onConfigChange,
  isConnected,
  onTradingStart,
  onTradingStop
}) => {




  // Kaldıraç 20x sabit

  const tpDegistir = async (value: string) => {
    const tp = parseFloat(value);
    if (!isNaN(tp) && tp > 0) {
      onConfigChange({ tpYuzdesi: tp });

      // API'ye gönder
      try {
        await fetch('/api/dashboard/config', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ tpYuzdesi: tp })
        });
      } catch (error) {
        console.error('TP config update error:', error);
      }
    }
  };

  const slDegistir = async (value: string) => {
    const sl = parseFloat(value);
    if (!isNaN(sl) && sl > 0) {
      onConfigChange({ slYuzdesi: sl });

      // API'ye gönder
      try {
        await fetch('/api/dashboard/config', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ slYuzdesi: sl })
        });
      } catch (error) {
        console.error('SL config update error:', error);
      }
    }
  };

  const manuelTicaret = () => {
    if (config.otomatikTicaret) {
      alert('Otomatik ticaret açıkken manuel işlem yapılamaz!');
      return;
    }
    console.log(`Manuel SHORT işlem: ${secilenSembol} - ${config.islemMiktari} USDT`);
  };

  return (
    <Card className="w-full max-w-md bg-gradient-to-br from-primary/5 to-position-bg border-primary/30 shadow-2xl hover:shadow-primary/30 transition-shadow duration-300">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-primary drop-shadow">
            <Zap className="h-5 w-5" />
            İşlem Paneli
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge
              variant={isConnected ? "default" : "destructive"}
              className={isConnected ? "animate-pulse-profit" : "animate-pulse-loss"}
            >
              {isConnected ? "Bağlı" : "Bağlantısız"}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* İşlem Başlatıldığında Uyarı */}
        {config.islemBaslatildi && (
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
            <div className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
              <Shield className="h-4 w-4" />
              <span className="text-sm font-medium">
                İşlem aktif! Güvenlik için ayarlar değiştirilemez.
              </span>
            </div>
          </div>
        )}

        {/* Spike sinyali alınan çift için otomatik işlem yapılacak */}

        {/* İşlem Miktarı */}
        <div className="space-y-2">
          <Label htmlFor="size">İşlem Miktarı (USDT)</Label>
          <Input
            id="size"
            type="number"
            value={config.islemMiktari}
            onChange={async (e) => {
              const amount = parseFloat(e.target.value) || 0.5;
              onConfigChange({ islemMiktari: amount });

              // API'ye gönder
              try {
                await fetch('/api/dashboard/config', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ islemMiktari: amount })
                });
              } catch (error) {
                console.error('Trade amount config update error:', error);
              }
            }}
            disabled={config.islemBaslatildi}
            className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
              config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            step="0.1"
            min="0.5"
            max="1000"
          />
        </div>

        {/* Kaldıraç - Sabit 20x */}
        <div className="space-y-3">
          <div className="flex justify-between">
            <Label>Kaldıraç</Label>
            <Badge variant="outline" className="border-primary/30 text-primary">
              20x (Sabit)
            </Badge>
          </div>
        </div>

        {/* MA Ayarı */}
        <div className="space-y-2">
          <Label className="flex items-center gap-1">
            <BarChart3 className="h-3 w-3" />
            MA Periyodu
          </Label>
          <Input
            type="number"
            value={config.maPeriyodu}
            onChange={async (e) => {
              const period = parseInt(e.target.value) || 10;
              onConfigChange({ maPeriyodu: period });

              // API'ye gönder
              try {
                await fetch('/api/dashboard/config', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ maPeriyodu: period })
                });
              } catch (error) {
                console.error('MA period config update error:', error);
              }
            }}
            disabled={config.islemBaslatildi}
            className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
              config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            step="1"
            min="5"
            max="50"
          />
          <p className="text-xs text-muted-foreground">
            Hareketli ortalama periyodu (5-50)
          </p>
        </div>

        {/* Hacim Çarpanı */}
        <div className="space-y-2">
          <Label className="flex items-center gap-1">
            <BarChart3 className="h-3 w-3" />
            Hacim Çarpanı
          </Label>
          <Input
            type="number"
            value={config.hacimCarpani}
            onChange={async (e) => {
              const multiplier = parseFloat(e.target.value) || 0.75; // Yarıya indirildi: 1.5 -> 0.75
              onConfigChange({ hacimCarpani: multiplier });

              // API'ye gönder
              try {
                await fetch('/api/dashboard/config', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ hacimCarpani: multiplier })
                });
              } catch (error) {
                console.error('Volume multiplier config update error:', error);
              }
            }}
            disabled={config.islemBaslatildi}
            className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
              config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            step="0.1"
            min="1.0"
            max="5.0"
          />
          <p className="text-xs text-muted-foreground">
            Ortalama hacmin kaç katı olmalı (1.0-5.0x)
          </p>
        </div>

        {/* Spike Eşiği */}
        <div className="space-y-2">
          <Label className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            Spike Eşiği %
          </Label>
          <Input
            type="number"
            value={config.spikeEsigi}
            onChange={async (e) => {
              const threshold = parseFloat(e.target.value) || 0.25; // Yarıya indirildi: 0.5 -> 0.25
              onConfigChange({ spikeEsigi: threshold });

              // API'ye gönder
              try {
                await fetch('/api/dashboard/config', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ spikeEsigi: threshold })
                });
              } catch (error) {
                console.error('Spike threshold config update error:', error);
              }
            }}
            disabled={config.islemBaslatildi}
            className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
              config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            step="0.1"
            min="0.1"
            max="2.0"
          />
          <p className="text-xs text-muted-foreground">
            MA10 üzerinde minimum yükseliş yüzdesi (0.1-2.0%)
          </p>
        </div>

        {/* TP/SL Ayarları */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-1 text-profit">
              <Target className="h-3 w-3" />
              Kar Al %
            </Label>
            <Input
              type="number"
              value={config.tpYuzdesi}
              onChange={(e) => tpDegistir(e.target.value)}
              disabled={config.islemBaslatildi}
              className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
                config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              step="0.1"
              min="0.1"
              max="10"
            />
          </div>
          <div className="space-y-2">
            <Label className="flex items-center gap-1 text-loss">
              <Shield className="h-3 w-3" />
              Zarar Durdur %
            </Label>
            <Input
              type="number"
              value={config.slYuzdesi}
              onChange={(e) => slDegistir(e.target.value)}
              disabled={config.islemBaslatildi}
              className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
                config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              step="0.1"
              min="0.1"
              max="10"
            />
          </div>
        </div>

        {/* Pozisyon ve İşlem Limitleri */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              Maks Pozisyon
            </Label>
            <Input
              type="number"
              value={config.maksPozisyon}
              onChange={async (e) => {
                const maxPos = parseInt(e.target.value) || 3;
                onConfigChange({ maksPozisyon: maxPos });

                // API'ye gönder
                try {
                  await fetch('/api/dashboard/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ maksPozisyon: maxPos })
                  });
                } catch (error) {
                  console.error('Max position config update error:', error);
                }
              }}
              disabled={config.islemBaslatildi}
              className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
                config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              step="1"
              min="1"
              max="10"
            />
          </div>
          <div className="space-y-2">
            <Label className="flex items-center gap-1">
              <Shield className="h-3 w-3" />
              Günlük İşlem
            </Label>
            <Input
              type="number"
              value={config.maksGunlukIslem}
              onChange={async (e) => {
                const dailyTrades = parseInt(e.target.value) || 20;
                onConfigChange({ maksGunlukIslem: dailyTrades });

                // API'ye gönder
                try {
                  await fetch('/api/dashboard/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ maksGunlukIslem: dailyTrades })
                  });
                } catch (error) {
                  console.error('Daily trades config update error:', error);
                }
              }}
              disabled={config.islemBaslatildi}
              className={`bg-muted/30 border-primary/30 focus:border-primary focus:ring-2 focus:ring-primary/40 shadow-md ${
                config.islemBaslatildi ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              step="1"
              min="1"
              max="300"
            />
          </div>
        </div>
        <Separator className="bg-border/30" />

        {/* İşlem Kontrolü */}
        <div className="space-y-3">
          <Button
            onClick={config.islemBaslatildi ? onTradingStop : onTradingStart}
            disabled={!isConnected}
            className={`w-full font-medium transition-all duration-200 ${config.islemBaslatildi
                ? 'bg-gradient-to-r from-loss to-loss/80 hover:from-loss/90 hover:to-loss text-loss-foreground hover:shadow-lg hover:shadow-loss/20'
                : 'bg-gradient-to-r from-profit to-profit/80 hover:from-profit/90 hover:to-profit text-profit-foreground hover:shadow-lg hover:shadow-profit/20'
              }`}
          >
            {config.islemBaslatildi ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                İşlemi Durdur
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                İşlemi Başlat
              </>
            )}
          </Button>

        </div>
      </CardContent>
    </Card>
  );
};