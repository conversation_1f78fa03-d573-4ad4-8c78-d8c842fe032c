import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Target, Shield, X, Clock, TrendingDown, RefreshCw } from 'lucide-react';
import { AcikEmir } from '@/types/turkish-trading';
import {
  formatPrice,
  formatCurrency,
  formatQuantity,
  formatTimeDuration
} from '@/utils/priceFormatter';

interface AcikEmirlerProps {
  emirler: AcikEmir[];
  onEmirIptal: (emirId: string) => void;
  onRefreshOrders: () => void;
}

export const AcikEmirler: React.FC<AcikEmirlerProps> = ({
  emirler,
  onEmirIptal,
  onRefreshOrders
}) => {
  // Removed old formatPrice and formatDate - using utility functions now

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Calculate order value with precise formatting
  const calculateOrderValue = (emir: AcikEmir): string => {
    const value = emir.fiyat * emir.miktar;
    return formatCurrency(value);
  };

  const getEmirIcon = (tip: string) => {
    switch (tip) {
      case 'TAKE_PROFIT':
        return <Target className="h-3 w-3 text-profit" />;
      case 'STOP_LOSS':
        return <Shield className="h-3 w-3 text-loss" />;
      default:
        return <TrendingDown className="h-3 w-3 text-muted-foreground" />;
    }
  };

  const getEmirTipAdi = (tip: string): string => {
    switch (tip) {
      case 'LIMIT':
        return 'Limit';
      case 'TAKE_PROFIT':
        return 'Kar Al';
      case 'STOP_LOSS':
        return 'Zarar Durdur';
      default:
        return tip;
    }
  };

  const getDurumBadgeVariant = (durum: string) => {
    switch (durum) {
      case 'BEKLIYOR':
        return 'secondary';
      case 'KISMI_DOLU':
        return 'outline';
      case 'DOLU':
        return 'default';
      case 'IPTAL':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getDurumAdi = (durum: string): string => {
    switch (durum) {
      case 'BEKLIYOR':
        return 'Bekliyor';
      case 'KISMI_DOLU':
        return 'Kısmi Dolu';
      case 'DOLU':
        return 'Dolu';
      case 'IPTAL':
        return 'İptal';
      default:
        return durum;
    }
  };

  return (
    <Card className="w-full bg-gradient-to-br from-card to-position-bg border-border/50 shadow-lg">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-primary">
            <Clock className="h-5 w-5" />
            Açık Emirler
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefreshOrders}
              className="h-6 px-2 text-xs hover:bg-primary/10 hover:text-primary hover:border-primary/30"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Yenile
            </Button>
            <Badge variant="outline" className="border-primary/30 text-primary">
              {emirler.length} Emir
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {emirler.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
            <p className="text-muted-foreground">Açık emir bulunmuyor</p>
          </div>
        ) : (
          <div className="space-y-3">
            {emirler.map((emir, index) => (
              <div key={emir.id}>
                <div className="p-4 bg-muted/20 rounded-lg border border-border/20 space-y-3">
                  {/* Üst Satır - Sembol ve Durum */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getEmirIcon(emir.tip)}
                      <span className="font-medium text-sm">{emir.sembol}</span>
                      <Badge variant="outline" className="text-xs">
                        {getEmirTipAdi(emir.tip)}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getDurumBadgeVariant(emir.durum)} className="text-xs">
                        {getDurumAdi(emir.durum)}
                      </Badge>
                      {emir.durum === 'BEKLIYOR' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEmirIptal(emir.id)}
                          className="h-6 w-6 p-0 text-loss hover:text-loss hover:bg-loss/10"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Orta Satır - Fiyat ve Miktar - Enhanced Precision */}
                  <div className="grid grid-cols-3 gap-3 text-sm">
                    <div>
                      <span className="text-muted-foreground">Fiyat:</span>
                      <div className="font-mono font-medium">
                        ${formatPrice(emir.fiyat, emir.sembol)}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Miktar:</span>
                      <div className="font-mono font-medium">
                        {formatQuantity(emir.miktar, emir.sembol)}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Değer:</span>
                      <div className="font-mono font-medium text-xs">
                        {calculateOrderValue(emir)}
                      </div>
                    </div>
                  </div>

                  {/* Alt Satır - Zaman ve PnL */}
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">
                      {formatDate(emir.olusturmZamani)}
                    </span>
                    {emir.pnlYuzdesi !== undefined && (
                      <span className={emir.pnlYuzdesi >= 0 ? 'text-profit' : 'text-loss'}>
                        {emir.pnlYuzdesi >= 0 ? '+' : ''}{emir.pnlYuzdesi.toFixed(2)}%
                      </span>
                    )}
                  </div>
                </div>

                {index < emirler.length - 1 && (
                  <Separator className="my-3 bg-border/30" />
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};