@tailwind base;
@tailwind components;
@tailwind utilities;

/* ParaBOT - Kripto Trading Bot Design System */

@layer base {
  :root {
    /* ParaBOT - Modern Blue Theme */
    --background: 210 60% 98%;
    --foreground: 220 20% 15%;

    --card: 210 100% 99%;
    --card-foreground: 220 20% 15%;

    --popover: 210 100% 99%;
    --popover-foreground: 220 20% 15%;

    /* Primary Brand Colors - Modern Blue */
    --primary: 213 100% 54%;
    /* <PERSON>ha canlı mavi */
    --primary-foreground: 0 0% 100%;
    --primary-glow: 213 100% 64%;

    --secondary: 210 80% 92%;
    --secondary-foreground: 220 20% 15%;

    --muted: 210 60% 95%;
    --muted-foreground: 215 20% 35%;

    --accent: 210 100% 60%;
    --accent-foreground: 0 0% 100%;

    /* Trading Colors - Bold & Vibrant */
    --profit: 142 76% 36%;
    --profit-foreground: 0 0% 100%;
    --loss: 0 84% 60%;
    --loss-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 213 40% 85%;
    --input: 213 40% 94%;
    --ring: 213 100% 54%;

    /* Trading Specific Colors - Bold Theme */
    --chart-green: 142 76% 36%;
    --chart-red: 0 84% 60%;
    --spike-alert: 45 96% 58%;
    --position-bg: 210 80% 97%;

    /* Modern Blue Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(213, 100%, 54%), hsl(213, 100%, 64%));
    --gradient-profit: linear-gradient(135deg, hsl(142, 76%, 36%), hsl(142, 76%, 46%));
    --gradient-loss: linear-gradient(135deg, hsl(0, 84%, 60%), hsl(0, 84%, 70%));
    --gradient-bg: linear-gradient(180deg, hsl(210, 60%, 98%), hsl(213, 80%, 95%));
    --gradient-card: linear-gradient(145deg, hsl(210, 100%, 99%), hsl(213, 80%, 96%));

    /* Modern Shadows & Effects */
    --shadow-glow: 0 0 30px hsl(213 100% 54% / 0.25);
    --shadow-profit: 0 2px 12px hsl(142 76% 36% / 0.20);
    --shadow-loss: 0 2px 12px hsl(0 84% 60% / 0.20);
    --shadow-card: 0 4px 20px hsl(213 40% 15% / 0.10);

    --radius: 0.75rem;

    --sidebar-background: 210 80% 97%;
    --sidebar-foreground: 240 20% 15%;
    --sidebar-primary: 213 100% 54%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 80% 92%;
    --sidebar-accent-foreground: 240 20% 15%;
    --sidebar-border: 213 40% 80%;
    --sidebar-ring: 213 100% 54%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 213 100% 54%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 213 100% 54%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 213 100% 54%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 213 100% 54%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Trading P&L Colors - Enhanced */
  .text-profit {
    color: hsl(var(--profit)) !important;
  }

  .text-loss {
    color: hsl(var(--loss)) !important;
  }

  .bg-profit {
    background-color: hsl(var(--profit)) !important;
  }

  .bg-loss {
    background-color: hsl(var(--loss)) !important;
  }

  .bg-profit-light {
    background-color: hsl(var(--profit) / 0.1) !important;
  }

  .bg-loss-light {
    background-color: hsl(var(--loss) / 0.1) !important;
  }

  .border-profit {
    border-color: hsl(var(--profit)) !important;
  }

  .border-loss {
    border-color: hsl(var(--loss)) !important;
  }

  /* Specific PnL styling */
  .pnl-positive {
    color: #16a34a !important;
    font-weight: 600;
  }

  .pnl-negative {
    color: #dc2626 !important;
    font-weight: 600;
  }

  .pnl-bg-positive {
    background-color: rgba(22, 163, 74, 0.1) !important;
    border-color: rgba(22, 163, 74, 0.3) !important;
  }

  .pnl-bg-negative {
    background-color: rgba(220, 38, 38, 0.1) !important;
    border-color: rgba(220, 38, 38, 0.3) !important;
  }

  /* Position side colors */
  .side-long {
    color: #16a34a !important;
  }

  .side-short {
    color: #dc2626 !important;
  }
}