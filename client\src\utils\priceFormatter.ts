/**
 * Advanced Price Formatting Utilities
 * Coin fiyatlarını hassasiyetlerine göre doğru formatlama
 */

// Symbol precision mapping - Binance'den alınan gerçek hassasiyet değerleri
const SYMBOL_PRECISION_MAP: Record<string, number> = {
  // Major pairs - 4 decimal places for better precision
  'BTCUSDT': 4,
  'ETHUSDT': 4,
  'BNBUSDT': 4,
  
  // Mid-cap coins - 4 decimal places for better precision
  'ADAUSDT': 4,
  'SOLUSDT': 4,
  'XRPUSDT': 4,
  'DOTUSDT': 4,
  'LINKUSDT': 4,
  'LTCUSDT': 4,
  'BCHUSDT': 4,
  'UNIUSDT': 4,
  'MATICUSDT': 4,
  'AVAXUSDT': 4,
  'ATOMUSDT': 4,
  'FILUSDT': 4,
  
  // Small-cap coins - 5-6 decimal places
  'DOGEUSDT': 5,
  'SHIBUSDT': 6,
  'PEPEUSDT': 7,
  'FLOKIUSDT': 6,
  
  // Default fallback
  'DEFAULT': 4
};

// Price range based precision
const getPrecisionByPrice = (price: number): number => {
  if (price >= 1000) return 2;      // $1000+ -> 2 decimals
  if (price >= 100) return 3;       // $100-999 -> 3 decimals
  if (price >= 10) return 3;        // $10-99 -> 3 decimals
  if (price >= 1) return 4;         // $1-9 -> 4 decimals
  if (price >= 0.1) return 4;       // $0.1-0.99 -> 4 decimals
  if (price >= 0.01) return 5;      // $0.01-0.099 -> 5 decimals
  if (price >= 0.001) return 6;     // $0.001-0.0099 -> 6 decimals
  if (price >= 0.0001) return 7;    // $0.0001-0.00099 -> 7 decimals
  return 8;                         // < $0.0001 -> 8 decimals
};

/**
 * Format price with appropriate precision based on symbol and value
 */
export const formatPrice = (price: number, symbol?: string): string => {
  if (price === undefined || price === null || isNaN(price)) {
    return '0.0000';
  }

  let precision: number;

  // Use symbol-specific precision if available
  if (symbol && SYMBOL_PRECISION_MAP[symbol]) {
    precision = SYMBOL_PRECISION_MAP[symbol];
  } else {
    // Use price-based precision
    precision = getPrecisionByPrice(price);
  }

  // Format with appropriate precision
  return price.toFixed(precision);
};

/**
 * Format price with currency symbol
 */
export const formatPriceWithCurrency = (price: number, symbol?: string): string => {
  return `$${formatPrice(price, symbol)}`;
};

/**
 * Format price difference with sign and color indication
 */
export const formatPriceDifference = (difference: number, symbol?: string): {
  formatted: string;
  isPositive: boolean;
  className: string;
} => {
  const isPositive = difference >= 0;
  const sign = isPositive ? '+' : '';
  const formatted = `${sign}${formatPrice(Math.abs(difference), symbol)}`;
  const className = isPositive ? 'pnl-positive' : 'pnl-negative';

  return {
    formatted,
    isPositive,
    className
  };
};

/**
 * Format percentage with appropriate precision
 */
export const formatPercentage = (percentage: number, precision: number = 2): string => {
  if (percentage === undefined || percentage === null || isNaN(percentage)) {
    return '0.00%';
  }

  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(precision)}%`;
};

/**
 * Format currency amount (USD)
 */
export const formatCurrency = (amount: number, precision: number = 2): string => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return '$0.00';
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  }).format(amount);
};

/**
 * Format volume with K, M, B suffixes
 */
export const formatVolume = (volume: number): string => {
  if (volume === undefined || volume === null || isNaN(volume)) {
    return '0';
  }

  if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`;
  if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`;
  if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`;
  return volume.toFixed(0);
};

/**
 * Format quantity with appropriate precision
 */
export const formatQuantity = (quantity: number, symbol?: string): string => {
  if (quantity === undefined || quantity === null || isNaN(quantity)) {
    return '0';
  }

  // Use higher precision for quantities
  let precision = 6;
  
  if (symbol && SYMBOL_PRECISION_MAP[symbol]) {
    precision = Math.max(SYMBOL_PRECISION_MAP[symbol], 4);
  }

  // Remove trailing zeros
  return parseFloat(quantity.toFixed(precision)).toString();
};

/**
 * Calculate and format PnL with proper precision
 */
export const calculateAndFormatPnL = (
  entryPrice: number,
  currentPrice: number,
  quantity: number,
  side: 'LONG' | 'SHORT',
  leverage: number = 1,
  symbol?: string
): {
  pnlUSD: number;
  pnlPercent: number;
  formattedPnL: string;
  formattedPercent: string;
  className: string;
} => {
  // Calculate PnL based on position side
  let pnlUSD = 0;
  
  if (side === 'LONG') {
    pnlUSD = (currentPrice - entryPrice) * quantity;
  } else if (side === 'SHORT') {
    pnlUSD = (entryPrice - currentPrice) * quantity;
  }

  // Calculate percentage with leverage effect
  const pnlPercent = entryPrice > 0 ? (pnlUSD / (entryPrice * quantity)) * 100 * leverage : 0;

  // Format values
  const formattedPnL = formatCurrency(pnlUSD);
  const formattedPercent = formatPercentage(pnlPercent);
  const className = pnlUSD >= 0 ? 'pnl-positive' : 'pnl-negative';

  return {
    pnlUSD,
    pnlPercent,
    formattedPnL,
    formattedPercent,
    className
  };
};

/**
 * Update symbol precision mapping from Binance API
 */
export const updateSymbolPrecision = (symbol: string, precision: number): void => {
  SYMBOL_PRECISION_MAP[symbol] = precision;
};

/**
 * Bulk update symbol precision from exchange info
 */
export const updateSymbolPrecisionBulk = (symbolPrecisionData: Record<string, any>): void => {
  Object.entries(symbolPrecisionData).forEach(([symbol, data]) => {
    SYMBOL_PRECISION_MAP[symbol] = data.pricePrecision;
  });
  console.log(`✅ Updated precision for ${Object.keys(symbolPrecisionData).length} symbols`);
};

/**
 * Get precision for a symbol
 */
export const getSymbolPrecision = (symbol: string): number => {
  return SYMBOL_PRECISION_MAP[symbol] || SYMBOL_PRECISION_MAP['DEFAULT'];
};

/**
 * Fetch and update symbol precision from API
 */
export const fetchAndUpdateSymbolPrecision = async (): Promise<void> => {
  try {
    const response = await fetch('/api/market/exchange-info');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.symbolPrecision) {
        updateSymbolPrecisionBulk(data.symbolPrecision);
      }
    }
  } catch (error) {
    console.error('❌ Failed to fetch symbol precision:', error);
  }
};

/**
 * Format time duration
 */
export const formatTimeDuration = (timestamp: number): string => {
  const diff = Date.now() - timestamp;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  return `${minutes}m`;
};
