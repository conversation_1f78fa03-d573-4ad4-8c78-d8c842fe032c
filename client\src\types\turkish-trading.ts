// ParaBOT Türkçe Trading Types

export interface TicareKonfigurasyonu {
  kaldirac: number; // 20x sabit
  tpYuzdesi: number;
  slYuzdesi: number;
  maksPozisyon: number;
  maksGunlukIslem: number;
  islemMiktari: number; // USD cinsinden
  spikeEsigi: number;
  hacimCarpani: number;
  maPeriyodu: number; // MA ayarı (default 10)
  islemBaslatildi: boolean;
}

export interface SpikeSignali {
  id: string;
  sembol: string;
  zaman: number;
  fiyat: number;
  ma5: number;
  ma10: number;
  egim: number;
  hacim: number;
  ortalamHacim: number;
  guvenilirlik: 'DUSUK' | 'ORTA' | 'YUKSEK' | 'ONAYLANMIS_SPIKE';
  durum: 'TESPIT_EDILDI' | 'TICARET_YAPIYOR' | 'TAMAMLANDI' | 'BASARISIZ';
}

export interface Pozisyon {
  id: string;
  sembol: string;
  yon: 'LONG' | 'SHORT'; // LONG ve SHORT destekleniyor
  boyut: number;
  miktar: number; // Geriye uyumluluk için
  girisFiyati: number;
  guncelFiyat: number; // Geriye uyumluluk için
  markFiyati: number;
  pnl: number;
  pnlYuzdesi: number;
  kaldirac: number;
  tpFiyati?: number;
  slFiyati?: number;
  durum: 'ACIK' | 'KAPANIYOR' | 'KAPALI';
  acilisZamani: number;
  stopLoss?: number | null;
  takeProfit?: number | null;
}

export interface AcikEmir {
  id: string;
  sembol: string;
  tip: 'LIMIT' | 'STOP_LOSS' | 'TAKE_PROFIT' | 'MARKET';
  yon: 'LONG' | 'SHORT' | 'BUY' | 'SELL';
  miktar: number;
  fiyat: number;
  durum: 'BEKLIYOR' | 'KISMI_DOLU' | 'DOLU' | 'IPTAL' | 'NEW' | 'PARTIALLY_FILLED' | 'FILLED' | 'CANCELED' | 'EXPIRED';
  olusturmZamani: number;
  olusturulmaZamani: number; // Geriye uyumluluk için
  guncellemeZamani: number; // Geriye uyumluluk için
  pnlYuzdesi?: number;
}

export interface HesapBakiyesi {
  kullanilabilirBakiye: number;
  toplamBakiye: number;
  gerceklesmemisPnl: number;
  pozisyonMarjini: number;
  emirMarjini: number;
}

export interface PiyasaVerisi {
  sembol: string;
  fiyat: number;
  degisim24s: number;
  hacim24s: number;
  yuksek24s: number;
  dusuk24s: number;
  markFiyati: number;
  zaman: number;
}

export interface TicareIstatistikleri {
  toplamIslem: number;
  kazanmaOrani: number;
  toplamPnl: number;
  gunlukPnl: number;
  enIyiIslem: number;
  enKotuIslem: number;
  ortalamaTutmaSuresi: number;
  bugunIslemSayisi: number;
}

// YENİ: Spike Analiz Ayarları
export interface SpikeAnalysisSettings {
  spikeThreshold: number;
  priceThreshold: number;
  slopeThreshold: number;
  maPeriod: number;
  volumeMultiplier: number;
  minDataPoints: number;
  cooldownPeriod: number;
  minSlope: number;
  maxPositions: number;
  autoTrade: boolean;
  takeProfitPercent: number;
  stopLossPercent: number;
  tradeAmount: number;
  leverage: number;
}