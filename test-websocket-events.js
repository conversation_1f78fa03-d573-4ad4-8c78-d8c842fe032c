/**
 * WebSocket Events Test
 * Ana panel pozisyon ve emir g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> test eder
 */

import { io } from 'socket.io-client';

// Test konfigürasyonu
const TEST_CONFIG = {
  serverUrl: 'http://localhost:3001',
  username: 'test_user',
  timeout: 30000 // 30 saniye
};

// Test verileri
const mockPositionUpdate = {
  a: {
    P: [
      {
        s: 'BTCUSDT',
        pa: '0.001',      // position amount
        ep: '45000.00',   // entry price
        mp: '45500.00',   // mark price
        up: '0.50',       // unrealized PnL
        mt: 'cross',      // margin type
        iw: '0',          // isolated wallet
        ps: 'BOTH',       // position side
        l: '20'           // leverage
      },
      {
        s: 'ETHUSDT',
        pa: '-0.1',
        ep: '3000.00',
        mp: '2950.00',
        up: '5.00',
        mt: 'cross',
        iw: '0',
        ps: 'BOTH',
        l: '10'
      }
    ],
    B: [
      {
        a: 'USDT',
        wb: '1000.00',    // wallet balance
        cw: '950.00'      // cross wallet balance
      }
    ]
  }
};

const mockOrderUpdate = {
  o: {
    s: 'BTCUSDT',        // symbol
    i: 12345678,         // order id
    X: 'NEW',            // order status
    S: 'BUY',            // side
    o: 'LIMIT',          // order type
    q: '0.001',          // quantity
    p: '44000.00',       // price
    z: '0',              // executed quantity
    T: Date.now(),       // time
    ap: '0',             // average price
    n: '0'               // commission
  }
};

// Test fonksiyonu
async function testWebSocketEvents() {
  console.log('🧪 WebSocket Events Test Başlıyor...\n');
  
  try {
    // Socket.IO client oluştur
    const socket = io(TEST_CONFIG.serverUrl, {
      transports: ['websocket'],
      timeout: TEST_CONFIG.timeout
    });

    // Bağlantı event'leri
    socket.on('connect', () => {
      console.log('✅ Socket.IO bağlantısı kuruldu');
      console.log(`📡 Socket ID: ${socket.id}`);
      
      // Test kullanıcısı olarak authenticate ol
      socket.emit('authenticate', {
        username: TEST_CONFIG.username,
        token: 'test_token'
      });
    });

    socket.on('disconnect', () => {
      console.log('🔌 Socket.IO bağlantısı kesildi');
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Bağlantı hatası:', error.message);
    });

    // Test event listener'ları
    socket.on('balance-update', (data) => {
      console.log('💰 Balance Update Event:');
      console.log(`   Asset: ${data.asset}`);
      console.log(`   Balance: ${data.balance}`);
      console.log(`   Available: ${data.availableBalance}`);
      console.log(`   Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
      console.log('');
    });

    socket.on('positions-update', (data) => {
      console.log('📊 Positions Update Event:');
      console.log(`   Positions Count: ${data.positions.length}`);
      data.positions.forEach((pos, index) => {
        console.log(`   Position ${index + 1}:`);
        console.log(`     Symbol: ${pos.s}`);
        console.log(`     Amount: ${pos.pa}`);
        console.log(`     Entry Price: ${pos.ep}`);
        console.log(`     Mark Price: ${pos.mp}`);
        console.log(`     PnL: ${pos.up}`);
        console.log(`     Leverage: ${pos.l}`);
      });
      console.log(`   Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
      console.log('');
    });

    socket.on('position-update', (data) => {
      console.log('📈 Individual Position Update Event:');
      console.log(`   Symbol: ${data.symbol}`);
      console.log(`   Position Amount: ${data.positionAmt}`);
      console.log(`   Entry Price: ${data.entryPrice}`);
      console.log(`   Mark Price: ${data.markPrice}`);
      console.log(`   Unrealized PnL: ${data.unRealizedProfit}`);
      console.log(`   Leverage: ${data.leverage}`);
      console.log(`   Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
      console.log('');
    });

    socket.on('orders-update', (data) => {
      console.log('📋 Orders Update Event:');
      console.log(`   Orders Count: ${data.orders.length}`);
      data.orders.forEach((order, index) => {
        console.log(`   Order ${index + 1}:`);
        console.log(`     Symbol: ${order.s}`);
        console.log(`     Type: ${order.o}`);
        console.log(`     Side: ${order.S}`);
        console.log(`     Quantity: ${order.q}`);
        console.log(`     Price: ${order.p}`);
        console.log(`     Status: ${order.X}`);
      });
      console.log(`   Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
      console.log('');
    });

    socket.on('order-update', (data) => {
      console.log('📝 Individual Order Update Event:');
      console.log(`   Symbol: ${data.symbol}`);
      console.log(`   Order ID: ${data.orderId}`);
      console.log(`   Type: ${data.type}`);
      console.log(`   Side: ${data.side}`);
      console.log(`   Status: ${data.status}`);
      console.log(`   Quantity: ${data.quantity}`);
      console.log(`   Price: ${data.price}`);
      console.log(`   Executed Qty: ${data.executedQty}`);
      console.log(`   Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
      console.log('');
    });

    socket.on('position-closed', (data) => {
      console.log('🔒 Position Closed Event:');
      console.log(`   Symbol: ${data.symbol}`);
      console.log(`   Reason: ${data.reason}`);
      console.log(`   PnL: ${data.pnl?.netPnl || 'N/A'}`);
      console.log(`   Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
      console.log('');
    });

    // Test senaryoları
    setTimeout(() => {
      console.log('🔄 Test senaryoları başlatılıyor...\n');
      
      // 1. Account update simülasyonu
      console.log('📊 Account Update simülasyonu gönderiliyor...');
      socket.emit('test-account-update', {
        username: TEST_CONFIG.username,
        data: mockPositionUpdate
      });
      
    }, 2000);

    setTimeout(() => {
      // 2. Order update simülasyonu
      console.log('📋 Order Update simülasyonu gönderiliyor...');
      socket.emit('test-order-update', {
        username: TEST_CONFIG.username,
        data: mockOrderUpdate
      });
      
    }, 4000);

    setTimeout(() => {
      // 3. Position close simülasyonu
      console.log('🔒 Position Close simülasyonu gönderiliyor...');
      socket.emit('test-position-close', {
        username: TEST_CONFIG.username,
        symbol: 'BTCUSDT',
        reason: 'TAKE_PROFIT',
        pnl: { netPnl: 10.50 }
      });
      
    }, 6000);

    // Test sonlandırma
    setTimeout(() => {
      console.log('✅ Test tamamlandı');
      socket.disconnect();
      process.exit(0);
    }, 10000);

  } catch (error) {
    console.error('❌ Test Error:', error.message);
    process.exit(1);
  }
}

// Test çalıştır
console.log('🚀 WebSocket Events Test Starting...');
console.log(`📡 Server: ${TEST_CONFIG.serverUrl}`);
console.log(`👤 Test User: ${TEST_CONFIG.username}`);
console.log('');

testWebSocketEvents().catch((error) => {
  console.error('❌ Test failed:', error.message);
  process.exit(1);
});
